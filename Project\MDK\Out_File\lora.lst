C251 COMPILER V5.60.0,  lora                                                               22/07/25  06:03:23  PAGE 1   


C251 COMPILER V5.60.0, COMPILATION OF MODULE lora
OBJECT MODULE PLACED IN .\Out_File\lora.obj
COMPILER INVOKED BY: E:\Keil5C251\C251\BIN\C251.EXE ..\CODE\lora.c LARGE INTR2 FLOAT64 ROM(HUGE) WARNINGLEVEL(3) OPTIMIZ
                    -E(0,SPEED) BROWSE INCDIR(..\..\Libraries\libraries;..\..\Libraries\seekfree_libraries;..\..\Libraries\seekfree_periphera
                    -l;..\CODE;..\USER\inc;..\USER\src;..\..\Libraries\seekfree_components) DEBUG PRINT(.\Out_File\lora.lst) TABS(2) OBJECT(.
                    -\Out_File\lora.obj) 

stmt  level    source

    1          #include "lora.h"


Module Information          Static   Overlayable
------------------------------------------------
  code size            =    ------     ------
  ecode size           =    ------     ------
  data size            =    ------     ------
  idata size           =    ------     ------
  pdata size           =    ------     ------
  xdata size           =    ------     ------
  xdata-const size     =    ------     ------
  edata size           =    ------     ------
  bit size             =    ------     ------
  ebit size            =    ------     ------
  bitaddressable size  =    ------     ------
  ebitaddressable size =    ------     ------
  far data size        =    ------     ------
  huge data size       =    ------     ------
  const size           =    ------     ------
  hconst size          =    ------     ------
End of Module Information.


C251 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
