C251 COMPILER V5.60.0,  ccd_processing                                                     22/07/25  06:03:26  PAGE 1   


C251 COMPILER V5.60.0, COMPILATION OF MODULE ccd_processing
OBJECT MODULE PLACED IN .\Out_File\ccd_processing.obj
COMPILER INVOKED BY: E:\Keil5C251\C251\BIN\C251.EXE ..\CODE\ccd_processing.c LARGE INTR2 FLOAT64 ROM(HUGE) WARNINGLEVEL(
                    -3) OPTIMIZE(0,SPEED) BROWSE INCDIR(..\..\Libraries\libraries;..\..\Libraries\seekfree_libraries;..\..\Libraries\seekfree
                    -_peripheral;..\CODE;..\USER\inc;..\USER\src;..\..\Libraries\seekfree_components) DEBUG PRINT(.\Out_File\ccd_processing.l
                    -st) TABS(2) OBJECT(.\Out_File\ccd_processing.obj) 

stmt  level    source

    1          #include "ccd_processing.h"
    2          #include "math.h"
    3          #define MOVING_AVG_WINDOW  5       // 移动平均窗口大小
    4          #define MEDIAN_FILTER_SIZE 3       // 中值滤波窗口大小
    5          #define WHITE_THRESHOLD    100     // 白线阈值
    6          // 全局变量定义
    7          //uint8 ccd_raw_data[CCD_PIXEL_NUM] = {0};
    8          
    9          //曝光时间
   10          uint8 timer_count = 10;
   11          
   12          uint16 ccd_filtered_data[CCD_PIXEL_NUM] = {0};
   13          EdgeInfo edges[MAX_EDGE_NUM] = {0};
   14          uint8 edge_count = 0;
   15          int16 line_position_error = 0;
   16          
   17          
   18          void set_pit_timer_ms(TIMN_enum tim_n,uint16 time_ms)
   19          {
   20   1        uint16 temp;
   21   1        temp = (uint16)65536 - (uint16)(sys_clk / (12 * (1000 / time_ms)));
   22   1        
   23   1        if(TIM_0 == tim_n)
   24   1        {
   25   2          TL0 = temp;   
   26   2          TH0 = temp >> 8;
   27   2        }
   28   1        else if(TIM_1 == tim_n)
   29   1        {
   30   2          TL1 = temp;   
   31   2          TH1 = temp >> 8;
   32   2        }
   33   1        else if(TIM_2 == tim_n)
   34   1        {
   35   2          T2L = temp;   
   36   2          T2H = temp >> 8;
   37   2        }
   38   1        else if(TIM_3 == tim_n)
   39   1        {
   40   2          T3L = temp;   
   41   2          T3H = temp >> 8;
   42   2        }
   43   1        else if(TIM_4 == tim_n)
   44   1        {
   45   2          T4L = temp;   
   46   2          T4H = temp >> 8;
   47   2        }
   48   1      }
   49          
   50          
   51          
   52          
   53          
   54          
   55          // 移动平均滤波
   56          static void moving_average_filter(uint16 *input, uint16 *output, uint8 data_size) {
C251 COMPILER V5.60.0,  ccd_processing                                                     22/07/25  06:03:26  PAGE 2   

   57   1          uint8 i, j;
   58   1          uint16 sum;
   59   1          
   60   1          for (i = MOVING_AVG_WINDOW/2; i < data_size - MOVING_AVG_WINDOW/2; i++) {
   61   2              sum = 0;
   62   2              for (j = 0; j < MOVING_AVG_WINDOW; j++) {
   63   3                  sum += input[i - MOVING_AVG_WINDOW/2 + j];
   64   3              }
   65   2              output[i] = sum / MOVING_AVG_WINDOW;
   66   2          }
   67   1          
   68   1          // 处理边界
   69   1          for (i = 0; i < MOVING_AVG_WINDOW/2; i++) {
   70   2              output[i] = input[i];
   71   2              output[data_size - 1 - i] = input[data_size - 1 - i];
   72   2          }
   73   1      }
   74          
   75          // 中值滤波
   76          static uint8 median_filter(uint8 *window, uint8 data_size) {
   77   1          uint8 i, j, temp;
   78   1          
   79   1          // 简单冒泡排序
   80   1          for (i = 0; i < data_size - 1; i++) {
   81   2              for (j = i + 1; j < data_size; j++) {
   82   3                  if (window[j] < window[i]) {
   83   4                      temp = window[i];
   84   4                      window[i] = window[j];
   85   4                      window[j] = temp;
   86   4                  }
   87   3              }
   88   2          }
   89   1          
   90   1          return window[data_size/2];
   91   1      }
   92          
   93          // CCD数据初始化
   94          //void ccd_data_init(void) {
   95          //    memset(ccd_raw_data, 0, CCD_PIXEL_NUM);
   96          //    memset(ccd_filtered_data, 0, CCD_PIXEL_NUM);
   97          //    memset(edges, 0, sizeof(EdgeInfo) * MAX_EDGE_NUM);
   98          //    edge_count = 0;
   99          //    line_position_error = 0;
  100          //}
  101          
  102          // CCD数据滤波
  103          void ccd_data_filter(void) {
  104   1          //uint8 i;
  105   1          //uint8 window[MEDIAN_FILTER_SIZE];
  106   1          
  107   1          // 第一步：移动平均滤波
  108   1          moving_average_filter(ccd_data_ch1, ccd_filtered_data, CCD_PIXEL_NUM);
  109   1          
  110   1          // 第二步：中值滤波
  111   1      //    for (i = 1; i < CCD_PIXEL_NUM - 1; i++) {
  112   1      //        window[0] = ccd_filtered_data[i - 1];
  113   1      //        window[1] = ccd_filtered_data[i];
  114   1      //        window[2] = ccd_filtered_data[i + 1];
  115   1      //        ccd_filtered_data[i] = median_filter(window, MEDIAN_FILTER_SIZE);
  116   1      //    }
  117   1      }
  118          
  119          // 边缘检测
  120          // 新增函数：计算动态阈值
  121          static uint16 calculate_dynamic_threshold(void) {
  122   1          uint16 max_val = 0;
C251 COMPILER V5.60.0,  ccd_processing                                                     22/07/25  06:03:26  PAGE 3   

  123   1          uint16 min_val = 4095; // 12位ADC最大值
  124   1          uint8 i;
  125   1          
  126   1          for (i = VALID_PIXEL_START; i < VALID_PIXEL_END; i++) {
  127   2              if (ccd_filtered_data[i] > max_val) max_val = ccd_filtered_data[i];
  128   2              if (ccd_filtered_data[i] < min_val) min_val = ccd_filtered_data[i];
  129   2          }
  130   1          return min_val + (uint16)((max_val - min_val) * DYNAMIC_THRESHOLD_RATIO);
  131   1      }
  132          
  133          // 修改边缘检测函数
  134          void ccd_edge_detect(void) {
  135   1          uint8 i;
  136   1          uint8 in_edge = 0;
  137   1          uint8 current_edge = 0;
  138   1          uint16 dynamic_threshold = calculate_dynamic_threshold();
  139   1          
  140   1          edge_count = 0;
  141   1          
  142   1          for (i = VALID_PIXEL_START; i < VALID_PIXEL_END - SAMPLE_INTERVAL && edge_count < MAX_EDGE_NUM; i += 
             -SAMPLE_INTERVAL) {
  143   2              // 检测上升沿（黑到白）
  144   2              if (!in_edge && 
  145   2                  ccd_filtered_data[i] > dynamic_threshold && 
  146   2                  ccd_filtered_data[i-SAMPLE_INTERVAL] <= dynamic_threshold) {
  147   3                  edges[current_edge].left_edge = i;
  148   3                  edges[current_edge].max_value = ccd_filtered_data[i];
  149   3                  in_edge = 1;
  150   3              }
  151   2              
  152   2              // 检测下降沿（白到黑）
  153   2              if (in_edge && 
  154   2                  ccd_filtered_data[i] <= dynamic_threshold && 
  155   2                  ccd_filtered_data[i-SAMPLE_INTERVAL] > dynamic_threshold) {
  156   3                  edges[current_edge].right_edge = i - 1;
  157   3                  edges[current_edge].width = edges[current_edge].right_edge - edges[current_edge].left_edge;
  158   3                  in_edge = 0;
  159   3                  current_edge++;
  160   3                  edge_count++;
  161   3              }
  162   2              
  163   2              // 更新边缘区域最大值
  164   2              if (in_edge && ccd_filtered_data[i] > edges[current_edge].max_value) {
  165   3                  edges[current_edge].max_value = ccd_filtered_data[i];
  166   3              }
  167   2          }
  168   1      }
  169          
  170          // 修改有效线判断函数
  171          uint8 ccd_is_line_valid(void) {
  172   1          uint8 continuous_count = 0;
  173   1          uint8 max_continuous = 0;
  174   1          uint16 left_contrast;
  175   1          uint16 right_contrast;
  176   1          uint8 i;
  177   1          uint16 dynamic_threshold = calculate_dynamic_threshold();
  178   1          if (edge_count > 0) {
  179   2              // 1. 基本宽度检查
  180   2              if (edges[0].width < MIN_EXPECTED_WIDTH || edges[0].width > MAX_EXPECTED_WIDTH) {
  181   3                  return 0;
  182   3              }
  183   2      
  184   2              // 2. 边缘对比度检查
  185   2              //uint16 left_contrast = edges[0].max_value - ccd_filtered_data[edges[0].left_edge-1];
  186   2              //uint16 right_contrast = edges[0].max_value - ccd_filtered_data[edges[0].right_edge+1];
  187   2          left_contrast = edges[0].max_value - ccd_filtered_data[edges[0].left_edge-1];
C251 COMPILER V5.60.0,  ccd_processing                                                     22/07/25  06:03:26  PAGE 4   

  188   2              right_contrast = edges[0].max_value - ccd_filtered_data[edges[0].right_edge+1];
  189   2              if (left_contrast < CONTRAST_THRESHOLD || right_contrast < CONTRAST_THRESHOLD) {
  190   3                  return 0;
  191   3              }
  192   2      
  193   2              // 3. 连续性检查（新增）
  194   2              //uint8 continuous_count = 0;
  195   2              //uint8 max_continuous = 0;
  196   2              //for (uint8 i = edges[0].left_edge; i <= edges[0].right_edge; i++) {
  197   2              for (i = edges[0].left_edge; i <= edges[0].right_edge; i++) {
  198   3                  if (ccd_filtered_data[i] > dynamic_threshold) {
  199   4                      continuous_count++;
  200   4                      if (continuous_count > max_continuous) {
  201   5                          max_continuous = continuous_count;
  202   5                      }
  203   4                  } else {
  204   4                      continuous_count = 0;
  205   4                  }
  206   3              }
  207   2              
  208   2              // 连续性阈值判断
  209   2              if (max_continuous < CONTINUITY_THRESHOLD) {
  210   3                  return 0;
  211   3              }
  212   2      
  213   2              return 1;
  214   2          }
  215   1          return 0;
  216   1      }
  217          
  218          // 计算位置误差，负号左转，正号右转
  219          int16 ccd_calc_position_error(void) {
  220   1          int32 sum = 0;
  221   1          int32 weight_sum = 0;
  222   1          uint8 i;
  223   1        int16 weight;
  224   1      
  225   1          int16 center;
  226   1          
  227   1          if (!ccd_is_line_valid()) {
  228   2              return 0;
  229   2          }
  230   1          
  231   1          // 使用加权平均计算中心位置
  232   1          for (i = edges[0].left_edge; i <= edges[0].right_edge; i++) {
  233   2              //int16 weight = ccd_filtered_data[i] - WHITE_THRESHOLD;
  234   2              weight = ccd_filtered_data[i] - WHITE_THRESHOLD;
  235   2              if (weight > 0) {
  236   3                  sum += (i - CCD_MIDDLE_POS) * weight;
  237   3                  weight_sum += weight;
  238   3              }
  239   2          }
  240   1          
  241   1          if (weight_sum != 0) {
  242   2              center = sum / weight_sum;
  243   2          } else {
  244   2              center = (edges[0].left_edge + edges[0].right_edge) / 2 - CCD_MIDDLE_POS;
  245   2          }
  246   1          
  247   1          line_position_error = center;
  248   1          return line_position_error;
  249   1      }
  250          
  251          
  252          // 计算位置误差，负号左转，正号右转
  253          int16 ccd_calc_position_error_simple(void) {
C251 COMPILER V5.60.0,  ccd_processing                                                     22/07/25  06:03:26  PAGE 5   

  254   1      
  255   1          int16 center;
  256   1      
  257   1              center = (edges[0].left_edge + edges[0].right_edge) / 2 - CCD_MIDDLE_POS;
  258   1          
  259   1          line_position_error = center;
  260   1          return line_position_error;
  261   1      }
*** WARNING C174 IN LINE 76 OF ..\CODE\ccd_processing.c: 'median_filter': unreferenced 'static' function


Module Information          Static   Overlayable
------------------------------------------------
  code size            =    ------     ------
  ecode size           =      2340     ------
  data size            =    ------     ------
  idata size           =    ------     ------
  pdata size           =    ------     ------
  xdata size           =       331     ------
  xdata-const size     =    ------     ------
  edata size           =    ------     ------
  bit size             =    ------     ------
  ebit size            =    ------     ------
  bitaddressable size  =    ------     ------
  ebitaddressable size =    ------     ------
  far data size        =    ------     ------
  huge data size       =    ------     ------
  const size           =    ------     ------
  hconst size          =       295     ------
End of Module Information.


C251 COMPILATION COMPLETE.  1 WARNING(S),  0 ERROR(S)
