#ifndef __KEY_H__
#define __KEY_H__
#include "common.h"
//定义按键引脚
#define key1_state    P70
#define key2_state    P71
#define key3_state    P72
#define key4_state    P73
//定义拨码开关引脚
#define SW1_PIN     P75
#define SW2_PIN     P76

extern int16 key_num;
extern int16 if_take_point_key;
//按键枚举声明
typedef enum
{
		key_none = 0,   // 没有按键按下
    key1_pres = 1,  // 按键1按下
    key2_pres = 2,  // 按键2按下
    key3_pres = 3,  // 按键3按下
    key4_pres = 4   // 按键4按下
} key_enum;
//按键扫描函数声明
key_enum key_scan(int mode);

#endif
