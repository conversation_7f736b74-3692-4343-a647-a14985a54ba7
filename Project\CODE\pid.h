#ifndef __PID_H__
#define __PID_H__
//#include "headfile.h"
#include "common.h"
//传感器相关全局变量
extern float ctimer_speed; //编码器速度

extern float kp_angle;
extern float ki_angle;
extern float kd_angle;

extern float kp_speed;
extern float ki_speed;
extern float kd_speed;

extern float kp_rate;
extern float ki_rate;
extern float kd_rate;

extern float kp_ccd; // CCD角度环比例系数
extern float ki_ccd; // CCD角度环积分系数   
extern float kd_ccd; // CCD角度环微分系数

//目标值
extern float target_rate;
extern float target_angle;
extern float target_speed;

float pid_angle(float target_angle, float current_angle);
float pid_rate(float target_rate, float current_rate);
float pid_speed(float target_speed, float current_speed);
float pid_ccd(float target_ccd, float current_ccd);

extern float rate_error_sum;
extern float angle_error_sum;
extern float speed_error_sum;



extern float speed_out;
extern float speed_side_out;
extern float speed_ccd_out;


//pid中间量
extern float rate_integral_sum ;




extern uint8 time1;
#endif