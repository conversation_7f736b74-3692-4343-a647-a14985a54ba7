L251 LINKER/LOCATER V4.66.93.0                                                          07/23/2025  07:24:14  PAGE 1


L251 LINKER/LOCATER V4.66.93.0, INVOKED BY:
E:\KEIL5C251\C251\BIN\L251.EXE .\Out_File\board.obj, .\Out_File\common.obj, .\Out_File\START251.obj, .\Out_File\zf_adc.o
>> bj, .\Out_File\zf_delay.obj, .\Out_File\zf_eeprom.obj, .\Out_File\zf_gpio.obj, .\Out_File\zf_nvic.obj, .\Out_File\zf_
>> pwm.obj, .\Out_File\zf_spi.obj, .\Out_File\zf_tim.obj, .\Out_File\zf_uart.obj, .\Out_File\zf_fifo.obj, .\Out_File\zf_
>> function.obj, ..\..\Libraries\seekfree_peripheral\SEEKFREE_CONFIG.LIB, .\Out_File\SEEKFREE_FONT.obj, .\Out_File\SEEKF
>> REE_IMU660RA.obj, .\Out_File\SEEKFREE_TSL1401.obj, .\Out_File\SEEKFREE_GPS_TAU1201.obj, .\Out_File\SEEKFREE_IPS200_SP
>> I.obj, .\Out_File\isr.obj, .\Out_File\main.obj, .\Out_File\beep.obj, .\Out_File\config.obj, .\Out_File\key.obj, .\Out
>> _File\lora.obj, .\Out_File\motor.obj, .\Out_File\my_atan2.obj, .\Out_File\pid.obj, .\Out_File\point_processing.obj, .
>> \Out_File\quaternion_pose_calculating.obj, .\Out_File\exception_handling.obj, .\Out_File\task.obj, .\Out_File\ccd_pro
>> cessing.obj, .\Out_File\menu.obj TO .\Out_File\SEEKFREE PRINT (.\Out_File\SEEKFREE.map) CASE DISABLEWARNING (15, 16, 
>> 57) REMOVEUNUSED CLASSES (EDATA (0X0-0XFFF), HDATA (0X0-0XFFF), HCONST (0XFE0C00-0XFFFFFF), ECODE (0XFE0C00-0XFFFFFF)
>> )


CPU MODE:     251 SOURCE MODE
INTR FRAME:   2 BYTES SAVED ON INTERRUPT
MEMORY MODEL: LARGE WITH DOUBLE PRECISION FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Out_File\board.obj (board)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\common.obj (common)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\START251.obj (?C_START?)
         COMMENT TYPE 0: A251 V4.69.13.0
  .\Out_File\zf_adc.obj (zf_adc)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_delay.obj (zf_delay)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_eeprom.obj (zf_eeprom)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_gpio.obj (zf_gpio)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_nvic.obj (zf_nvic)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_pwm.obj (zf_pwm)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_spi.obj (zf_spi)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_tim.obj (zf_tim)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_uart.obj (zf_uart)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_fifo.obj (zf_fifo)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\zf_function.obj (zf_function)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\SEEKFREE_FONT.obj (SEEKFREE_FONT)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\SEEKFREE_IMU660RA.obj (SEEKFREE_IMU660RA)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\SEEKFREE_TSL1401.obj (SEEKFREE_TSL1401)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\SEEKFREE_GPS_TAU1201.obj (SEEKFREE_GPS_TAU1201)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\SEEKFREE_IPS200_SPI.obj (SEEKFREE_IPS200_SPI)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\isr.obj (isr)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\main.obj (main)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\beep.obj (beep)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\config.obj (config)
         COMMENT TYPE 0: C251 V5.60.0
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 2


  .\Out_File\key.obj (key)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\lora.obj (lora)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\motor.obj (motor)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\my_atan2.obj (my_atan2)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\pid.obj (pid)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\point_processing.obj (point_processing)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\quaternion_pose_calculating.obj (quaternion_pose_calculating)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\exception_handling.obj (exception_handling)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\task.obj (task)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\ccd_processing.obj (ccd_processing)
         COMMENT TYPE 0: C251 V5.60.0
  .\Out_File\menu.obj (menu)
         COMMENT TYPE 0: C251 V5.60.0
  ..\..\Libraries\seekfree_peripheral\SEEKFREE_CONFIG.LIB (SEEKFREE_CONFIG)
         COMMENT TYPE 0: C251 V5.60.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPADD?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPMUL?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPDIV?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPCMP?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPNEG?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DCAST?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?CASTD?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DCASTF?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?FCASTD?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (PRINTF?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (SPRINTF?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (FABS?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (SQRT??)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (POW??)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (SIN??)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (ATAN??)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (ASIN??)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (FLOOR??)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPGETOPN?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (_CHKDOUBLE_??)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?PRNFMT?)
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 3


         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPSERIES?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (EXP??)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (LOG??)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (MODF??)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DPCONVERT?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SDPL.LIB (?C?DTNPWR?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?FPGETOPN?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?FPADD?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?FPMUL?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?FPDIV?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?FPCMP?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?FPNEG?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?FCAST?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SFPL.LIB (?C?CASTF?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?INITHDATA)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?SIDIV?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?LMUL?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?LIMUL?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?ULDIV?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?SLDIV?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?BMOVEPP?)
         COMMENT TYPE 0: A251 V4.69.6.0
  E:\KEIL5C251\C251\LIB\C2SL.LIB (STRNCMP?)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SL.LIB (STRNCPY?)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SL.LIB (STRLEN?)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SL.LIB (STRCHR?)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SL.LIB (MEMCPY?)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SL.LIB (MEMSET?)
         COMMENT TYPE 0: C251 V5.58.7
  E:\KEIL5C251\C251\LIB\C2SL.LIB (?C?INITHDATA_END)
         COMMENT TYPE 0: A251 V4.69.6.0


ACTIVE MEMORY CLASSES OF MODULE:  .\Out_File\SEEKFREE (board)

BASE        START       END         USED      MEMORY CLASS
==========================================================
000000H     000000H     000FFFH     000A16H   EDATA
000000H     000000H     000FFFH               HDATA
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 4


000000H     FE0C00H     FFFFFFH     0008CEH   HCONST
000000H     FE0C00H     FFFFFFH     011378H   ECODE
010000H     010000H     01FFFFH     000F4AH   XDATA
FF0000H     FF0000H     FFFFFFH     002708H   CODE
000000H     000000H     00007FH     000008H   DATA


MEMORY MAP OF MODULE:  .\Out_File\SEEKFREE (board)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000208H   000201H   BYTE   UNIT     EDATA          ?ED?SEEKFREE_TSL1401
000209H   00020FH   000007H   BYTE   UNIT     EDATA          ?ED?IMU660RA_GET_ACC??SEEKFREE_IMU660RA
000210H   000216H   000007H   BYTE   UNIT     EDATA          ?ED?IMU660RA_GET_GYRO??SEEKFREE_IMU660RA
000217H   000219H   000003H   BYTE   UNIT     EDATA          ?ED?IMU660RA_SELF_CHECK?SEEKFREE_IMU660RA
00021AH   00021BH   000002H   BYTE   UNIT     EDATA          ?ED?IMU660RA_READ_REGISTER?SEEKFREE_IMU660RA
00021CH   00021CH   000001H   BYTE   UNIT     EDATA          ?ED?IMU660RA_SIMSPI_WR_BYTE?SEEKFREE_IMU660RA
00021DH   00021DH   000001H   BYTE   UNIT     EDATA          ?ED?IMU660RA_INIT??SEEKFREE_IMU660RA
00021EH   000A1DH   000800H   BYTE   UNIT     EDATA          ?STACK
000A1EH   00FFFFH   00F5E2H   ---    ---      **GAP**
010000H   010259H   00025AH   BYTE   INSEG    XDATA          ?XD?POINT_PROCESSING
01025AH   010429H   0001D0H   BYTE   INSEG    XDATA          ?XD?SEEKFREE_GPS_TAU1201
01042AH   010537H   00010EH   BYTE   INSEG    XDATA          ?XD?CCD_PROCESSING
010538H   0105E0H   0000A9H   BYTE   INSEG    XDATA          ?XD?MAIN
0105E1H   010686H   0000A6H   BYTE   INSEG    XDATA          ?XD?PID
010687H   01071BH   000095H   BYTE   UNIT     XDATA          ?XD?GPS_INIT??SEEKFREE_GPS_TAU1201
01071CH   010785H   00006AH   BYTE   UNIT     XDATA          ?XD?FUNC_DOUBLE_TO_STR??ZF_FUNCTION
010786H   0107E5H   000060H   BYTE   UNIT     XDATA          ?XD?GET_TWO_POINTS_DISTANCE??SEEKFREE_GPS_TAU1201
0107E6H   010841H   00005CH   BYTE   UNIT     XDATA          ?XD?QUATERNION_UPDATE??QUATERNION_POSE_CALCULATING
010842H   01088BH   00004AH   BYTE   UNIT     XDATA          ?XD?FUNC_FLOAT_TO_STR??ZF_FUNCTION
01088CH   0108D2H   000047H   BYTE   UNIT     XDATA          ?XD?PATH_DISPLAY_PAGE_FUN??MENU
0108D3H   010915H   000043H   BYTE   UNIT     XDATA          ?XD?GPS_GNRMC_PARSE?SEEKFREE_GPS_TAU1201
010916H   01094DH   000038H   BYTE   UNIT     XDATA          ?XD?GET_TWO_POINTS_AZIMUTH??SEEKFREE_GPS_TAU1201
01094EH   010985H   000038H   BYTE   INSEG    XDATA          ?XD?QUATERNION_POSE_CALCULATING
010986H   0109B9H   000034H   BYTE   INSEG    XDATA          ?XD?TASK
0109BAH   0109ECH   000033H   BYTE   INSEG    XDATA          ?XD?CONFIG
0109EDH   010A1BH   00002FH   BYTE   UNIT     XDATA          ?XD?IPS200_SHOW_FLOAT??SEEKFREE_IPS200_SPI
010A1CH   010A43H   000028H   BYTE   UNIT     XDATA          _XDATA_GROUP_
010A44H   010A69H   000026H   BYTE   UNIT     XDATA          ?XD?CHECK_CROSS_BORDER??EXCEPTION_HANDLING
010A6AH   010A8EH   000025H   BYTE   UNIT     XDATA          ?XD?FUNC_HEX_TO_STR??ZF_FUNCTION
010A8FH   010AAEH   000020H   BYTE   UNIT     XDATA          ?XD?IPS200_SHOW_WAVE??SEEKFREE_IPS200_SPI
010AAFH   010ACBH   00001DH   BYTE   UNIT     XDATA          ?XD?FUNC_INT_TO_STR??ZF_FUNCTION
010ACCH   010AE8H   00001DH   BYTE   UNIT     XDATA          ?XD?FUNC_STR_TO_DOUBLE??ZF_FUNCTION
010AE9H   010B05H   00001DH   BYTE   UNIT     XDATA          ?XD?IPS200_SHOW_INT??SEEKFREE_IPS200_SPI
010B06H   010B22H   00001DH   BYTE   UNIT     XDATA          ?XD?IPS200_SHOW_UINT??SEEKFREE_IPS200_SPI
010B23H   010B3EH   00001CH   BYTE   UNIT     XDATA          ?XD?GET_DOUBLE_NUMBER?SEEKFREE_GPS_TAU1201
010B3FH   010B57H   000019H   BYTE   UNIT     XDATA          ?XD?FUNC_UINT_TO_STR??ZF_FUNCTION
010B58H   010B6FH   000018H   BYTE   UNIT     XDATA          ?XD?FIFO_READ_BUFFER??ZF_FIFO
010B70H   010B87H   000018H   BYTE   UNIT     XDATA          ?XD?FIFO_READ_TAIL_BUFFER??ZF_FIFO
010B88H   010B9FH   000018H   BYTE   UNIT     XDATA          ?XD?GET_FLOAT_NUMBER?SEEKFREE_GPS_TAU1201
010BA0H   010BB7H   000018H   BYTE   UNIT     XDATA          ?XD?MY_ATAN2??MY_ATAN2
010BB8H   010BCDH   000016H   BYTE   UNIT     XDATA          ?XD?IPS200_DRAW_LINE??SEEKFREE_IPS200_SPI
010BCEH   010BE1H   000014H   BYTE   UNIT     XDATA          ?XD?PWM_INIT??ZF_PWM
010BE2H   010BF5H   000014H   BYTE   UNIT     XDATA          ?XD?PWM_FREQ??ZF_PWM
010BF6H   010C09H   000014H   BYTE   UNIT     XDATA          ?XD?GET_EULER_ANGLES??QUATERNION_POSE_CALCULATING
010C0AH   010C1BH   000012H   BYTE   UNIT     XDATA          ?XD?FIFO_WRITE_BUFFER??ZF_FIFO
010C1CH   010C2DH   000012H   BYTE   INSEG    XDATA          ?XD?MENU
010C2EH   010C3EH   000011H   BYTE   UNIT     XDATA          ?XD?FUNC_STR_TO_FLOAT??ZF_FUNCTION
010C3FH   010C4FH   000011H   BYTE   UNIT     XDATA          ?XD?GET_INT_NUMBER?SEEKFREE_GPS_TAU1201
010C50H   010C60H   000011H   BYTE   UNIT     XDATA          ?XD?DOUBLE_TO_BYTES??POINT_PROCESSING
010C61H   010C70H   000010H   BYTE   UNIT     XDATA          ?XD?FIFO_INIT??ZF_FIFO
010C71H   010C80H   000010H   BYTE   UNIT     XDATA          ?XD?TAKE_POINT??POINT_PROCESSING
010C81H   010C90H   000010H   BYTE   UNIT     XDATA          ?XD?EEPROM_WRITE_DOUBLE??POINT_PROCESSING
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 5


010C91H   010C9EH   00000EH   BYTE   UNIT     XDATA          ?XD?PWM_DUTY??ZF_PWM
010C9FH   010CACH   00000EH   BYTE   UNIT     XDATA          ?XD?UART_INIT??ZF_UART
010CADH   010CBAH   00000EH   BYTE   UNIT     XDATA          ?XD?FUNC_STR_TO_HEX??ZF_FUNCTION
010CBBH   010CC8H   00000EH   BYTE   UNIT     XDATA          ?XD?GPS_GNGGA_PARSE?SEEKFREE_GPS_TAU1201
010CC9H   010CD6H   00000EH   BYTE   INSEG    XDATA          ?XD?SEEKFREE_IPS200_SPI
010CD7H   010CE3H   00000DH   BYTE   UNIT     XDATA          ?XD?SPI_INIT??ZF_SPI
010CE4H   010CF0H   00000DH   BYTE   UNIT     XDATA          ?XD?GET_PARAMETER_INDEX?SEEKFREE_GPS_TAU1201
010CF1H   010CFDH   00000DH   BYTE   UNIT     XDATA          ?XD?BYTES_TO_DOUBLE??POINT_PROCESSING
010CFEH   010D0AH   00000DH   BYTE   UNIT     XDATA          ?XD?MOVING_AVERAGE_FILTER?CCD_PROCESSING
010D0BH   010D17H   00000DH   BYTE   UNIT     XDATA          ?XD?CCD_CALC_POSITION_ERROR??CCD_PROCESSING
010D18H   010D23H   00000CH   BYTE   UNIT     XDATA          ?XD?FIFO_READ_ELEMENT??ZF_FIFO
010D24H   010D2FH   00000CH   BYTE   INSEG    XDATA          ?XD?SEEKFREE_IMU660RA
010D30H   010D3BH   00000CH   BYTE   UNIT     XDATA          ?XD?GPS_DATA_PARSE??SEEKFREE_GPS_TAU1201
010D3CH   010D47H   00000CH   BYTE   UNIT     XDATA          ?XD?EEPROM_READ_DOUBLE??POINT_PROCESSING
010D48H   010D53H   00000CH   BYTE   UNIT     XDATA          ?XD?GET_POINT_ERROR??POINT_PROCESSING
010D54H   010D5FH   00000CH   BYTE   UNIT     XDATA          ?XD?CORRECT_POINT??POINT_PROCESSING
010D60H   010D6BH   00000CH   BYTE   UNIT     XDATA          ?XD?LOW_PASS_FILTER??QUATERNION_POSE_CALCULATING
010D6CH   010D76H   00000BH   BYTE   UNIT     XDATA          ?XD?GPS_UART_CALLBACK??SEEKFREE_GPS_TAU1201
010D77H   010D80H   00000AH   BYTE   UNIT     XDATA          ?XD?IAP_READ_BYTES??ZF_EEPROM
010D81H   010D8AH   00000AH   BYTE   UNIT     XDATA          ?XD?IAP_WRITE_BYTES??ZF_EEPROM
010D8BH   010D94H   00000AH   BYTE   UNIT     XDATA          ?XD?UART_PUTBUFF??ZF_UART
010D95H   010D9EH   00000AH   BYTE   UNIT     XDATA          ?XD?FIFO_WRITE_ELEMENT??ZF_FIFO
010D9FH   010DA8H   00000AH   BYTE   UNIT     XDATA          ?XD?IPS200_SHOW_STRING??SEEKFREE_IPS200_SPI
010DA9H   010DB2H   00000AH   BYTE   UNIT     XDATA          ?XD?SPEED_SET??MOTOR
010DB3H   010DBBH   000009H   BYTE   UNIT     XDATA          ?XD?FUNC_STR_TO_INT??ZF_FUNCTION
010DBCH   010DC4H   000009H   BYTE   UNIT     XDATA          ?XD?IMU660RA_SIMSPI_W_REG_BYTES?SEEKFREE_IMU660RA
010DC5H   010DCDH   000009H   BYTE   UNIT     XDATA          ?XD?IMU660RA_SIMSPI_R_REG_BYTES?SEEKFREE_IMU660RA
010DCEH   010DD6H   000009H   BYTE   UNIT     XDATA          ?XD?IMU660RA_WRITE_REGISTERS?SEEKFREE_IMU660RA
010DD7H   010DDFH   000009H   BYTE   UNIT     XDATA          ?XD?IMU660RA_READ_REGISTERS?SEEKFREE_IMU660RA
010DE0H   010DE8H   000009H   BYTE   UNIT     XDATA          ?XD?IPS200_SHOW_CHAR??SEEKFREE_IPS200_SPI
010DE9H   010DF1H   000009H   BYTE   UNIT     XDATA          ?XD?CCD_IS_LINE_VALID??CCD_PROCESSING
010DF2H   010DF9H   000008H   BYTE   UNIT     XDATA          ?XD?FIFO_HEAD_OFFSET?ZF_FIFO
010DFAH   010E01H   000008H   BYTE   UNIT     XDATA          ?XD?FIFO_END_OFFSET?ZF_FIFO
010E02H   010E09H   000008H   BYTE   UNIT     XDATA          ?XD?FUNC_GET_GREATEST_COMMON_DIVISOR??ZF_FUNCTION
010E0AH   010E11H   000008H   BYTE   UNIT     XDATA          ?XD?FUNC_STR_TO_UINT??ZF_FUNCTION
010E12H   010E19H   000008H   BYTE   UNIT     XDATA          ?XD?IPS200_SET_REGION?SEEKFREE_IPS200_SPI
010E1AH   010E21H   000008H   BYTE   UNIT     XDATA          ?XD?PID_ANGLE??PID
010E22H   010E29H   000008H   BYTE   UNIT     XDATA          ?XD?PID_RATE??PID
010E2AH   010E31H   000008H   BYTE   UNIT     XDATA          ?XD?PID_SPEED??PID
010E32H   010E39H   000008H   BYTE   UNIT     XDATA          ?XD?PID_CCD??PID
010E3AH   010E41H   000008H   BYTE   UNIT     XDATA          ?XD?QUATERNION_NORMALIZE?QUATERNION_POSE_CALCULATING
010E42H   010E49H   000008H   BYTE   UNIT     XDATA          ?XD?MEDIAN_FILTER?CCD_PROCESSING
010E4AH   010E50H   000007H   BYTE   UNIT     XDATA          ?XD?CCD_SEND_DATA??SEEKFREE_TSL1401
010E51H   010E56H   000006H   BYTE   INSEG    XDATA          ?XD?COMMON
010E57H   010E5CH   000006H   BYTE   UNIT     XDATA          ?XD?ADC_ONCE??ZF_ADC
010E5DH   010E62H   000006H   BYTE   UNIT     XDATA          ?XD?DELAY_US??ZF_DELAY
010E63H   010E68H   000006H   BYTE   UNIT     XDATA          ?XD?PIT_TIMER_MS??ZF_TIM
010E69H   010E6EH   000006H   BYTE   UNIT     XDATA          ?XD?UART_PUTSTR??ZF_UART
010E6FH   010E74H   000006H   BYTE   UNIT     XDATA          ?XD?FIFO_CLEAR??ZF_FIFO
010E75H   010E7AH   000006H   BYTE   UNIT     XDATA          ?XD?IMU660RA_ACC_TRANSITION??SEEKFREE_IMU660RA
010E7BH   010E80H   000006H   BYTE   UNIT     XDATA          ?XD?IMU660RA_GYRO_TRANSITION??SEEKFREE_IMU660RA
010E81H   010E86H   000006H   BYTE   UNIT     XDATA          ?XD?IPS200_FULL??SEEKFREE_IPS200_SPI
010E87H   010E8CH   000006H   BYTE   UNIT     XDATA          ?XD?IPS200_DRAW_POINT??SEEKFREE_IPS200_SPI
010E8DH   010E92H   000006H   BYTE   UNIT     XDATA          ?XD?SET_PIT_TIMER_MS??CCD_PROCESSING
010E93H   010E98H   000006H   BYTE   UNIT     XDATA          ?XD?DRAW_MARKER?MENU
010E99H   010E9DH   000005H   BYTE   INSEG    XDATA          ?XD?ZF_UART
010E9EH   010EA2H   000005H   BYTE   UNIT     XDATA          ?XD?UTC_TO_BTC?SEEKFREE_GPS_TAU1201
010EA3H   010EA7H   000005H   BYTE   INSEG    XDATA          ?XD?KEY
010EA8H   010EACH   000005H   BYTE   UNIT     XDATA          ?XD?BARRICADE_FUNCTION??TASK
010EADH   010EB1H   000005H   BYTE   UNIT     XDATA          ?XD?CALCULATE_DYNAMIC_THRESHOLD?CCD_PROCESSING
010EB2H   010EB6H   000005H   BYTE   UNIT     XDATA          ?XD?CCD_EDGE_DETECT??CCD_PROCESSING
010EB7H   010EBAH   000004H   BYTE   INSEG    XDATA          ?XD?BOARD
010EBBH   010EBEH   000004H   BYTE   UNIT     XDATA          ?XD?ADC_INIT??ZF_ADC
010EBFH   010EC2H   000004H   BYTE   INSEG    XDATA          ?XD?ZF_DELAY
010EC3H   010EC6H   000004H   BYTE   UNIT     XDATA          ?XD?DELAY_MS??ZF_DELAY
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 6


010EC7H   010ECAH   000004H   BYTE   UNIT     XDATA          ?XD?IAP_ERASE_PAGE??ZF_EEPROM
010ECBH   010ECEH   000004H   BYTE   UNIT     XDATA          ?XD?GPIO_PULL_SET??ZF_GPIO
010ECFH   010ED2H   000004H   BYTE   UNIT     XDATA          ?XD?GPIO_MODE??ZF_GPIO
010ED3H   010ED6H   000004H   BYTE   UNIT     XDATA          ?XD?CTIMER_COUNT_READ??ZF_TIM
010ED7H   010EDAH   000004H   BYTE   UNIT     XDATA          ?XD?FIFO_USED??ZF_FIFO
010EDBH   010EDEH   000004H   BYTE   UNIT     XDATA          ?XD?FUNC_SOFT_DELAY??ZF_FUNCTION
010EDFH   010EE2H   000004H   BYTE   UNIT     XDATA          ?XD?IPS200_WRITE_16BIT_DATA_SPI??SEEKFREE_IPS200_SPI
010EE3H   010EE6H   000004H   BYTE   UNIT     XDATA          ?XD?IPS200_CLEAR??SEEKFREE_IPS200_SPI
010EE7H   010EEAH   000004H   BYTE   UNIT     XDATA          ?XD?IPS200_SET_COLOR??SEEKFREE_IPS200_SPI
010EEBH   010EEEH   000004H   BYTE   UNIT     XDATA          ?XD?TIM3_PIT_HANLDER??MAIN
010EEFH   010EF2H   000004H   BYTE   UNIT     XDATA          ?XD?TAIL_LEFT_MOTOR_SET_SPEED??MOTOR
010EF3H   010EF6H   000004H   BYTE   UNIT     XDATA          ?XD?TAIL_RIGHT_MOTOR_SET_SPEED??MOTOR
010EF7H   010EFAH   000004H   BYTE   UNIT     XDATA          ?XD?SIDE_LEFT_MOTOR_SET_SPEED??MOTOR
010EFBH   010EFEH   000004H   BYTE   UNIT     XDATA          ?XD?SIDE_RIGHT_MOTOR_SET_SPEED??MOTOR
010EFFH   010F02H   000004H   BYTE   UNIT     XDATA          ?XD?GET_QUATERNION??QUATERNION_POSE_CALCULATING
010F03H   010F06H   000004H   BYTE   UNIT     XDATA          ?XD?GRASS_FUNCTION??TASK
010F07H   010F0AH   000004H   BYTE   UNIT     XDATA          ?XD?BUMPY_FUNCTION??TASK
010F0BH   010F0EH   000004H   BYTE   UNIT     XDATA          ?XD?RAMP_FUNCTION??TASK
010F0FH   010F12H   000004H   BYTE   UNIT     XDATA          ?XD?ARCH_FUNCTION??TASK
010F13H   010F16H   000004H   BYTE   UNIT     XDATA          ?XD?TURN_FUNCTION??TASK
010F17H   010F19H   000003H   BYTE   UNIT     XDATA          ?XD?NVIC_SETPRIORITY??ZF_NVIC
010F1AH   010F1CH   000003H   BYTE   UNIT     XDATA          ?XD?UART_PUTCHAR??ZF_UART
010F1DH   010F1EH   000002H   BYTE   UNIT     XDATA          ?XD?PWM_SET_GPIO??ZF_PWM
010F1FH   010F20H   000002H   BYTE   UNIT     XDATA          ?XD?SPI_CHANGE_PIN??ZF_SPI
010F21H   010F22H   000002H   BYTE   UNIT     XDATA          ?XD?CTIMER_COUNT_INIT??ZF_TIM
010F23H   010F24H   000002H   BYTE   UNIT     XDATA          ?XD?CTIMER_COUNT_CLEAN??ZF_TIM
010F25H   010F26H   000002H   BYTE   UNIT     XDATA          ?XD?IMU660RA_SIMSPI_W_REG_BYTE?SEEKFREE_IMU660RA
010F27H   010F28H   000002H   BYTE   UNIT     XDATA          ?XD?IMU660RA_WRITE_REGISTER?SEEKFREE_IMU660RA
010F29H   010F2AH   000002H   BYTE   UNIT     XDATA          ?XD?IPS200_WRITE_16BIT_DATA?SEEKFREE_IPS200_SPI
010F2BH   010F2CH   000002H   BYTE   UNIT     XDATA          ?XD?IPS200_SET_DIR??SEEKFREE_IPS200_SPI
010F2DH   010F2EH   000002H   BYTE   UNIT     XDATA          ?XD?IPS200_SET_FONT??SEEKFREE_IPS200_SPI
010F2FH   010F30H   000002H   BYTE   UNIT     XDATA          ?XD?KEY_SCAN??KEY
010F31H   010F32H   000002H   BYTE   UNIT     XDATA          ?XD?BOTOOM_LEFT_MOTOR_SET_SPEED??MOTOR
010F33H   010F34H   000002H   BYTE   UNIT     XDATA          ?XD?BOTOOM_RIGHT_MOTOR_SET_SPEED??MOTOR
010F35H   010F36H   000002H   BYTE   UNIT     XDATA          ?XD?SELECTION_STATUS??POINT_PROCESSING
010F37H   010F38H   000002H   BYTE   UNIT     XDATA          ?XD?CCD_CALC_POSITION_ERROR_SIMPLE??CCD_PROCESSING
010F39H   010F39H   000001H   BYTE   UNIT     XDATA          ?XD?PUTCHAR??BOARD
010F3AH   010F3AH   000001H   BYTE   UNIT     XDATA          ?XD?IAP_SET_TPS??ZF_EEPROM
010F3BH   010F3BH   000001H   BYTE   UNIT     XDATA          ?XD?SPI_MOSI??ZF_SPI
010F3CH   010F3CH   000001H   BYTE   UNIT     XDATA          ?XD?SPI_CHANGE_MODE??ZF_SPI
010F3DH   010F3DH   000001H   BYTE   UNIT     XDATA          ?XD?IMU660RA_SIMSPI_WR_BYTE?SEEKFREE_IMU660RA
010F3EH   010F3EH   000001H   BYTE   UNIT     XDATA          ?XD?IMU660RA_READ_REGISTER?SEEKFREE_IMU660RA
010F3FH   010F3FH   000001H   BYTE   UNIT     XDATA          ?XD?IMU660RA_SELF_CHECK?SEEKFREE_IMU660RA
010F40H   010F40H   000001H   BYTE   UNIT     XDATA          ?XD?CCD_COLLECT??SEEKFREE_TSL1401
010F41H   010F41H   000001H   BYTE   UNIT     XDATA          ?XD?IPS200_WRITE_8BIT_DATA_SPI??SEEKFREE_IPS200_SPI
010F42H   010F42H   000001H   BYTE   UNIT     XDATA          ?XD?IPS200_WRITE_COMMAND?SEEKFREE_IPS200_SPI
010F43H   010F43H   000001H   BYTE   UNIT     XDATA          ?XD?IPS200_WRITE_8BIT_DATA?SEEKFREE_IPS200_SPI
010F44H   010F44H   000001H   BYTE   UNIT     XDATA          ?XD?UART1_ISR??ISR
010F45H   010F45H   000001H   BYTE   INSEG    XDATA          ?XD?ISR
010F46H   010F46H   000001H   BYTE   UNIT     XDATA          ?XD?UART_RX_INTERRUPT_HANDLER??MAIN
010F47H   010F47H   000001H   BYTE   UNIT     XDATA          ?XD?READ_GPS_POINT_TASK??POINT_PROCESSING
010F48H   010F48H   000001H   BYTE   UNIT     XDATA          ?XD?EEPROM_TO_ARRAY??POINT_PROCESSING
010F49H   010F49H   000001H   BYTE   UNIT     XDATA          ?XD?POINT_TASK_FUNCTION??TASK
010F4AH   FE0BFFH   FCFCB6H   ---    ---      **GAP**
FE0C00H   FE2D17H   002118H   BYTE   INSEG    ECODE          ?PR?MENU
FE2D18H   FE4A00H   001CE9H   BYTE   INSEG    ECODE          ?PR?SEEKFREE_GPS_TAU1201
FE4A01H   FE628EH   00188EH   BYTE   INSEG    ECODE          ?PR?ZF_FIFO
FE628FH   FE79F1H   001763H   BYTE   INSEG    ECODE          ?C?LIB_CODE?
FE79F2H   FE8FF5H   001604H   BYTE   INSEG    ECODE          ?PR?ZF_FUNCTION
FE8FF6H   FE9F49H   000F54H   BYTE   INSEG    ECODE          ?PR?TASK
FE9F4AH   FEAD9BH   000E52H   BYTE   INSEG    ECODE          ?PR?MAIN
FEAD9CH   FEBADBH   000D40H   BYTE   INSEG    ECODE          ?PR?SEEKFREE_IPS200_SPI
FEBADCH   FEC808H   000D2DH   BYTE   INSEG    ECODE          ?PR?QUATERNION_POSE_CALCULATING
FEC809H   FED415H   000C0DH   BYTE   INSEG    ECODE          ?PR?ZF_PWM
FED416H   FEDE93H   000A7EH   BYTE   INSEG    ECODE          ?PR?ZF_GPIO
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 7


FEDE94H   FEE7BAH   000927H   BYTE   INSEG    ECODE          ?PR?POINT_PROCESSING
FEE7BBH   FEF0DEH   000924H   BYTE   INSEG    ECODE          ?PR?CCD_PROCESSING
FEF0DFH   FEF91DH   00083FH   BYTE   INSEG    ECODE          ?PR?PID
FEF91EH   FEFE66H   000549H   BYTE   INSEG    ECODE          ?PR?SEEKFREE_IMU660RA
FEFE67H   FEFFCCH   000166H   BYTE   INSEG    ECODE          ?PR?MY_ATAN2
FEFFCDH   FEFFFFH   000033H   BYTE   INSEG    ECODE          ?PR?BEEP
FF0000H   FF0002H   000003H   ---    OFFS..   CODE           ?CO?START251?4
FF0003H   FF0006H   000004H   ---    OFFS..   CODE           ?PR?IV?0
FF0007H   FF000AH   000004H   BYTE   INSEG    HCONST         ?HC?PRINTF
FF000BH   FF000EH   000004H   ---    OFFS..   CODE           ?PR?IV?1
FF000FH   FF0012H   000004H   ---    ---      **GAP**
FF0013H   FF0016H   000004H   ---    OFFS..   CODE           ?PR?IV?2
FF0017H   FF001AH   000004H   ---    ---      **GAP**
FF001BH   FF001EH   000004H   ---    OFFS..   CODE           ?PR?IV?3
FF001FH   FF0022H   000004H   ---    ---      **GAP**
FF0023H   FF0026H   000004H   ---    OFFS..   CODE           ?PR?IV?4
FF0027H   FF0040H   00001AH   BYTE   INSEG    ECODE          ?PR?MEMSET_
FF0041H   FF0042H   000002H   ---    ---      **GAP**
FF0043H   FF0046H   000004H   ---    OFFS..   CODE           ?PR?IV?8
FF0047H   FF0052H   00000CH   ---    ---      **GAP**
FF0053H   FF0056H   000004H   ---    OFFS..   CODE           ?PR?IV?10
FF0057H   FF005AH   000004H   ---    ---      **GAP**
FF005BH   FF005EH   000004H   ---    OFFS..   CODE           ?PR?IV?11
FF005FH   FF0062H   000004H   ---    ---      **GAP**
FF0063H   FF0066H   000004H   ---    OFFS..   CODE           ?PR?IV?12
FF0067H   FF0074H   00000EH   BYTE   INSEG    ECODE          ?PR?STRLEN_
FF0075H   FF0082H   00000EH   ---    ---      **GAP**
FF0083H   FF0086H   000004H   ---    OFFS..   CODE           ?PR?IV?16
FF0087H   FF008AH   000004H   ---    ---      **GAP**
FF008BH   FF008EH   000004H   ---    OFFS..   CODE           ?PR?IV?17
FF008FH   FF0092H   000004H   ---    ---      **GAP**
FF0093H   FF0096H   000004H   ---    OFFS..   CODE           ?PR?IV?18
FF0097H   FF009AH   000004H   ---    ---      **GAP**
FF009BH   FF009EH   000004H   ---    OFFS..   CODE           ?PR?IV?19
FF009FH   FF00A2H   000004H   ---    ---      **GAP**
FF00A3H   FF00A6H   000004H   ---    OFFS..   CODE           ?PR?IV?20
FF00A7H   FF212DH   002087H   BYTE   UNIT     CODE           ?CO?SEEKFREE_CONFIG
FF212EH   FF271DH   0005F0H   BYTE   UNIT     CODE           ?CO?SEEKFREE_FONT
FF271EH   FF273EH   000021H   BYTE   UNIT     CODE           ?C_C51STARTUP
FF273FH   FF276FH   000031H   BYTE   UNIT     CODE           ?C_C51STARTUP?2
FF2770H   FF2773H   000004H   BYTE   UNIT     CODE           ?C_C51STARTUP?3
FF2774H   FF2BA2H   00042FH   BYTE   INSEG    ECODE          ?PR?ZF_UART
FF2BA3H   FF2F8CH   0003EAH   BYTE   INSEG    ECODE          ?PR?EXCEPTION_HANDLING
FF2F8DH   FF3286H   0002FAH   BYTE   INSEG    ECODE          ?PR?BOARD
FF3287H   FF3537H   0002B1H   BYTE   INSEG    ECODE          ?PR?ZF_TIM
FF3538H   FF37D8H   0002A1H   BYTE   INSEG    ECODE          ?PR?MOTOR
FF37D9H   FF3A40H   000268H   BYTE   INSEG    ECODE          ?PR?ISR
FF3A41H   FF3C45H   000205H   BYTE   INSEG    ECODE          ?PR?ZF_SPI
FF3C46H   FF3E0FH   0001CAH   BYTE   INSEG    ECODE          ?PR?ZF_EEPROM
FF3E10H   FF3FB7H   0001A8H   BYTE   INSEG    ECODE          ?PR?POW__
FF3FB8H   FF410EH   000157H   BYTE   INSEG    ECODE          ?PR?SEEKFREE_TSL1401
FF410FH   FF4244H   000136H   BYTE   INSEG    ECODE          ?PR?ZF_ADC
FF4245H   FF4322H   0000DEH   BYTE   INSEG    ECODE          ?PR?ZF_DELAY
FF4323H   FF4400H   0000DEH   BYTE   INSEG    ECODE          ?PR?KEY
FF4401H   FF44CDH   0000CDH   BYTE   INSEG    ECODE          ?PR?ZF_NVIC
FF44CEH   FF4596H   0000C9H   BYTE   INSEG    ECODE          ?PR?FLOOR__
FF4597H   FF4618H   000082H   BYTE   INSEG    ECODE          ?PR?MODF__
FF4619H   FF464FH   000037H   BYTE   INSEG    ECODE          ?PR?STRNCMP_
FF4650H   FF467FH   000030H   BYTE   INSEG    ECODE          ?PR?STRNCPY_
FF4680H   FF46A4H   000025H   BYTE   INSEG    ECODE          ?PR?MEMCPY_
FF46A5H   FF46C3H   00001FH   BYTE   INSEG    ECODE          ?PR?STRCHR_
FF46C4H   FF4BD4H   000511H   BYTE   UNIT     HCONST         ?C_INITHDATA
FF4BD5H   FF4E23H   00024FH   BYTE   UNIT     HCONST         ?HC?MENU
FF4E24H   FF4EC5H   0000A2H   BYTE   UNIT     HCONST         ?HC?SEEKFREE_GPS_TAU1201
FF4EC6H   FF4F1DH   000058H   BYTE   UNIT     HCONST         ?HC?ZF_PWM
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 8


FF4F1EH   FF4F51H   000034H   BYTE   UNIT     HCONST         ?HC?SEEKFREE_IMU660RA
FF4F52H   FF4F6BH   00001AH   BYTE   UNIT     HCONST         ?HC?MAIN
FF4F6CH   FF4F7DH   000012H   BYTE   UNIT     HCONST         ?HC?EXCEPTION_HANDLING
FF4F7EH   FF4F8DH   000010H   BYTE   UNIT     HCONST         ?HC?ZF_FUNCTION



OVERLAY MAP OF MODULE:   .\Out_File\SEEKFREE (board)


FUNCTION/MODULE                               XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP
=========================================================
?C_C51STARTUP                                 ----- -----

*** NEW ROOT ********************************

?C_C51STARTUP?3                               ----- -----
  +--> main?/main

main?/main                                    ----- -----
  +--> _m?/main
  +--> SPRINTF?/SPRINTF?

_m?/main                                      ----- -----
  +--> board_init?/board
  +--> uart_init?/zf_uart
  +--> ctimer_count_init?/zf_tim
  +--> iap_init?/zf_eeprom
  +--> motor_init?/motor
  +--> beep_init?/beep
  +--> imu660ra_init?/SEEKFREE_IMU660RA
  +--> ips200_init_spi?/SEEKFREE_IPS200_SPI
  +--> gps_init?/SEEKFREE_GPS_TAU1201
  +--> NVIC_SetPriority?/zf_nvic
  +--> uart_rx_interrupt_handler?/main
  +--> ccd_init?/SEEKFREE_TSL1401
  +--> pit_timer_ms?/zf_tim
  +--> tim3_pit_hanlder?/main
  +--> tim1_pit_hanlder?/main
  +--> tim4_pit_hanlder?/main
  +--> ips200_clear?/SEEKFREE_IPS200_SPI
  +--> posture_exception_handling?/exception_handling
  +--> read_gps_point_task?/point_processing
  +--> PRINTF?/PRINTF?
  +--> menu_display?/menu

board_init?/board                             ----- -----
  +--> set_clk?/board
  +--> delay_init?/zf_delay
  +--> uart_init?/zf_uart
  +--> EnableGlobalIRQ?/board

set_clk?/board                                ----- -----

delay_init?/zf_delay                          ----- -----
  +--> ?C?SLDIV?/?C?SLDIV?

?C?SLDIV?/?C?SLDIV?                           ----- -----
  +--> ?C?ULIDIV?/?C?ULDIV?

?C?ULIDIV?/?C?ULDIV?                          ----- -----

uart_init?/zf_uart                            ----- -----
  +--> ?C?ULIDIV?/?C?ULDIV?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 9



EnableGlobalIRQ?/board                        ----- -----

ctimer_count_init?/zf_tim                     ----- -----

iap_init?/zf_eeprom                           ----- -----
  +--> iap_set_tps?/zf_eeprom

iap_set_tps?/zf_eeprom                        ----- -----
  +--> ?C?SLDIV?/?C?SLDIV?

motor_init?/motor                             ----- -----
  +--> pwm_init?/zf_pwm

pwm_init?/zf_pwm                              ----- -----
  +--> pwm_set_gpio?/zf_pwm
  +--> ?C?ULIDIV?/?C?ULDIV?

pwm_set_gpio?/zf_pwm                          ----- -----
  +--> gpio_mode?/zf_gpio

gpio_mode?/zf_gpio                            ----- -----

beep_init?/beep                               ----- -----
  +--> gpio_mode?/zf_gpio

imu660ra_init?/SEEKFREE_IMU660RA              ----- -----
  +--> delay_ms?/zf_delay
  +--> imu660ra_read_register/SEEKFREE_IMU660RA
  +--> imu660ra_self_check/SEEKFREE_IMU660RA
  +--> PRINTF?/PRINTF?
  +--> imu660ra_write_register/SEEKFREE_IMU660RA
  +--> imu660ra_write_registers/SEEKFREE_IMU660RA

delay_ms?/zf_delay                            ----- -----

imu660ra_read_register/SEEKFREE_IMU660RA      ----- -----
  +--> imu660ra_simspi_r_reg_bytes/SEEKFREE_IMU660RA

imu660ra_simspi_r_reg_bytes/SEEKFREE_IMU+     ----- -----
... 660RA
  +--> imu660ra_simspi_wr_byte/SEEKFREE_IMU660RA

imu660ra_simspi_wr_byte/SEEKFREE_IMU660R+     ----- -----
... A

imu660ra_self_check/SEEKFREE_IMU660RA         ----- -----
  +--> imu660ra_read_register/SEEKFREE_IMU660RA
  +--> delay_ms?/zf_delay

PRINTF?/PRINTF?                               0A1CH 0A43H
  +--> OUT/PRINTF?

OUT/PRINTF?                                   ----- -----
  +--> PUTCH/PRINTF?

PUTCH/PRINTF?                                 ----- -----
  +--> putchar?/board
  +--> OUT/PRINTF?

putchar?/board                                ----- -----
  +--> uart_putchar?/zf_uart

uart_putchar?/zf_uart                         ----- -----

L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 10


imu660ra_write_register/SEEKFREE_IMU660R+     ----- -----
... A
  +--> imu660ra_simspi_w_reg_byte/SEEKFREE_IMU660RA

imu660ra_simspi_w_reg_byte/SEEKFREE_IMU6+     ----- -----
... 60RA
  +--> imu660ra_simspi_wr_byte/SEEKFREE_IMU660RA

imu660ra_write_registers/SEEKFREE_IMU660+     ----- -----
... RA
  +--> imu660ra_simspi_w_reg_bytes/SEEKFREE_IMU660RA

imu660ra_simspi_w_reg_bytes/SEEKFREE_IMU+     ----- -----
... 660RA
  +--> imu660ra_simspi_wr_byte/SEEKFREE_IMU660RA

ips200_init_spi?/SEEKFREE_IPS200_SPI          ----- -----
  +--> spi_init?/zf_spi
  +--> ips200_set_dir?/SEEKFREE_IPS200_SPI
  +--> ips200_set_color?/SEEKFREE_IPS200_SPI
  +--> delay_ms?/zf_delay
  +--> ips200_write_command/SEEKFREE_IPS200_SPI
  +--> ips200_write_8bit_data/SEEKFREE_IPS200_SPI
  +--> ips200_clear?/SEEKFREE_IPS200_SPI

spi_init?/zf_spi                              ----- -----
  +--> gpio_mode?/zf_gpio

ips200_set_dir?/SEEKFREE_IPS200_SPI           ----- -----

ips200_set_color?/SEEKFREE_IPS200_SPI         ----- -----

ips200_write_command/SEEKFREE_IPS200_SPI      ----- -----
  +--> ips200_write_8bit_data_spi?/SEEKFREE_IPS200_SPI

ips200_write_8bit_data_spi?/SEEKFREE_IPS+     ----- -----
... 200_SPI
  +--> spi_mosi?/zf_spi

spi_mosi?/zf_spi                              ----- -----

ips200_write_8bit_data/SEEKFREE_IPS200_S+     ----- -----
... PI
  +--> ips200_write_8bit_data_spi?/SEEKFREE_IPS200_SPI

ips200_clear?/SEEKFREE_IPS200_SPI             ----- -----
  +--> ips200_set_region/SEEKFREE_IPS200_SPI
  +--> ips200_write_16bit_data/SEEKFREE_IPS200_SPI

ips200_set_region/SEEKFREE_IPS200_SPI         ----- -----
  +--> ips200_write_command/SEEKFREE_IPS200_SPI
  +--> ips200_write_16bit_data/SEEKFREE_IPS200_SPI

ips200_write_16bit_data/SEEKFREE_IPS200_+     ----- -----
... SPI
  +--> ips200_write_16bit_data_spi?/SEEKFREE_IPS200_SPI

ips200_write_16bit_data_spi?/SEEKFREE_IP+     ----- -----
... S200_SPI
  +--> spi_mosi?/zf_spi

gps_init?/SEEKFREE_GPS_TAU1201                ----- -----
  +--> fifo_init?/zf_fifo
  +--> delay_ms?/zf_delay
  +--> uart_init?/zf_uart
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 11


  +--> uart_putbuff?/zf_uart

fifo_init?/zf_fifo                            ----- -----

uart_putbuff?/zf_uart                         ----- -----
  +--> uart_putchar?/zf_uart

NVIC_SetPriority?/zf_nvic                     ----- -----

uart_rx_interrupt_handler?/main               ----- -----
  +--> fifo_write_buffer?/zf_fifo
  +--> speed_set?/motor
  +--> speed_set_zero?/motor
  +--> remind_on?/beep

fifo_write_buffer?/zf_fifo                    ----- -----
  +--> memcpy??/MEMCPY?
  +--> fifo_head_offset/zf_fifo

memcpy??/MEMCPY?                              ----- -----

fifo_head_offset/zf_fifo                      ----- -----

speed_set?/motor                              ----- -----
  +--> botoom_left_motor_set_speed?/motor
  +--> botoom_right_motor_set_speed?/motor
  +--> tail_left_motor_set_speed?/motor
  +--> tail_right_motor_set_speed?/motor

botoom_left_motor_set_speed?/motor            ----- -----
  +--> pwm_duty?/zf_pwm

pwm_duty?/zf_pwm                              ----- -----

botoom_right_motor_set_speed?/motor           ----- -----
  +--> pwm_duty?/zf_pwm

tail_left_motor_set_speed?/motor              ----- -----
  +--> pwm_duty?/zf_pwm

tail_right_motor_set_speed?/motor             ----- -----
  +--> pwm_duty?/zf_pwm

speed_set_zero?/motor                         ----- -----
  +--> pwm_duty?/zf_pwm

remind_on?/beep                               ----- -----

ccd_init?/SEEKFREE_TSL1401                    ----- -----
  +--> adc_init?/zf_adc
  +--> pit_timer_ms?/zf_tim

adc_init?/zf_adc                              ----- -----

pit_timer_ms?/zf_tim                          ----- -----
  +--> ?C?SLDIV?/?C?SLDIV?

tim3_pit_hanlder?/main                        ----- -----
  +--> gps_data_parse?/SEEKFREE_GPS_TAU1201
  +--> ctimer_count_read?/zf_tim
  +--> ctimer_count_clean?/zf_tim
  +--> correct_point?/point_processing
  +--> get_two_points_distance?/SEEKFREE_GPS_TAU1201
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> get_two_points_azimuth?/SEEKFREE_GPS_TAU1201
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 12


  +--> remind_on?/beep
  +--> speed_set?/motor
  +--> speed_set_zero?/motor
  +--> point_task_function?/task

gps_data_parse?/SEEKFREE_GPS_TAU1201          ----- -----
  +--> strchr??/STRCHR?
  +--> strncpy??/STRNCPY?
  +--> func_str_to_hex?/zf_function
  +--> gps_gnrmc_parse/SEEKFREE_GPS_TAU1201
  +--> gps_gngga_parse/SEEKFREE_GPS_TAU1201

strchr??/STRCHR?                              ----- -----

strncpy??/STRNCPY?                            ----- -----

func_str_to_hex?/zf_function                  ----- -----
  +--> strlen??/STRLEN?

strlen??/STRLEN?                              ----- -----

gps_gnrmc_parse/SEEKFREE_GPS_TAU1201          ----- -----
  +--> get_parameter_index/SEEKFREE_GPS_TAU1201
  +--> get_double_number/SEEKFREE_GPS_TAU1201
  +--> get_float_number/SEEKFREE_GPS_TAU1201
  +--> utc_to_btc/SEEKFREE_GPS_TAU1201

get_parameter_index/SEEKFREE_GPS_TAU1201      ----- -----
  +--> strchr??/STRCHR?

get_double_number/SEEKFREE_GPS_TAU1201        ----- -----
  +--> get_parameter_index/SEEKFREE_GPS_TAU1201
  +--> strncpy??/STRNCPY?
  +--> func_str_to_double?/zf_function

func_str_to_double?/zf_function               ----- -----

get_float_number/SEEKFREE_GPS_TAU1201         ----- -----
  +--> get_parameter_index/SEEKFREE_GPS_TAU1201
  +--> strncpy??/STRNCPY?
  +--> func_str_to_double?/zf_function

utc_to_btc/SEEKFREE_GPS_TAU1201               ----- -----

gps_gngga_parse/SEEKFREE_GPS_TAU1201          ----- -----
  +--> get_parameter_index/SEEKFREE_GPS_TAU1201
  +--> get_int_number/SEEKFREE_GPS_TAU1201
  +--> get_float_number/SEEKFREE_GPS_TAU1201

get_int_number/SEEKFREE_GPS_TAU1201           ----- -----
  +--> get_parameter_index/SEEKFREE_GPS_TAU1201
  +--> strncpy??/STRNCPY?
  +--> func_str_to_int?/zf_function

func_str_to_int?/zf_function                  ----- -----

ctimer_count_read?/zf_tim                     ----- -----

ctimer_count_clean?/zf_tim                    ----- -----

correct_point?/point_processing               ----- -----

get_two_points_distance?/SEEKFREE_GPS_TA+     ----- -----
... U1201
  +--> pow??/POW??
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 13



pow??/POW??                                   ----- -----
  +--> LOG??/LOG??

LOG??/LOG??                                   ----- -----

ips200_show_string?/SEEKFREE_IPS200_SPI       ----- -----
  +--> ips200_show_char?/SEEKFREE_IPS200_SPI

ips200_show_char?/SEEKFREE_IPS200_SPI         ----- -----
  +--> ips200_set_region/SEEKFREE_IPS200_SPI
  +--> ips200_write_16bit_data/SEEKFREE_IPS200_SPI

get_two_points_azimuth?/SEEKFREE_GPS_TAU+     ----- -----
... 1201

point_task_function?/task                     ----- -----
  +--> normal_function?/task
  +--> grass_function?/task
  +--> ramp_function?/task
  +--> bumpy_function?/task
  +--> arch_function?/task
  +--> barricade_function?/task
  +--> turn_function?/task
  +--> Complete_function?/task

normal_function?/task                         ----- -----
  +--> pid_angle?/pid
  +--> pid_rate?/pid
  +--> pid_speed?/pid
  +--> speed_set?/motor

pid_angle?/pid                                ----- -----

pid_rate?/pid                                 ----- -----

pid_speed?/pid                                ----- -----

grass_function?/task                          ----- -----
  +--> speed_set?/motor
  +--> remind_on?/beep
  +--> remind_off?/beep

remind_off?/beep                              ----- -----

ramp_function?/task                           ----- -----
  +--> remind_off?/beep
  +--> posture_exception_handling?/exception_handling
  +--> pid_speed?/pid
  +--> pid_angle?/pid
  +--> pid_rate?/pid
  +--> speed_set?/motor

posture_exception_handling?/exception_ha+     ----- -----
... ndling
  +--> speed_set?/motor
  +--> speed_set_zero?/motor
  +--> remind_on?/beep
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI

bumpy_function?/task                          ----- -----
  +--> posture_exception_handling?/exception_handling
  +--> pid_speed?/pid
  +--> pid_angle?/pid
  +--> pid_rate?/pid
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 14


  +--> speed_set?/motor

arch_function?/task                           ----- -----
  +--> remind_off?/beep
  +--> pid_angle?/pid
  +--> pid_ccd?/pid
  +--> pid_rate?/pid
  +--> pid_speed?/pid
  +--> speed_set?/motor

pid_ccd?/pid                                  ----- -----

barricade_function?/task                      ----- -----
  +--> pid_angle?/pid
  +--> pid_ccd?/pid
  +--> pid_rate?/pid
  +--> pid_speed?/pid
  +--> speed_set?/motor
  +--> remind_off?/beep

turn_function?/task                           ----- -----
  +--> pwm_duty?/zf_pwm
  +--> pid_angle?/pid
  +--> pid_rate?/pid
  +--> pid_speed?/pid
  +--> speed_set?/motor
  +--> remind_off?/beep

Complete_function?/task                       ----- -----
  +--> speed_set?/motor
  +--> speed_set_zero?/motor
  +--> remind_on?/beep

tim1_pit_hanlder?/main                        ----- -----
  +--> imu660ra_get_acc?/SEEKFREE_IMU660RA
  +--> imu660ra_get_gyro?/SEEKFREE_IMU660RA
  +--> imu660ra_acc_transition?/SEEKFREE_IMU660RA
  +--> imu660ra_gyro_transition?/SEEKFREE_IMU660RA
  +--> low_pass_filter?/quaternion_pose_calculating
  +--> quaternion_update?/quaternion_pose_calculating
  +--> get_euler_angles?/quaternion_pose_calculating
  +--> floor??/FLOOR??

imu660ra_get_acc?/SEEKFREE_IMU660RA           ----- -----
  +--> imu660ra_read_registers/SEEKFREE_IMU660RA

imu660ra_read_registers/SEEKFREE_IMU660R+     ----- -----
... A
  +--> imu660ra_simspi_r_reg_bytes/SEEKFREE_IMU660RA

imu660ra_get_gyro?/SEEKFREE_IMU660RA          ----- -----
  +--> imu660ra_read_registers/SEEKFREE_IMU660RA

imu660ra_acc_transition?/SEEKFREE_IMU660+     ----- -----
... RA

imu660ra_gyro_transition?/SEEKFREE_IMU66+     ----- -----
... 0RA

low_pass_filter?/quaternion_pose_calcula+     ----- -----
... ting

quaternion_update?/quaternion_pose_calcu+     ----- -----
... lating
  +--> quaternion_normalize/quaternion_pose_calculating
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 15



quaternion_normalize/quaternion_pose_cal+     ----- -----
... culating

get_euler_angles?/quaternion_pose_calcul+     ----- -----
... ating
  +--> my_atan2?/my_atan2
  +--> FABS??/FABS?
  +--> floor??/FLOOR??

my_atan2?/my_atan2                            ----- -----

FABS??/FABS?                                  ----- -----

floor??/FLOOR??                               ----- -----
  +--> modf??/MODF??

modf??/MODF??                                 ----- -----

tim4_pit_hanlder?/main                        ----- -----
  +--> ccd_collect?/SEEKFREE_TSL1401
  +--> ccd_data_filter?/ccd_processing
  +--> ccd_edge_detect?/ccd_processing
  +--> ccd_calc_position_error_simple?/ccd_processing

ccd_collect?/SEEKFREE_TSL1401                 ----- -----
  +--> adc_once?/zf_adc

adc_once?/zf_adc                              ----- -----

ccd_data_filter?/ccd_processing               ----- -----
  +--> moving_average_filter/ccd_processing

moving_average_filter/ccd_processing          ----- -----

ccd_edge_detect?/ccd_processing               ----- -----
  +--> calculate_dynamic_threshold/ccd_processing

calculate_dynamic_threshold/ccd_processi+     ----- -----
... ng

ccd_calc_position_error_simple?/ccd_proc+     ----- -----
... essing

read_gps_point_task?/point_processing         ----- -----
  +--> eeprom_read_double?/point_processing
  +--> iap_read_bytes?/zf_eeprom
  +--> get_two_points_azimuth?/SEEKFREE_GPS_TAU1201
  +--> get_point_error?/point_processing

eeprom_read_double?/point_processing          ----- -----
  +--> iap_read_bytes?/zf_eeprom
  +--> bytes_to_double?/point_processing

iap_read_bytes?/zf_eeprom                     ----- -----
  +--> eeprom_trig?/zf_eeprom

eeprom_trig?/zf_eeprom                        ----- -----

bytes_to_double?/point_processing             ----- -----

get_point_error?/point_processing             ----- -----

menu_display?/menu                            ----- -----
  +--> take_point_page_fun?/menu
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 16


  +--> state_slect_page_fun?/menu
  +--> function_page_fun?/menu
  +--> parameter_adjustment_page_fun?/menu
  +--> common_para_adj_page_fun?/menu
  +--> path_display_page_fun?/menu
  +--> ccd_page_fun?/menu
  +--> ips200_show_float?/SEEKFREE_IPS200_SPI
  +--> ips200_show_int?/SEEKFREE_IPS200_SPI
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> ips200_show_uint?/SEEKFREE_IPS200_SPI

take_point_page_fun?/menu                     ----- -----
  +--> key_scan?/key
  +--> take_point?/point_processing
  +--> remind_once?/beep
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI

key_scan?/key                                 ----- -----
  +--> delay_ms?/zf_delay

take_point?/point_processing                  ----- -----
  +--> eeprom_write_double?/point_processing

eeprom_write_double?/point_processing         ----- -----
  +--> double_to_bytes?/point_processing
  +--> iap_write_bytes?/zf_eeprom

double_to_bytes?/point_processing             ----- -----

iap_write_bytes?/zf_eeprom                    ----- -----
  +--> eeprom_trig?/zf_eeprom

remind_once?/beep                             ----- -----
  +--> delay_ms?/zf_delay

state_slect_page_fun?/menu                    ----- -----
  +--> key_scan?/key
  +--> remind_once?/beep
  +--> selection_status?/point_processing
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI

selection_status?/point_processing            ----- -----
  +--> iap_write_bytes?/zf_eeprom

function_page_fun?/menu                       ----- -----
  +--> key_scan?/key
  +--> remind_once?/beep
  +--> start_page_fun?/menu
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> clear_eeprom_page_fun?/menu

start_page_fun?/menu                          ----- -----
  +--> key_scan?/key
  +--> remind_once?/beep
  +--> remind_on?/beep
  +--> reset_quaternion?/quaternion_pose_calculating
  +--> remind_off?/beep
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> ips200_show_uint?/SEEKFREE_IPS200_SPI

reset_quaternion?/quaternion_pose_calcul+     ----- -----
... ating

ips200_show_uint?/SEEKFREE_IPS200_SPI         ----- -----
  +--> memset??/MEMSET?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 17


  +--> ?C?ULIDIV?/?C?ULDIV?
  +--> func_uint_to_str?/zf_function
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI

memset??/MEMSET?                              ----- -----

func_uint_to_str?/zf_function                 ----- -----
  +--> ?C?ULIDIV?/?C?ULDIV?

clear_eeprom_page_fun?/menu                   ----- -----
  +--> key_scan?/key
  +--> iap_erase_page?/zf_eeprom
  +--> remind_once?/beep
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI

iap_erase_page?/zf_eeprom                     ----- -----
  +--> eeprom_trig?/zf_eeprom
  +--> delay_ms?/zf_delay

parameter_adjustment_page_fun?/menu           ----- -----
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> key_scan?/key
  +--> remind_once?/beep
  +--> ips200_show_float?/SEEKFREE_IPS200_SPI

ips200_show_float?/SEEKFREE_IPS200_SPI        ----- -----
  +--> memset??/MEMSET?
  +--> ?C?SLDIV?/?C?SLDIV?
  +--> func_double_to_str?/zf_function
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI

func_double_to_str?/zf_function               ----- -----
  +--> ?C?SLDIV?/?C?SLDIV?

common_para_adj_page_fun?/menu                ----- -----
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> key_scan?/key
  +--> remind_once?/beep
  +--> ips200_show_float?/SEEKFREE_IPS200_SPI

path_display_page_fun?/menu                   ----- -----
  +--> ips200_clear?/SEEKFREE_IPS200_SPI
  +--> key_scan?/key
  +--> remind_once?/beep
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> ips200_show_int?/SEEKFREE_IPS200_SPI
  +--> ips200_show_float?/SEEKFREE_IPS200_SPI
  +--> ips200_draw_point?/SEEKFREE_IPS200_SPI
  +--> draw_marker/menu
  +--> ips200_draw_line?/SEEKFREE_IPS200_SPI

ips200_show_int?/SEEKFREE_IPS200_SPI          ----- -----
  +--> memset??/MEMSET?
  +--> ?C?SLDIV?/?C?SLDIV?
  +--> func_int_to_str?/zf_function
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI

func_int_to_str?/zf_function                  ----- -----
  +--> ?C?SLDIV?/?C?SLDIV?

ips200_draw_point?/SEEKFREE_IPS200_SPI        ----- -----
  +--> ips200_set_region/SEEKFREE_IPS200_SPI
  +--> ips200_write_16bit_data/SEEKFREE_IPS200_SPI

draw_marker/menu                              ----- -----
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 18


  +--> ips200_draw_point?/SEEKFREE_IPS200_SPI

ips200_draw_line?/SEEKFREE_IPS200_SPI         ----- -----
  +--> ips200_draw_point?/SEEKFREE_IPS200_SPI

ccd_page_fun?/menu                            ----- -----
  +--> ips200_clear?/SEEKFREE_IPS200_SPI
  +--> key_scan?/key
  +--> set_pit_timer_ms?/ccd_processing
  +--> remind_once?/beep
  +--> ips200_show_wave?/SEEKFREE_IPS200_SPI
  +--> ips200_show_string?/SEEKFREE_IPS200_SPI
  +--> ips200_show_uint?/SEEKFREE_IPS200_SPI
  +--> ips200_show_int?/SEEKFREE_IPS200_SPI

set_pit_timer_ms?/ccd_processing              ----- -----
  +--> ?C?SLDIV?/?C?SLDIV?

ips200_show_wave?/SEEKFREE_IPS200_SPI         ----- -----
  +--> ips200_set_region/SEEKFREE_IPS200_SPI
  +--> ips200_write_16bit_data/SEEKFREE_IPS200_SPI
  +--> ?C?ULIDIV?/?C?ULDIV?
  +--> ips200_draw_point?/SEEKFREE_IPS200_SPI

SPRINTF?/SPRINTF?                             0A1CH 0A43H
  +--> OUT/SPRINTF?

OUT/SPRINTF?                                  ----- -----
  +--> PUTCH/SPRINTF?

PUTCH/SPRINTF?                                ----- -----

*** NEW ROOT ********************************

UART1_Isr?/isr                                ----- -----

*** NEW ROOT ********************************

UART2_Isr?/isr                                ----- -----
  +--> gps_uart_callback?/SEEKFREE_GPS_TAU1201

gps_uart_callback?/SEEKFREE_GPS_TAU1201       ----- -----
  +--> fifo_write_buffer?/zf_fifo
  +--> fifo_read_buffer?/zf_fifo
  +--> strncmp??/STRNCMP?
  +--> fifo_used?/zf_fifo
  +--> fifo_clear?/zf_fifo

fifo_read_buffer?/zf_fifo                     ----- -----
  +--> fifo_used?/zf_fifo
  +--> memcpy??/MEMCPY?
  +--> fifo_end_offset/zf_fifo

fifo_used?/zf_fifo                            ----- -----

fifo_end_offset/zf_fifo                       ----- -----

strncmp??/STRNCMP?                            ----- -----

fifo_clear?/zf_fifo                           ----- -----
  +--> memset??/MEMSET?

*** NEW ROOT ********************************

UART3_Isr?/isr                                ----- -----
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 19



*** NEW ROOT ********************************

UART4_Isr?/isr                                ----- -----

*** NEW ROOT ********************************

INT0_Isr?/isr                                 ----- -----

*** NEW ROOT ********************************

INT1_Isr?/isr                                 ----- -----

*** NEW ROOT ********************************

INT2_Isr?/isr                                 ----- -----

*** NEW ROOT ********************************

INT3_Isr?/isr                                 ----- -----

*** NEW ROOT ********************************

INT4_Isr?/isr                                 ----- -----

*** NEW ROOT ********************************

TM0_Isr?/isr                                  ----- -----

*** NEW ROOT ********************************

TM1_Isr?/isr                                  ----- -----

*** NEW ROOT ********************************

TM2_Isr?/isr                                  ----- -----

*** NEW ROOT ********************************

TM3_Isr?/isr                                  ----- -----

*** NEW ROOT ********************************

TM4_Isr?/isr                                  ----- -----

*** NEW ROOT ********************************

?C_C51STARTUP?2                               ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Out_File\SEEKFREE (board)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00FE79C9H   ECODE    ---       ?C?BMOVEPP8?
      00FE79CBH   ECODE    ---       ?C?BMOVEPP?
      00FE6610H   ECODE    FAR LAB   ?C?CASTD?
      00FE78B7H   ECODE    FAR LAB   ?C?CASTF?
      000000FFH   NUMBER   ---       ?C?CODESEG
      00FE65CDH   ECODE    FAR LAB   ?C?DCASTC?
      00FE6670H   ECODE    FAR LAB   ?C?DCASTF?
      00FE65C8H   ECODE    FAR LAB   ?C?DCASTI?
      00FE65C3H   ECODE    FAR LAB   ?C?DCASTL?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 20


      00FE706FH   ECODE    ---       ?C?DP2SERIES?
      00FE6296H   ECODE    ---       ?C?DPADD?
      00FE6554H   ECODE    ---       ?C?DPCMP3?
      00FE6556H   ECODE    ---       ?C?DPCMP?
      00FE7369H   ECODE    ---       ?C?DPCONVERT?
      00FE64B5H   ECODE    ---       ?C?DPDIV?
      00FE6B91H   ECODE    ---       ?C?DPGETOPN2?
      00FE63A5H   ECODE    ---       ?C?DPMUL?
      00FE6BDCH   ECODE    ---       ?C?DPNANRESULT?
      00FE65B5H   ECODE    ---       ?C?DPNEG?
      00FE6BE8H   ECODE    ---       ?C?DPOVERFLOW?
      00FE6BCDH   ECODE    ---       ?C?DPRESULT2?
      00FE6BB4H   ECODE    ---       ?C?DPRESULT?
      00FE7492H   ECODE    ---       ?C?DPROUND?
      00FE707BH   ECODE    ---       ?C?DPSERIES?
      00FE6292H   ECODE    ---       ?C?DPSUB?
      00FE6BE3H   ECODE    ---       ?C?DPUNDERFLOW?
      00FE756DH   ECODE    ---       ?C?DTNPWR?
      00FE7884H   ECODE    FAR LAB   ?C?FCASTC?
      00FE66B2H   ECODE    FAR LAB   ?C?FCASTD?
      00FE787FH   ECODE    FAR LAB   ?C?FCASTI?
      00FE787AH   ECODE    FAR LAB   ?C?FCASTL?
      00FE764DH   ECODE    ---       ?C?FPADD?
      00FE7826H   ECODE    ---       ?C?FPCMP3?
      00FE7828H   ECODE    ---       ?C?FPCMP?
      00FE77A5H   ECODE    ---       ?C?FPDIV?
      00FE75F6H   ECODE    ---       ?C?FPGETOPN2?
      00FE7705H   ECODE    ---       ?C?FPMUL?
      00FE7633H   ECODE    ---       ?C?FPNANRESULT?
      00FE786CH   ECODE    ---       ?C?FPNEG?
      00FE763BH   ECODE    ---       ?C?FPOVERFLOW?
      00FE7625H   ECODE    ---       ?C?FPRESULT2?
      00FE760FH   ECODE    ---       ?C?FPRESULT?
      00FE764AH   ECODE    ---       ?C?FPSUB?
      00FE7638H   ECODE    ---       ?C?FPUNDERFLOW?
      00FF273FH   CODE     ---       ?C?INITHDATA
      00FF4BD3H   HCONST   WORD      ?C?INITHDATA_END
      00FE7937H   ECODE    ---       ?C?LIMUL?
      00FE7926H   ECODE    ---       ?C?LMUL?
      00FE6C41H   ECODE    ---       ?C?PRNFMT?
      00FE78F4H   ECODE    ---       ?C?SIDIV?
      00FE7993H   ECODE    FAR LAB   ?C?SLDIV?
      00FF0000H   CODE     ---       ?C?STARTUP?
      00FE7942H   ECODE    FAR LAB   ?C?ULDIV?
      00FE7940H   ECODE    FAR LAB   ?C?ULIDIV?
      00000001H   NUMBER   ---       ?C?XDATASEG
      00FF0000H   CODE     ---       ?C_STARTUP?
      00010A44H   XDATA    BYTE      ?check_cross_border??BYTE
      00010D54H   XDATA    BYTE      ?correct_point??BYTE
      00010C81H   XDATA    BYTE      ?eeprom_write_double??BYTE
      00010C61H   XDATA    BYTE      ?fifo_init??BYTE
      00010B58H   XDATA    BYTE      ?fifo_read_buffer??BYTE
      00010D18H   XDATA    BYTE      ?fifo_read_element??BYTE
      00010B70H   XDATA    BYTE      ?fifo_read_tail_buffer??BYTE
      00010C0AH   XDATA    BYTE      ?fifo_write_buffer??BYTE
      0001071CH   XDATA    BYTE      ?func_double_to_str??BYTE
      00010D48H   XDATA    BYTE      ?get_point_error??BYTE
      00010916H   XDATA    BYTE      ?get_two_points_azimuth??BYTE
      00010786H   XDATA    BYTE      ?get_two_points_distance??BYTE
      00010D77H   XDATA    BYTE      ?iap_read_bytes??BYTE
      00010D81H   XDATA    BYTE      ?iap_write_bytes??BYTE
      00010BB8H   XDATA    BYTE      ?ips200_draw_line??BYTE
      000109EDH   XDATA    BYTE      ?ips200_show_float??BYTE
      00010A8FH   XDATA    BYTE      ?ips200_show_wave??BYTE
      00010D60H   XDATA    BYTE      ?low_pass_filter??BYTE
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 21


      00010BA0H   XDATA    BYTE      ?my_atan2??BYTE
      00010A44H   XDATA    ---       ?printf??BYTE
      00010BE2H   XDATA    BYTE      ?pwm_freq??BYTE
      00010BCEH   XDATA    BYTE      ?pwm_init??BYTE
      000107E6H   XDATA    BYTE      ?quaternion_update??BYTE
      00010DA9H   XDATA    BYTE      ?speed_set??BYTE
      00010CD7H   XDATA    BYTE      ?spi_init??BYTE
      00010A44H   XDATA    ---       ?sprintf??BYTE
      00010C71H   XDATA    BYTE      ?take_point??BYTE
      00010C9FH   XDATA    BYTE      ?uart_init??BYTE
      00010D8BH   XDATA    BYTE      ?uart_putbuff??BYTE
      00FE6BF6H   ECODE    ---       _chkdouble_??
      00FE9F78H   ECODE    ---       _m?
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      0001094EH   XDATA    FLOAT     actual_angular_speed
*SFR* 000000BCH   DATA     BYTE      ADC_CONTR
      00FF410FH   ECODE    ---       adc_init?
      00FF41D8H   ECODE    ---       adc_once?
*SFR* 000000BDH   DATA     BYTE      ADC_RES
*SFR* 000000BEH   DATA     BYTE      ADC_RESL
*SFR* 000000DEH   DATA     BYTE      ADCCFG
      00010605H   XDATA    FLOAT     angle_error_sum
      0001097AH   XDATA    ---       angles
      000109BAH   XDATA    BYTE      anomaly_change_point_num
      000109BBH   XDATA    DOUBLE    anomaly_detection_angle
      000109C4H   XDATA    BYTE      anomaly_detection_flag
      00FE9680H   ECODE    ---       arch_function?
      00010987H   XDATA    BYTE      arch_task_flag
      00FF212EH   CODE     ---       ascii_font_8x16
      00FE6B62H   ECODE    ---       asin??
      00FE6A3EH   ECODE    ---       atan??
*SFR* 000000EFH   DATA     BYTE      AUXINTIF
*SFR* 0000008EH   DATA     BYTE      AUXR
*SFR* 00000097H   DATA     BYTE      AUXR2
      00010952H   XDATA    FLOAT     ax
      00010956H   XDATA    FLOAT     ay
      0001095AH   XDATA    FLOAT     az
*SFR* 000000F0H   DATA     BYTE      B
      00FE98C2H   ECODE    ---       barricade_function?
      00010988H   XDATA    BYTE      barricade_task_flag
      00FEFFCDH   ECODE    ---       beep_init?
      00FF31C3H   ECODE    ---       board_init?
      00FF3599H   ECODE    ---       botoom_left_motor_set_speed?
      00FF35B0H   ECODE    ---       botoom_right_motor_set_speed?
      00FE9175H   ECODE    ---       bumpy_function?
      0001098AH   XDATA    BYTE      bumpy_task_flag
      00010E99H   XDATA    ---       busy
      00FEE428H   ECODE    ---       bytes_to_double?
*SFR* 000000C8H.1 DATA     BIT       C_T2
*SFR* 000000F1H   DATA     BYTE      CANICR
*SFR* 000000FAH   DATA     BYTE      CCAP0H
*SFR* 000000EAH   DATA     BYTE      CCAP0L
*SFR* 000000FBH   DATA     BYTE      CCAP1H
*SFR* 000000EBH   DATA     BYTE      CCAP1L
*SFR* 000000FCH   DATA     BYTE      CCAP2H
*SFR* 000000ECH   DATA     BYTE      CCAP2L
*SFR* 000000FDH   DATA     BYTE      CCAP3H
*SFR* 000000EDH   DATA     BYTE      CCAP3L
*SFR* 000000FEH   DATA     BYTE      CCAP4H
*SFR* 000000EEH   DATA     BYTE      CCAP4L
*SFR* 000000DAH   DATA     BYTE      CCAPM0
*SFR* 000000DBH   DATA     BYTE      CCAPM1
*SFR* 000000DCH   DATA     BYTE      CCAPM2
*SFR* 000000DDH   DATA     BYTE      CCAPM3
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 22


*SFR* 000000DEH   DATA     BYTE      CCAPM4
      00FEEF7BH   ECODE    ---       ccd_calc_position_error?
      00FEF0AAH   ECODE    ---       ccd_calc_position_error_simple?
      00FF408FH   ECODE    ---       ccd_collect?
      00000009H   EDATA    ---       ccd_data_ch1
      00000109H   EDATA    ---       ccd_data_ch2
      00FEEB07H   ECODE    ---       ccd_data_filter?
      00FEEC08H   ECODE    ---       ccd_edge_detect?
      00010438H   XDATA    ---       ccd_filtered_data
      00FF3FB8H   ECODE    ---       ccd_init?
      00FEEE4EH   ECODE    ---       ccd_is_line_valid?
      00FE164BH   ECODE    ---       ccd_page_fun?
      00FF3FDDH   ECODE    ---       ccd_send_data?
*SFR* 000000D8H.0 DATA     BIT       CCF0
*SFR* 000000D8H.1 DATA     BIT       CCF1
*SFR* 000000D8H.2 DATA     BIT       CCF2
*SFR* 000000D8H.3 DATA     BIT       CCF3
*SFR* 000000D8H.4 DATA     BIT       CCF4
*SFR* 000000D8H   DATA     BYTE      CCON
*SFR* 00000090H.3 DATA     BIT       CEX0
*SFR* 00000090H.4 DATA     BIT       CEX1
*SFR* 00000090H.5 DATA     BIT       CEX2
*SFR* 00000090H.6 DATA     BIT       CEX3
*SFR* 00000090H.7 DATA     BIT       CEX4
*SFR* 000000D8H.7 DATA     BIT       CF
*SFR* 000000F9H   DATA     BYTE      CH
      00FF2BA3H   ECODE    ---       check_cross_border?
*SFR* 000000EAH   DATA     BYTE      CKCON
*SFR* 000000E9H   DATA     BYTE      CL
      00FE161EH   ECODE    ---       clear_eeprom_page_fun?
*SFR* 000000D9H   DATA     BYTE      CMOD
*SFR* 000000E6H   DATA     BYTE      CMPCR1
*SFR* 000000E7H   DATA     BYTE      CMPCR2
      00FE1EF6H   ECODE    ---       common_para_adj_page_fun?
      00FE9E98H   ECODE    ---       Complete_function?
      0001098CH   XDATA    BYTE      Complete_task_flag
      00FEE6F0H   ECODE    ---       correct_point?
      00FE689BH   ECODE    ---       cos??
*SFR* 000000C8H.0 DATA     BIT       CP_RL2
*SFR* 000000D8H.6 DATA     BIT       CR
      00FF33C1H   ECODE    ---       ctimer_count_clean?
      00FF3287H   ECODE    ---       ctimer_count_init?
      00FF32E3H   ECODE    ---       ctimer_count_read?
      0001063FH   XDATA    FLOAT     ctimer_speed
*SFR* 000000D0H.7 DATA     BIT       CY
      00010538H   XDATA    BYTE      debug_data
      00010539H   XDATA    DWORD     debug_time
      00FF4245H   ECODE    ---       delay_init?
      00FF429FH   ECODE    ---       delay_ms?
      00FF42DCH   ECODE    ---       delay_us?
      00FF327FH   ECODE    ---       DisableGlobalIRQ?
      00FF00A7H   CODE     ---       dl1b_default_configuration
      00FEE3A1H   ECODE    ---       double_to_bytes?
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 00000084H   DATA     BYTE      DPXL
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000BAH.7 DATA     BIT       EAXFR
*SFR* 000000A8H.6 DATA     BIT       EC
*SFR* 00000090H.2 DATA     BIT       ECI
      00010436H   XDATA    BYTE      edge_count
      0001042AH   XDATA    ---       edges
      00FEE4DAH   ECODE    ---       eeprom_read_double?
      00FEE511H   ECODE    ---       eeprom_to_array?
      00FF3C46H   ECODE    ---       eeprom_trig?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 23


      00FEE48DH   ECODE    ---       eeprom_write_double?
      000109C3H   XDATA    BYTE      element_processing_state
      00FF3283H   ECODE    ---       EnableGlobalIRQ?
      000109D4H   XDATA    LONG      encoder
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000C8H.3 DATA     BIT       EXEN2
*SFR* 000000C8H.6 DATA     BIT       EXF2
      00FE7159H   ECODE    ---       exp??
*SFR* 000000D0H.5 DATA     BIT       F0
      00FE6736H   ECODE    FAR LAB   fabs??
*SFR* 00000098H.7 DATA     BIT       FE
      00FE4B41H   ECODE    ---       fifo_clear?
      00FE61C2H   ECODE    ---       fifo_init?
      00FE5620H   ECODE    ---       fifo_read_buffer?
      00FE53FEH   ECODE    ---       fifo_read_element?
      00FE5B95H   ECODE    ---       fifo_read_tail_buffer?
      00FE4C89H   ECODE    ---       fifo_used?
      00FE4E73H   ECODE    ---       fifo_write_buffer?
      00FE4CC0H   ECODE    ---       fifo_write_element?
      00FF44CEH   ECODE    ---       floor??
      00FE8890H   ECODE    ---       func_double_to_str?
      00FE8174H   ECODE    ---       func_float_to_str?
      00FE79F2H   ECODE    ---       func_get_greatest_common_divisor?
      00FE8E93H   ECODE    ---       func_hex_to_str?
      00FE7BE0H   ECODE    ---       func_int_to_str?
      00FE7A98H   ECODE    ---       func_soft_delay?
      00FE857CH   ECODE    ---       func_str_to_double?
      00FE7F10H   ECODE    ---       func_str_to_float?
      00FE8CC8H   ECODE    ---       func_str_to_hex?
      00FE7AC0H   ECODE    ---       func_str_to_int?
      00FE7D76H   ECODE    ---       func_str_to_uint?
      00FE7E15H   ECODE    ---       func_uint_to_str?
      00FE144AH   ECODE    ---       function_page_fun?
      000109D3H   XDATA    BYTE      g_ccd_process_flag
      000109CEH   XDATA    BYTE      g_ctimer_read_finish_flag
      0001023EH   XDATA    BYTE      get_data_error_flag
      00FEC3CAH   ECODE    ---       get_euler_angles?
      00FEE625H   ECODE    ---       get_point_error?
      00FEC3A4H   ECODE    ---       get_quaternion?
      00FE3D91H   ECODE    ---       get_two_points_azimuth?
      00FE3A06H   ECODE    ---       get_two_points_distance?
      00010000H   XDATA    BYTE      got_point_num
      00FED4BBH   ECODE    ---       gpio_mode?
      00FED416H   ECODE    ---       gpio_pull_set?
      00FE431BH   ECODE    ---       gps_data_parse?
      00010426H   XDATA    INT       gps_gga_state
      00FE46CEH   ECODE    ---       gps_init?
      00010428H   XDATA    INT       gps_rmc_state
      000102F2H   XDATA    ---       gps_tau1201
      00010271H   XDATA    BYTE      gps_tau1201_flag
      00FE4534H   ECODE    ---       gps_uart_callback?
      00FE908EH   ECODE    ---       grass_function?
      00010989H   XDATA    BYTE      grass_task_flag
      0001095EH   XDATA    FLOAT     gx
      00010962H   XDATA    FLOAT     gy
      00010966H   XDATA    FLOAT     gz
      000109CFH   XDATA    DWORD     heartbeat_time
*SFR* 000000F6H   DATA     BYTE      IAP_ADDRE
*SFR* 000000C3H   DATA     BYTE      IAP_ADDRH
*SFR* 000000C4H   DATA     BYTE      IAP_ADDRL
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 24


*SFR* 000000C5H   DATA     BYTE      IAP_CMD
*SFR* 000000C7H   DATA     BYTE      IAP_CONTR
*SFR* 000000C2H   DATA     BYTE      IAP_DATA
      00FF3DCDH   ECODE    ---       iap_erase_page?
      00FF3C68H   ECODE    ---       iap_get_cmd_state?
      00FF3C64H   ECODE    ---       iap_idle?
      00FF3C5BH   ECODE    ---       iap_init?
      00FF3C9DH   ECODE    ---       iap_read_bytes?
      00FF3C7AH   ECODE    ---       iap_set_tps?
*SFR* 000000F5H   DATA     BYTE      IAP_TPS
*SFR* 000000C6H   DATA     BYTE      IAP_TRIG
      00FF3D3DH   ECODE    ---       iap_write_bytes?
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 000000A8H   DATA     BYTE      IE0
*SFR* 00000088H.1 DATA     BIT       IE0_
*SFR* 000000B1H   DATA     BYTE      IE1
*SFR* 00000088H.3 DATA     BIT       IE1_
*SFR* 000000AFH   DATA     BYTE      IE2
      00010EA3H   XDATA    INT       if_take_point_key
      00FEFC11H   ECODE    ---       imu660ra_acc_transition?
      00010D24H   XDATA    INT       imu660ra_acc_x
      00010D26H   XDATA    INT       imu660ra_acc_y
      00010D28H   XDATA    INT       imu660ra_acc_z
      00FF012EH   CODE     ---       imu660ra_config_file
      00FEFB65H   ECODE    ---       imu660ra_get_acc?
      00FEFBBBH   ECODE    ---       imu660ra_get_gyro?
      00FEFCC6H   ECODE    ---       imu660ra_gyro_transition?
      00010D2AH   XDATA    INT       imu660ra_gyro_x
      00010D2CH   XDATA    INT       imu660ra_gyro_y
      00010D2EH   XDATA    INT       imu660ra_gyro_z
      00FEFDB2H   ECODE    ---       imu660ra_init?
*SFR* 000000B0H.2 DATA     BIT       INT0
      00FF38F6H   ECODE    ---       INT0_Isr?
*SFR* 000000B0H.3 DATA     BIT       INT1
      00FF38FEH   ECODE    ---       INT1_Isr?
      00FF38FFH   ECODE    ---       INT2_Isr?
      00FF3907H   ECODE    ---       INT3_Isr?
      00FF390FH   ECODE    ---       INT4_Isr?
*SFR* 0000008FH   DATA     BYTE      INTCLKO
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000B5H   DATA     BYTE      IP2
*SFR* 000000B6H   DATA     BYTE      IP2H
*SFR* 000000DFH   DATA     BYTE      IP3
*SFR* 000000EEH   DATA     BYTE      IP3H
*SFR* 000000B7H   DATA     BYTE      IPH
*SFR* 000000B7H   DATA     BYTE      IPH0
*SFR* 000000B8H   DATA     BYTE      IPL0
      00FEAE73H   ECODE    ---       ips200_clear?
      00FEB03BH   ECODE    ---       ips200_draw_line?
      00FEAFFEH   ECODE    ---       ips200_draw_point?
      00FEAEF4H   ECODE    ---       ips200_full?
      00FEB8FFH   ECODE    ---       ips200_init_spi?
      00FEAFD9H   ECODE    ---       ips200_set_color?
      00FEAF7BH   ECODE    ---       ips200_set_dir?
      00FEAFC6H   ECODE    ---       ips200_set_font?
      00FEB1C2H   ECODE    ---       ips200_show_char?
      00FEB570H   ECODE    ---       ips200_show_float?
      00FEB38EH   ECODE    ---       ips200_show_int?
      00FEB307H   ECODE    ---       ips200_show_string?
      00FEB482H   ECODE    ---       ips200_show_uint?
      00FEB716H   ECODE    ---       ips200_show_wave?
      00FEADA9H   ECODE    ---       ips200_write_16bit_data_spi?
      00FEAD9CH   ECODE    ---       ips200_write_8bit_data_spi?
*SFR* 0000009DH   DATA     BYTE      IRCBAND
*SFR* 0000009FH   DATA     BYTE      IRTRIM
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 25


*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      000105EDH   XDATA    FLOAT     kd_angle
      00010619H   XDATA    FLOAT     kd_ccd
      0001061DH   XDATA    FLOAT     kd_rate
      000105F5H   XDATA    FLOAT     kd_speed
      00010EA5H   XDATA    INT       key_num
      00FF4323H   ECODE    ---       key_scan?
      000105F1H   XDATA    FLOAT     ki_angle
      00010621H   XDATA    FLOAT     ki_ccd
      00010627H   XDATA    FLOAT     ki_rate
      000105FDH   XDATA    FLOAT     ki_speed
      000105F9H   XDATA    FLOAT     kp_angle
      0001062BH   XDATA    FLOAT     kp_ccd
      0001062FH   XDATA    FLOAT     kp_rate
      00010609H   XDATA    FLOAT     kp_speed
      00010434H   XDATA    INT       line_position_error
*SFR* 000000F9H   DATA     BYTE      LINICR
      00FE7246H   ECODE    FAR LAB   log??
      00FEC7B2H   ECODE    ---       low_pass_filter?
      00FE9F4AH   ECODE    ---       MAIN?
      00FF4680H   ECODE    ---       memcpy??
      00FF0027H   ECODE    ---       memset??
      00FE0C00H   ECODE    ---       menu_display?
      00FF4597H   ECODE    ---       modf??
      00FF3538H   ECODE    ---       motor_init?
      00FEFE67H   ECODE    ---       my_atan2?
      00FE8FF6H   ECODE    ---       normal_function?
      00010986H   XDATA    BYTE      normal_task_flag
      0001098EH   XDATA    BYTE      now_task_node
      00FF4401H   ECODE    ---       NVIC_SetPriority?
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000094H   DATA     BYTE      P0M0
*SFR* 00000093H   DATA     BYTE      P0M1
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000092H   DATA     BYTE      P1M0
*SFR* 00000091H   DATA     BYTE      P1M1
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.7 DATA     BIT       P27
*SFR* 00000096H   DATA     BYTE      P2M0
*SFR* 00000095H   DATA     BYTE      P2M1
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
*SFR* 000000B2H   DATA     BYTE      P3M0
*SFR* 000000B1H   DATA     BYTE      P3M1
*SFR* 000000C0H   DATA     BYTE      P4
*SFR* 000000C0H.0 DATA     BIT       P40
*SFR* 000000C0H.1 DATA     BIT       P41
*SFR* 000000C0H.2 DATA     BIT       P42
*SFR* 000000C0H.3 DATA     BIT       P43
*SFR* 000000B4H   DATA     BYTE      P4M0
*SFR* 000000B3H   DATA     BYTE      P4M1
*SFR* 000000C8H   DATA     BYTE      P5
*SFR* 000000C8H.2 DATA     BIT       P52
*SFR* 000000CAH   DATA     BYTE      P5M0
*SFR* 000000C9H   DATA     BYTE      P5M1
*SFR* 000000E8H   DATA     BYTE      P6
*SFR* 000000E8H.7 DATA     BIT       P67
*SFR* 000000CCH   DATA     BYTE      P6M0
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 26


*SFR* 000000CBH   DATA     BYTE      P6M1
*SFR* 000000F8H   DATA     BYTE      P7
*SFR* 000000F8H.0 DATA     BIT       P70
*SFR* 000000F8H.1 DATA     BIT       P71
*SFR* 000000F8H.2 DATA     BIT       P72
*SFR* 000000F8H.3 DATA     BIT       P73
*SFR* 000000F8H.5 DATA     BIT       P75
*SFR* 000000F8H.6 DATA     BIT       P76
*SFR* 000000E2H   DATA     BYTE      P7M0
*SFR* 000000E1H   DATA     BYTE      P7M1
*SFR* 000000A2H   DATA     BYTE      P_SW1
*SFR* 000000BAH   DATA     BYTE      P_SW2
*SFR* 000000BBH   DATA     BYTE      P_SW3
      00FE17DBH   ECODE    ---       parameter_adjustment_page_fun?
      00FE2253H   ECODE    ---       path_display_page_fun?
*SFR* 00000087H   DATA     BYTE      PCON
      00FEF0DFH   ECODE    ---       pid_angle?
      00FEF7E8H   ECODE    ---       pid_ccd?
      00FEF359H   ECODE    ---       pid_rate?
      00FEF5B0H   ECODE    ---       pid_speed?
      00FF3426H   ECODE    ---       pit_timer_ms?
      0001023FH   XDATA    ---       point_corrected_data
      00010009H   XDATA    ---       point_data_array
      0001022DH   XDATA    ---       point_error_data
      00FE9EBAH   ECODE    ---       point_task_function?
      00FF2EA2H   ECODE    ---       posture_exception_handling?
      00FF3E10H   ECODE    ---       pow??
*SFR* 000000B8H.6 DATA     BIT       PPC
      00FE66E9H   ECODE    FAR LAB   printf?
*SFR* 000000B8H.4 DATA     BIT       PS
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000D1H   DATA     BYTE      PSW1
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
      00FF326AH   ECODE    ---       putchar?
      00FF4EE6H   HCONST   ---       PWM_ARR_ADDR
      00FF4EEEH   HCONST   ---       PWM_CCER_ADDR
      00FF4EFEH   HCONST   ---       PWM_CCMR_ADDR
      00FF4EC6H   HCONST   ---       PWM_CCR_ADDR
      00FED076H   ECODE    ---       pwm_duty?
      00FED1DDH   ECODE    ---       pwm_freq?
      00FECBEFH   ECODE    ---       pwm_init?
      00FEC809H   ECODE    ---       pwm_set_gpio?
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      0001096AH   XDATA    ---       q
      00FEBC78H   ECODE    ---       quaternion_update?
      00FE93D6H   ECODE    ---       ramp_function?
      0001098BH   XDATA    BYTE      ramp_task_flag
      00010637H   XDATA    FLOAT     rate_error_sum
      000105E9H   XDATA    FLOAT     rate_integral_sum
*SFR* 00000098H.2 DATA     BIT       RB8
*SFR* 000000CBH   DATA     BYTE      RCAP2H
*SFR* 000000CAH   DATA     BYTE      RCAP2L
*SFR* 000000C8H.5 DATA     BIT       RCLK
*SFR* 000000B0H.7 DATA     BIT       RD
      00FEE0B7H   ECODE    ---       read_gps_point_task?
      0001023DH   XDATA    BYTE      read_point_status_node
      00010225H   XDATA    DWORD     read_start_addr
      0001024FH   XDATA    BYTE      receive
      00FEFFE4H   ECODE    ---       remind_off?
      00FEFFDDH   ECODE    ---       remind_on?
      00FEFFEBH   ECODE    ---       remind_once?
*SFR* 00000098H.4 DATA     BIT       REN
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 27


      00FEC705H   ECODE    ---       reset_quaternion?
*SFR* 00000098H.0 DATA     BIT       RI
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 000000FFH   DATA     BYTE      RSTCFG
*SFR* 000000B0H.0 DATA     BIT       RXD
*SFR* 0000009BH   DATA     BYTE      S2BUF
*SFR* 0000009AH   DATA     BYTE      S2CON
*SFR* 000000ADH   DATA     BYTE      S3BUF
*SFR* 000000ACH   DATA     BYTE      S3CON
*SFR* 000000FEH   DATA     BYTE      S4BUF
*SFR* 000000FDH   DATA     BYTE      S4CON
*SFR* 000000A9H   DATA     BYTE      SADDR
*SFR* 000000B9H   DATA     BYTE      SADEN
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
      00FEE359H   ECODE    ---       selection_status?
      00FF2F8DH   ECODE    ---       set_clk?
      00FEE7BBH   ECODE    ---       set_pit_timer_ms?
      00FF3705H   ECODE    ---       side_left_motor_set_speed?
      00FF372CH   ECODE    ---       side_right_motor_set_speed?
      00FE68BCH   ECODE    ---       sin??
*SFR* 00000098H.7 DATA     BIT       SM0
*SFR* 00000098H.6 DATA     BIT       SM1
*SFR* 00000098H.5 DATA     BIT       SM2
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000CEH   DATA     BYTE      SPCTL
*SFR* 000000CFH   DATA     BYTE      SPDAT
      00010615H   XDATA    FLOAT     speed_ccd_out
      00010611H   XDATA    FLOAT     speed_error_sum
      00010601H   XDATA    FLOAT     speed_out
      00FF3753H   ECODE    ---       speed_set?
      00FF37B0H   ECODE    ---       speed_set_zero?
      0001060DH   XDATA    FLOAT     speed_side_out
*SFR* 000000BEH   DATA     BYTE      SPH
      00FF3C20H   ECODE    ---       spi_change_mode?
      00FF3B42H   ECODE    ---       spi_change_pin?
      00FF3A41H   ECODE    ---       spi_init?
      00FF3B2CH   ECODE    ---       spi_mosi?
      00FE6716H   ECODE    FAR LAB   sprintf?
*SFR* 000000CDH   DATA     BYTE      SPSTAT
      00FE674BH   ECODE    ---       sqrt??
      00010229H   XDATA    DWORD     start_addr
      000109C5H   XDATA    DOUBLE    start_angle
      000109CDH   XDATA    BYTE      start_flag
      00FE15A7H   ECODE    ---       start_page_fun?
      00010C21H   XDATA    BYTE      start_step
      00FE1213H   ECODE    ---       state_slect_page_fun?
      00FF46A5H   ECODE    ---       strchr??
      00FF0067H   ECODE    ---       strlen??
      00FF4619H   ECODE    ---       strncmp??
      00FF4650H   ECODE    ---       strncpy??
      00010EB7H   XDATA    LONG      sys_clk
*SFR* 000000B0H.4 DATA     BIT       T0
*SFR* 000000B0H.5 DATA     BIT       T1
*SFR* 00000090H.0 DATA     BIT       T2
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 00000090H.1 DATA     BIT       T2EX
*SFR* 000000D6H   DATA     BYTE      T2H
*SFR* 000000D7H   DATA     BYTE      T2L
*SFR* 000000C9H   DATA     BYTE      T2MOD
*SFR* 000000D4H   DATA     BYTE      T3H
*SFR* 000000D5H   DATA     BYTE      T3L
*SFR* 000000D2H   DATA     BYTE      T4H
*SFR* 000000D3H   DATA     BYTE      T4L
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 28


*SFR* 000000DDH   DATA     BYTE      T4T3M
      00FF35C7H   ECODE    ---       tail_left_motor_set_speed?
      00FF3666H   ECODE    ---       tail_right_motor_set_speed?
      00FEE2C5H   ECODE    ---       take_point?
      000109D8H   XDATA    CHAR      take_point_num
      00FE1192H   ECODE    ---       take_point_page_fun?
      00FE68F2H   ECODE    ---       TAN??
      0001063BH   XDATA    FLOAT     target_angle
      000105E5H   XDATA    FLOAT     target_rate
      000105E1H   XDATA    FLOAT     target_speed
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.4 DATA     BIT       TCLK
*SFR* 00000088H   DATA     BYTE      TCON
      00FEDE94H   ECODE    ---       test_point_init?
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C8H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 00000098H.1 DATA     BIT       TI
      000109D9H   XDATA    ---       tim0_irq_handler
      000109DDH   XDATA    ---       tim1_irq_handler
      00FEAA75H   ECODE    ---       tim1_pit_hanlder?
      000109E1H   XDATA    ---       tim2_irq_handler
      000109E5H   XDATA    ---       tim3_irq_handler
      00FEA3B6H   ECODE    ---       tim3_pit_hanlder?
      000109E9H   XDATA    ---       tim4_irq_handler
      00FEAD7CH   ECODE    ---       tim4_pit_hanlder?
      00010626H   XDATA    BYTE      time1
      00010437H   XDATA    BYTE      timer_count
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
      00FF3917H   ECODE    ---       TM0_Isr?
      00FF395EH   ECODE    ---       TM1_Isr?
      00FF39A5H   ECODE    ---       TM2_Isr?
      00FF39ADH   ECODE    ---       TM3_Isr?
      00FF39F7H   ECODE    ---       TM4_Isr?
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C8H.2 DATA     BIT       TR2
      00000008H   EDATA    BYTE      tsl1401_finish_flag
      00FE9AEFH   ECODE    ---       turn_function?
      0001098DH   XDATA    BYTE      turn_task_flag
      00010001H   XDATA    DOUBLE    two_points_distance
*SFR* 000000B0H.1 DATA     BIT       TXD
      00FF37D9H   ECODE    ---       UART1_Isr?
      00FF382AH   ECODE    ---       UART2_Isr?
      00FF3872H   ECODE    ---       UART3_Isr?
      00FF3898H   ECODE    ---       UART4_Isr?
      00FF2774H   ECODE    ---       uart_init?
      00FF2B0EH   ECODE    ---       uart_putbuff?
      00FF2A91H   ECODE    ---       uart_putchar?
      00FF2B5EH   ECODE    ---       uart_putstr?
      00FEA314H   ECODE    ---       uart_rx_interrupt_handler?
*SFR* 000000F4H   DATA     BYTE      USBCON
*SFR* 000000A6H   DATA     BYTE      VRTRIM
*SFR* 000000C1H   DATA     BYTE      WDT_CONTR
*SFR* 000000A6H   DATA     BYTE      WDTRST
      00010E53H   XDATA    ---       wireless_module_uart_handler
      00010E51H   XDATA    INT       wireless_type
*SFR* 000000B0H.6 DATA     BIT       WR
*SFR* 000000E9H   DATA     BYTE      WTST
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 29


      00010EBFH   XDATA    WORD      zf_delay_ms
      00010EC1H   XDATA    WORD      zf_delay_us



SYMBOL TABLE OF MODULE:  .\Out_File\SEEKFREE (board)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       board
      00FF3283H   PUBLIC    ECODE    ---       EnableGlobalIRQ?
      00FF31C3H   PUBLIC    ECODE    ---       board_init?
      00FF2F8DH   PUBLIC    ECODE    ---       set_clk?
      00FF327FH   PUBLIC    ECODE    ---       DisableGlobalIRQ?
      00FF326AH   PUBLIC    ECODE    ---       putchar?
      00010EB7H   PUBLIC    XDATA    LONG      sys_clk
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000BAH.7 SFRSYM    DATA     BIT       EAXFR
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EAH   SFRSYM    DATA     BYTE      CKCON
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000A6H   SFRSYM    DATA     BYTE      VRTRIM
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      0000009FH   SFRSYM    DATA     BYTE      IRTRIM
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000E9H   SFRSYM    DATA     BYTE      WTST
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 30


      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF2F8DH   BLOCK     ECODE    ---       LVL=0
      00FF2F8DH   LINE      ECODE    ---       #45
      00FF2F8DH   LINE      ECODE    ---       #48
      00FF2F90H   LINE      ECODE    ---       #50
      00FF2FA6H   LINE      ECODE    ---       #53
      00FF2FB3H   LINE      ECODE    ---       #54
      00FF2FC0H   LINE      ECODE    ---       #55
      00FF2FCDH   LINE      ECODE    ---       #56
      00FF2FD1H   LINE      ECODE    ---       #57
      00FF2FDDH   LINE      ECODE    ---       #58
      00FF2FE0H   LINE      ECODE    ---       #59
      00FF2FF6H   LINE      ECODE    ---       #62
      00FF3003H   LINE      ECODE    ---       #63
      00FF3010H   LINE      ECODE    ---       #64
      00FF301DH   LINE      ECODE    ---       #65
      00FF3021H   LINE      ECODE    ---       #66
      00FF302DH   LINE      ECODE    ---       #67
      00FF3030H   LINE      ECODE    ---       #68
      00FF3046H   LINE      ECODE    ---       #71
      00FF3053H   LINE      ECODE    ---       #72
      00FF3060H   LINE      ECODE    ---       #73
      00FF306DH   LINE      ECODE    ---       #74
      00FF3071H   LINE      ECODE    ---       #75
      00FF307DH   LINE      ECODE    ---       #76
      00FF3080H   LINE      ECODE    ---       #77
      00FF3096H   LINE      ECODE    ---       #81
      00FF30A3H   LINE      ECODE    ---       #82
      00FF30B0H   LINE      ECODE    ---       #83
      00FF30BDH   LINE      ECODE    ---       #84
      00FF30C1H   LINE      ECODE    ---       #85
      00FF30CDH   LINE      ECODE    ---       #86
      00FF30D0H   LINE      ECODE    ---       #87
      00FF30E6H   LINE      ECODE    ---       #90
      00FF30F3H   LINE      ECODE    ---       #91
      00FF3100H   LINE      ECODE    ---       #92
      00FF310DH   LINE      ECODE    ---       #93
      00FF3111H   LINE      ECODE    ---       #94
      00FF311DH   LINE      ECODE    ---       #95
      00FF3120H   LINE      ECODE    ---       #96
      00FF3136H   LINE      ECODE    ---       #99
      00FF3143H   LINE      ECODE    ---       #100
      00FF3150H   LINE      ECODE    ---       #101
      00FF315DH   LINE      ECODE    ---       #102
      00FF3161H   LINE      ECODE    ---       #103
      00FF316DH   LINE      ECODE    ---       #104
      00FF316FH   LINE      ECODE    ---       #107
      00FF3181H   LINE      ECODE    ---       #109
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 31


      00FF318EH   LINE      ECODE    ---       #110
      00FF319BH   LINE      ECODE    ---       #111
      00FF31A8H   LINE      ECODE    ---       #112
      00FF31ACH   LINE      ECODE    ---       #113
      00FF31B8H   LINE      ECODE    ---       #114
      00FF31B8H   LINE      ECODE    ---       #116
      00FF31C2H   LINE      ECODE    ---       #117
      ---         BLOCKEND  ---      ---       LVL=0

      00FF31C3H   BLOCK     ECODE    ---       LVL=0
      00FF31C3H   LINE      ECODE    ---       #123
      00FF31C3H   LINE      ECODE    ---       #125
      00FF31C7H   LINE      ECODE    ---       #126
      00FF31CAH   LINE      ECODE    ---       #127
      00FF31CDH   LINE      ECODE    ---       #128
      00FF31D0H   LINE      ECODE    ---       #129
      00FF31D4H   LINE      ECODE    ---       #139
      00FF31E2H   LINE      ECODE    ---       #145
      00FF31E6H   LINE      ECODE    ---       #147
      00FF31E9H   LINE      ECODE    ---       #148
      00FF31ECH   LINE      ECODE    ---       #149
      00FF31F8H   LINE      ECODE    ---       #151
      00FF31FBH   LINE      ECODE    ---       #152
      00FF31FEH   LINE      ECODE    ---       #153
      00FF3201H   LINE      ECODE    ---       #154
      00FF3204H   LINE      ECODE    ---       #155
      00FF3207H   LINE      ECODE    ---       #156
      00FF320AH   LINE      ECODE    ---       #157
      00FF320DH   LINE      ECODE    ---       #158
      00FF3210H   LINE      ECODE    ---       #159
      00FF3213H   LINE      ECODE    ---       #160
      00FF3216H   LINE      ECODE    ---       #161
      00FF3219H   LINE      ECODE    ---       #162
      00FF321CH   LINE      ECODE    ---       #163
      00FF321FH   LINE      ECODE    ---       #164
      00FF3222H   LINE      ECODE    ---       #165
      00FF3225H   LINE      ECODE    ---       #166
      00FF3228H   LINE      ECODE    ---       #168
      00FF322BH   LINE      ECODE    ---       #169
      00FF322EH   LINE      ECODE    ---       #170
      00FF3231H   LINE      ECODE    ---       #171
      00FF3234H   LINE      ECODE    ---       #172
      00FF3237H   LINE      ECODE    ---       #173
      00FF323AH   LINE      ECODE    ---       #174
      00FF323DH   LINE      ECODE    ---       #175
      00FF3240H   LINE      ECODE    ---       #176
      00FF3243H   LINE      ECODE    ---       #178
      00FF3265H   LINE      ECODE    ---       #179
      00FF3269H   LINE      ECODE    ---       #180
      ---         BLOCKEND  ---      ---       LVL=0

      00FF326AH   BLOCK     ECODE    ---       LVL=0
      00010F39H   SYMBOL    XDATA    CHAR      c
      00FF326AH   LINE      ECODE    ---       #185
      00FF326EH   LINE      ECODE    ---       #192
      00FF327AH   LINE      ECODE    ---       #194
      00FF327EH   LINE      ECODE    ---       #195
      ---         BLOCKEND  ---      ---       LVL=0

      00FF327FH   BLOCK     ECODE    ---       LVL=0
      00FF327FH   LINE      ECODE    ---       #198
      00FF327FH   LINE      ECODE    ---       #200
      00FF3282H   LINE      ECODE    ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 32


      00FF3283H   BLOCK     ECODE    ---       LVL=0
      00FF3283H   LINE      ECODE    ---       #204
      00FF3283H   LINE      ECODE    ---       #206
      00FF3286H   LINE      ECODE    ---       #207
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       common
      00010E51H   PUBLIC    XDATA    INT       wireless_type
      00010E53H   PUBLIC    XDATA    ---       wireless_module_uart_handler
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       ?C_START?
      00FF0000H   PUBLIC    CODE     ---       ?C?STARTUP?
      00FF0000H   PUBLIC    CODE     ---       ?C_STARTUP?
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000F0H   SFRSYM    DATA     BYTE      B
      000000FAH   SFRSYM    DATA     BYTE      CCAP0H
      000000EAH   SFRSYM    DATA     BYTE      CCAP0L
      000000FBH   SFRSYM    DATA     BYTE      CCAP1H
      000000EBH   SFRSYM    DATA     BYTE      CCAP1L
      000000FCH   SFRSYM    DATA     BYTE      CCAP2H
      000000ECH   SFRSYM    DATA     BYTE      CCAP2L
      000000FDH   SFRSYM    DATA     BYTE      CCAP3H
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 33


      000000EDH   SFRSYM    DATA     BYTE      CCAP3L
      000000FEH   SFRSYM    DATA     BYTE      CCAP4H
      000000EEH   SFRSYM    DATA     BYTE      CCAP4L
      000000DAH   SFRSYM    DATA     BYTE      CCAPM0
      000000DBH   SFRSYM    DATA     BYTE      CCAPM1
      000000DCH   SFRSYM    DATA     BYTE      CCAPM2
      000000DDH   SFRSYM    DATA     BYTE      CCAPM3
      000000DEH   SFRSYM    DATA     BYTE      CCAPM4
      000000D8H.0 SFRSYM    DATA     BIT       CCF0
      000000D8H.1 SFRSYM    DATA     BIT       CCF1
      000000D8H.2 SFRSYM    DATA     BIT       CCF2
      000000D8H.3 SFRSYM    DATA     BIT       CCF3
      000000D8H.4 SFRSYM    DATA     BIT       CCF4
      000000D8H   SFRSYM    DATA     BYTE      CCON
      00000090H.3 SFRSYM    DATA     BIT       CEX0
      00000090H.4 SFRSYM    DATA     BIT       CEX1
      00000090H.5 SFRSYM    DATA     BIT       CEX2
      00000090H.6 SFRSYM    DATA     BIT       CEX3
      00000090H.7 SFRSYM    DATA     BIT       CEX4
      000000D8H.7 SFRSYM    DATA     BIT       CF
      000000F9H   SFRSYM    DATA     BYTE      CH
      000000E9H   SFRSYM    DATA     BYTE      CL
      000000D9H   SFRSYM    DATA     BYTE      CMOD
      000000C8H.0 SFRSYM    DATA     BIT       CP_RL2
      000000D8H.6 SFRSYM    DATA     BIT       CR
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000C8H.1 SFRSYM    DATA     BIT       C_T2
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      00000084H   SFRSYM    DATA     BYTE      DPXL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000A8H.6 SFRSYM    DATA     BIT       EC
      00000090H.2 SFRSYM    DATA     BIT       ECI
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C8H.3 SFRSYM    DATA     BIT       EXEN2
      000000C8H.6 SFRSYM    DATA     BIT       EXF2
      000000D0H.5 SFRSYM    DATA     BIT       F0
      00000098H.7 SFRSYM    DATA     BIT       FE
      000000A8H   SFRSYM    DATA     BYTE      IE0
      00000088H.1 SFRSYM    DATA     BIT       IE0_
      000000B1H   SFRSYM    DATA     BYTE      IE1
      00000088H.3 SFRSYM    DATA     BIT       IE1_
      000000B0H.2 SFRSYM    DATA     BIT       INT0
      000000B0H.3 SFRSYM    DATA     BIT       INT1
      000000B7H   SFRSYM    DATA     BYTE      IPH0
      000000B8H   SFRSYM    DATA     BYTE      IPL0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000D0H.0 SFRSYM    DATA     BIT       P
      00000080H   SFRSYM    DATA     BYTE      P0
      00000090H   SFRSYM    DATA     BYTE      P1
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B0H   SFRSYM    DATA     BYTE      P3
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000B8H.6 SFRSYM    DATA     BIT       PPC
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000D1H   SFRSYM    DATA     BYTE      PSW1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 34


      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000098H.2 SFRSYM    DATA     BIT       RB8
      000000CBH   SFRSYM    DATA     BYTE      RCAP2H
      000000CAH   SFRSYM    DATA     BYTE      RCAP2L
      000000C8H.5 SFRSYM    DATA     BIT       RCLK
      000000B0H.7 SFRSYM    DATA     BIT       RD
      00000098H.4 SFRSYM    DATA     BIT       REN
      00000098H.0 SFRSYM    DATA     BIT       RI
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B0H.0 SFRSYM    DATA     BIT       RXD
      000000A9H   SFRSYM    DATA     BYTE      SADDR
      000000B9H   SFRSYM    DATA     BYTE      SADEN
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000098H   SFRSYM    DATA     BYTE      SCON
      00000098H.7 SFRSYM    DATA     BIT       SM0
      00000098H.6 SFRSYM    DATA     BIT       SM1
      00000098H.5 SFRSYM    DATA     BIT       SM2
      00000081H   SFRSYM    DATA     BYTE      SP
      000000BEH   SFRSYM    DATA     BYTE      SPH
      000000B0H.4 SFRSYM    DATA     BIT       T0
      000000B0H.5 SFRSYM    DATA     BIT       T1
      00000090H.0 SFRSYM    DATA     BIT       T2
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000090H.1 SFRSYM    DATA     BIT       T2EX
      000000C9H   SFRSYM    DATA     BYTE      T2MOD
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000C8H.4 SFRSYM    DATA     BIT       TCLK
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000088H.5 SFRSYM    DATA     BIT       TF0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000C8H.7 SFRSYM    DATA     BIT       TF2
      0000008CH   SFRSYM    DATA     BYTE      TH0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      00000098H.1 SFRSYM    DATA     BIT       TI
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000CCH   SFRSYM    DATA     BYTE      TL2
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H.4 SFRSYM    DATA     BIT       TR0
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000C8H.2 SFRSYM    DATA     BIT       TR2
      000000B0H.1 SFRSYM    DATA     BIT       TXD
      000000A6H   SFRSYM    DATA     BYTE      WDTRST
      000000B0H.6 SFRSYM    DATA     BIT       WR
      00000800H   SYMBOL    NUMBER   ---       EDATALEN
      00FF2726H   SYMBOL    CODE     ---       EDATALOOP
      00000001H   SYMBOL    NUMBER   ---       EDF
      00000001H   SYMBOL    NUMBER   ---       EMAP
      00000000H   SYMBOL    NUMBER   ---       HDATALEN
      00010000H   SYMBOL    NUMBER   ---       HDATASTART
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000001H   SYMBOL    NUMBER   ---       INTR
      00000001H   SYMBOL    NUMBER   ---       PAGM
      00000003H   SYMBOL    NUMBER   ---       RDRG
      00000001H   SYMBOL    NUMBER   ---       SRCM
      00000800H   SYMBOL    NUMBER   ---       STACKSIZE
      00FF271EH   SYMBOL    CODE     ---       STARTUP1
      00000003H   SYMBOL    NUMBER   ---       WSA
      00000003H   SYMBOL    NUMBER   ---       WSB
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 35


      00000001H   SYMBOL    NUMBER   ---       XALE
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00010000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00008000H   SYMBOL    NUMBER   ---       XDATALEN
      00FF2735H   SYMBOL    CODE     ---       XDATALOOP
      00010000H   SYMBOL    NUMBER   ---       XDATASTART

      00FF0000H   BLOCK     CODE     NEAR LAB  LVL=0
      00FF0000H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0

      00FF271EH   BLOCK     CODE     NEAR LAB  LVL=0
      00FF271EH   LINE      CODE     ---       #188
      00FF2721H   LINE      CODE     ---       #191
      00FF2725H   LINE      CODE     ---       #192
      00FF2726H   LINE      CODE     ---       #193
      00FF2729H   LINE      CODE     ---       #194
      00FF272BH   LINE      CODE     ---       #195
      00FF272DH   LINE      CODE     ---       #199
      00FF2730H   LINE      CODE     ---       #200
      00FF2734H   LINE      CODE     ---       #201
      00FF2735H   LINE      CODE     ---       #202
      00FF2736H   LINE      CODE     ---       #203
      00FF2737H   LINE      CODE     ---       #204
      00FF2739H   LINE      CODE     ---       #205
      00FF273BH   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2770H   BLOCK     CODE     NEAR LAB  LVL=0
      00FF2770H   LINE      CODE     ---       #238
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_adc
      00FF41D8H   PUBLIC    ECODE    ---       adc_once?
      00FF410FH   PUBLIC    ECODE    ---       adc_init?
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000BEH   SFRSYM    DATA     BYTE      ADC_RESL
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000DEH   SFRSYM    DATA     BYTE      ADCCFG
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 36


      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000BDH   SFRSYM    DATA     BYTE      ADC_RES
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FF410FH   BLOCK     ECODE    ---       LVL=0
      00010EBBH   SYMBOL    XDATA    INT       adcn
      00010EBDH   SYMBOL    XDATA    INT       speed
      00FF410FH   LINE      ECODE    ---       #32
      00FF411BH   LINE      ECODE    ---       #34
      00FF411EH   LINE      ECODE    ---       #36
      00FF4121H   LINE      ECODE    ---       #37
      00FF412BH   LINE      ECODE    ---       #39
      00FF413FH   LINE      ECODE    ---       #42
      00FF415CH   LINE      ECODE    ---       #43
      00FF4177H   LINE      ECODE    ---       #44
      00FF4179H   LINE      ECODE    ---       #45
      00FF418BH   LINE      ECODE    ---       #48
      00FF41A8H   LINE      ECODE    ---       #49
      00FF41C3H   LINE      ECODE    ---       #50
      00FF41C3H   LINE      ECODE    ---       #52
      00FF41D4H   LINE      ECODE    ---       #54
      00FF41D7H   LINE      ECODE    ---       #55
      ---         BLOCKEND  ---      ---       LVL=0

      00FF41D8H   BLOCK     ECODE    ---       LVL=0
      00010E57H   SYMBOL    XDATA    INT       adcn
      00010E59H   SYMBOL    XDATA    INT       resolution
      00FF41E4H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E5BH   SYMBOL    XDATA    WORD      adc_value
      ---         BLOCKEND  ---      ---       LVL=1
      00FF41D8H   LINE      ECODE    ---       #66
      00FF41E4H   LINE      ECODE    ---       #67
      00FF41E4H   LINE      ECODE    ---       #70
      00FF41E7H   LINE      ECODE    ---       #71
      00FF41F1H   LINE      ECODE    ---       #73
      00FF41F4H   LINE      ECODE    ---       #74
      00FF41F9H   LINE      ECODE    ---       #75
      00FF41FCH   LINE      ECODE    ---       #77
      00FF4206H   LINE      ECODE    ---       #78
      00FF4213H   LINE      ECODE    ---       #79
      00FF4222H   LINE      ECODE    ---       #81
      00FF4225H   LINE      ECODE    ---       #82
      00FF4228H   LINE      ECODE    ---       #84
      00FF423EH   LINE      ECODE    ---       #87
      00FF4244H   LINE      ECODE    ---       #88
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_delay
      00FF4245H   PUBLIC    ECODE    ---       delay_init?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 37


      00FF429FH   PUBLIC    ECODE    ---       delay_ms?
      00FF42DCH   PUBLIC    ECODE    ---       delay_us?
      00010EBFH   PUBLIC    XDATA    WORD      zf_delay_ms
      00010EC1H   PUBLIC    XDATA    WORD      zf_delay_us
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF4245H   BLOCK     ECODE    ---       LVL=0
      00FF4245H   LINE      ECODE    ---       #34
      00FF4245H   LINE      ECODE    ---       #36
      00FF425DH   LINE      ECODE    ---       #37
      00FF4279H   LINE      ECODE    ---       #38
      00FF429EH   LINE      ECODE    ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      00FF429FH   BLOCK     ECODE    ---       LVL=0
      00010EC3H   SYMBOL    XDATA    WORD      ms
      00FF42A5H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010EC5H   SYMBOL    XDATA    WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF429FH   LINE      ECODE    ---       #48
      00FF42A5H   LINE      ECODE    ---       #49
      00FF42A5H   LINE      ECODE    ---       #51
      00FF42A5H   LINE      ECODE    ---       #52
      00FF42B1H   LINE      ECODE    ---       #54
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 38


      00FF42C6H   LINE      ECODE    ---       #55
      00FF42DBH   LINE      ECODE    ---       #56
      ---         BLOCKEND  ---      ---       LVL=0

      00FF42DCH   BLOCK     ECODE    ---       LVL=0
      00010E5DH   SYMBOL    XDATA    DWORD     us
      00FF42E6H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E61H   SYMBOL    XDATA    WORD      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF42DCH   LINE      ECODE    ---       #64
      00FF42E6H   LINE      ECODE    ---       #65
      00FF42E6H   LINE      ECODE    ---       #67
      00FF42E6H   LINE      ECODE    ---       #68
      00FF42F2H   LINE      ECODE    ---       #70
      00FF4307H   LINE      ECODE    ---       #71
      00FF4322H   LINE      ECODE    ---       #72
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_eeprom
      00FF3C7AH   PUBLIC    ECODE    ---       iap_set_tps?
      00FF3C46H   PUBLIC    ECODE    ---       eeprom_trig?
      00FF3DCDH   PUBLIC    ECODE    ---       iap_erase_page?
      00FF3C64H   PUBLIC    ECODE    ---       iap_idle?
      00FF3C9DH   PUBLIC    ECODE    ---       iap_read_bytes?
      00FF3C5BH   PUBLIC    ECODE    ---       iap_init?
      00FF3D3DH   PUBLIC    ECODE    ---       iap_write_bytes?
      00FF3C68H   PUBLIC    ECODE    ---       iap_get_cmd_state?
      00010D77H   PUBLIC    XDATA    BYTE      ?iap_read_bytes??BYTE
      00010D81H   PUBLIC    XDATA    BYTE      ?iap_write_bytes??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      000000D0H.5 SFRSYM    DATA     BIT       F0
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000C6H   SFRSYM    DATA     BYTE      IAP_TRIG
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000C2H   SFRSYM    DATA     BYTE      IAP_DATA
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F5H   SFRSYM    DATA     BYTE      IAP_TPS
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000C4H   SFRSYM    DATA     BYTE      IAP_ADDRL
      000000C3H   SFRSYM    DATA     BYTE      IAP_ADDRH
      000000F6H   SFRSYM    DATA     BYTE      IAP_ADDRE
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 39


      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000C5H   SFRSYM    DATA     BYTE      IAP_CMD
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF3C46H   BLOCK     ECODE    ---       LVL=0
      00FF3C46H   LINE      ECODE    ---       #30
      00FF3C46H   LINE      ECODE    ---       #32
      00FF3C4AH   LINE      ECODE    ---       #33
      00FF3C4EH   LINE      ECODE    ---       #34
      00FF3C52H   LINE      ECODE    ---       #37
      00FF3C53H   LINE      ECODE    ---       #38
      00FF3C54H   LINE      ECODE    ---       #39
      00FF3C55H   LINE      ECODE    ---       #40
      00FF3C56H   LINE      ECODE    ---       #42
      00FF3C5AH   LINE      ECODE    ---       #43
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3C5BH   BLOCK     ECODE    ---       LVL=0
      00FF3C5BH   LINE      ECODE    ---       #52
      00FF3C5BH   LINE      ECODE    ---       #54
      00FF3C5FH   LINE      ECODE    ---       #55
      00FF3C63H   LINE      ECODE    ---       #58
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3C64H   BLOCK     ECODE    ---       LVL=0
      00FF3C64H   LINE      ECODE    ---       #66
      00FF3C64H   LINE      ECODE    ---       #68
      00FF3C67H   LINE      ECODE    ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3C68H   BLOCK     ECODE    ---       LVL=0
      00FF3C68H   LINE      ECODE    ---       #79
      00FF3C68H   LINE      ECODE    ---       #81
      00FF3C79H   LINE      ECODE    ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3C7AH   BLOCK     ECODE    ---       LVL=0
      00010F3AH   SYMBOL    XDATA    BYTE      write_time
      00FF3C7AH   LINE      ECODE    ---       #91
      00FF3C7AH   LINE      ECODE    ---       #92
      00FF3C7AH   LINE      ECODE    ---       #94
      00FF3C96H   LINE      ECODE    ---       #95
      00FF3C9CH   LINE      ECODE    ---       #96
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3C9DH   BLOCK     ECODE    ---       LVL=0
      00010D77H   SYMBOL    XDATA    DWORD     addr
      00010D7BH   SYMBOL    XDATA    ---       buf
      00010D7FH   SYMBOL    XDATA    WORD      len
      00FF3C9DH   LINE      ECODE    ---       #109
      00FF3CB1H   LINE      ECODE    ---       #113
      00FF3CB5H   LINE      ECODE    ---       #115
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 40


      00FF3CB7H   LINE      ECODE    ---       #117
      00FF3CBAH   LINE      ECODE    ---       #118
      00FF3CC6H   LINE      ECODE    ---       #119
      00FF3CD4H   LINE      ECODE    ---       #120
      00FF3CE2H   LINE      ECODE    ---       #121
      00FF3CE6H   LINE      ECODE    ---       #122
      00FF3D0BH   LINE      ECODE    ---       #123
      00FF3D22H   LINE      ECODE    ---       #125
      00FF3D3CH   LINE      ECODE    ---       #127
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3D3DH   BLOCK     ECODE    ---       LVL=0
      00010D81H   SYMBOL    XDATA    DWORD     addr
      00010D85H   SYMBOL    XDATA    ---       buf
      00010D89H   SYMBOL    XDATA    WORD      len
      00FF3D3DH   LINE      ECODE    ---       #139
      00FF3D51H   LINE      ECODE    ---       #142
      00FF3D55H   LINE      ECODE    ---       #144
      00FF3D57H   LINE      ECODE    ---       #146
      00FF3D5AH   LINE      ECODE    ---       #147
      00FF3D66H   LINE      ECODE    ---       #148
      00FF3D74H   LINE      ECODE    ---       #149
      00FF3D82H   LINE      ECODE    ---       #150
      00FF3D9AH   LINE      ECODE    ---       #151
      00FF3DB1H   LINE      ECODE    ---       #153
      00FF3DB5H   LINE      ECODE    ---       #154
      00FF3DCCH   LINE      ECODE    ---       #156
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3DCDH   BLOCK     ECODE    ---       LVL=0
      00010EC7H   SYMBOL    XDATA    DWORD     addr
      00FF3DCDH   LINE      ECODE    ---       #167
      00FF3DD7H   LINE      ECODE    ---       #170
      00FF3DDBH   LINE      ECODE    ---       #171
      00FF3DE7H   LINE      ECODE    ---       #172
      00FF3DF5H   LINE      ECODE    ---       #173
      00FF3E03H   LINE      ECODE    ---       #174
      00FF3E07H   LINE      ECODE    ---       #177
      00FF3E0FH   LINE      ECODE    ---       #178
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_gpio
      00FED416H   PUBLIC    ECODE    ---       gpio_pull_set?
      00FED4BBH   PUBLIC    ECODE    ---       gpio_mode?
      00000094H   SFRSYM    DATA     BYTE      P0M0
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 41


      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E1H   SFRSYM    DATA     BYTE      P7M1
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000CBH   SFRSYM    DATA     BYTE      P6M1
      000000E2H   SFRSYM    DATA     BYTE      P7M0
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000CCH   SFRSYM    DATA     BYTE      P6M0
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000B3H   SFRSYM    DATA     BYTE      P4M1
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B1H   SFRSYM    DATA     BYTE      P3M1
      000000B4H   SFRSYM    DATA     BYTE      P4M0
      00000090H   SFRSYM    DATA     BYTE      P1
      00000095H   SFRSYM    DATA     BYTE      P2M1
      000000B2H   SFRSYM    DATA     BYTE      P3M0
      00000080H   SFRSYM    DATA     BYTE      P0
      00000091H   SFRSYM    DATA     BYTE      P1M1
      00000096H   SFRSYM    DATA     BYTE      P2M0
      00000092H   SFRSYM    DATA     BYTE      P1M0
      00000093H   SFRSYM    DATA     BYTE      P0M1

      00FED416H   BLOCK     ECODE    ---       LVL=0
      00010ECBH   SYMBOL    XDATA    INT       pin
      00010ECDH   SYMBOL    XDATA    INT       pull
      00FED416H   LINE      ECODE    ---       #33
      00FED422H   LINE      ECODE    ---       #35
      00FED430H   LINE      ECODE    ---       #37
      00FED46DH   LINE      ECODE    ---       #38
      00FED46FH   LINE      ECODE    ---       #39
      00FED47BH   LINE      ECODE    ---       #41
      00FED4BAH   LINE      ECODE    ---       #42
      ---         BLOCKEND  ---      ---       LVL=0

      00FED4BBH   BLOCK     ECODE    ---       LVL=0
      00010ECFH   SYMBOL    XDATA    INT       pin
      00010ED1H   SYMBOL    XDATA    INT       mode
      00FED4BBH   LINE      ECODE    ---       #54
      00FED4C7H   LINE      ECODE    ---       #56
      00FED4D6H   LINE      ECODE    ---       #58
      00FED4E8H   LINE      ECODE    ---       #60
      00FED505H   LINE      ECODE    ---       #61
      00FED522H   LINE      ECODE    ---       #62
      00FED522H   LINE      ECODE    ---       #63
      00FED536H   LINE      ECODE    ---       #65
      00FED553H   LINE      ECODE    ---       #66
      00FED570H   LINE      ECODE    ---       #67
      00FED570H   LINE      ECODE    ---       #68
      00FED584H   LINE      ECODE    ---       #70
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 42


      00FED5A1H   LINE      ECODE    ---       #71
      00FED5BEH   LINE      ECODE    ---       #72
      00FED5BEH   LINE      ECODE    ---       #74
      00FED5D2H   LINE      ECODE    ---       #76
      00FED5EFH   LINE      ECODE    ---       #77
      00FED60CH   LINE      ECODE    ---       #78
      00FED60CH   LINE      ECODE    ---       #79
      00FED620H   LINE      ECODE    ---       #81
      00FED63DH   LINE      ECODE    ---       #82
      00FED65AH   LINE      ECODE    ---       #83
      00FED65AH   LINE      ECODE    ---       #84
      00FED66EH   LINE      ECODE    ---       #86
      00FED68BH   LINE      ECODE    ---       #87
      00FED6A8H   LINE      ECODE    ---       #88
      00FED6A8H   LINE      ECODE    ---       #89
      00FED6BCH   LINE      ECODE    ---       #91
      00FED6D9H   LINE      ECODE    ---       #92
      00FED6F6H   LINE      ECODE    ---       #93
      00FED6F6H   LINE      ECODE    ---       #94
      00FED70DH   LINE      ECODE    ---       #96
      00FED72AH   LINE      ECODE    ---       #97
      00FED747H   LINE      ECODE    ---       #98
      00FED74AH   LINE      ECODE    ---       #100
      00FED75BH   LINE      ECODE    ---       #102
      00FED76DH   LINE      ECODE    ---       #104
      00FED78AH   LINE      ECODE    ---       #105
      00FED7A5H   LINE      ECODE    ---       #106
      00FED7A5H   LINE      ECODE    ---       #107
      00FED7B9H   LINE      ECODE    ---       #109
      00FED7D6H   LINE      ECODE    ---       #110
      00FED7F1H   LINE      ECODE    ---       #111
      00FED7F1H   LINE      ECODE    ---       #112
      00FED805H   LINE      ECODE    ---       #114
      00FED822H   LINE      ECODE    ---       #115
      00FED83DH   LINE      ECODE    ---       #116
      00FED83DH   LINE      ECODE    ---       #118
      00FED851H   LINE      ECODE    ---       #120
      00FED86EH   LINE      ECODE    ---       #121
      00FED889H   LINE      ECODE    ---       #122
      00FED889H   LINE      ECODE    ---       #123
      00FED89DH   LINE      ECODE    ---       #125
      00FED8BAH   LINE      ECODE    ---       #126
      00FED8D5H   LINE      ECODE    ---       #127
      00FED8D5H   LINE      ECODE    ---       #128
      00FED8E9H   LINE      ECODE    ---       #130
      00FED906H   LINE      ECODE    ---       #131
      00FED921H   LINE      ECODE    ---       #132
      00FED921H   LINE      ECODE    ---       #133
      00FED935H   LINE      ECODE    ---       #135
      00FED952H   LINE      ECODE    ---       #136
      00FED96DH   LINE      ECODE    ---       #137
      00FED96DH   LINE      ECODE    ---       #138
      00FED984H   LINE      ECODE    ---       #140
      00FED9A1H   LINE      ECODE    ---       #141
      00FED9BCH   LINE      ECODE    ---       #142
      00FED9BFH   LINE      ECODE    ---       #145
      00FED9D0H   LINE      ECODE    ---       #147
      00FED9E2H   LINE      ECODE    ---       #149
      00FED9FDH   LINE      ECODE    ---       #150
      00FEDA1AH   LINE      ECODE    ---       #151
      00FEDA1AH   LINE      ECODE    ---       #152
      00FEDA2EH   LINE      ECODE    ---       #154
      00FEDA49H   LINE      ECODE    ---       #155
      00FEDA66H   LINE      ECODE    ---       #156
      00FEDA66H   LINE      ECODE    ---       #157
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 43


      00FEDA7AH   LINE      ECODE    ---       #159
      00FEDA95H   LINE      ECODE    ---       #160
      00FEDAB2H   LINE      ECODE    ---       #161
      00FEDAB2H   LINE      ECODE    ---       #163
      00FEDAC6H   LINE      ECODE    ---       #165
      00FEDAE1H   LINE      ECODE    ---       #166
      00FEDAFEH   LINE      ECODE    ---       #167
      00FEDAFEH   LINE      ECODE    ---       #168
      00FEDB12H   LINE      ECODE    ---       #170
      00FEDB2DH   LINE      ECODE    ---       #171
      00FEDB4AH   LINE      ECODE    ---       #172
      00FEDB4AH   LINE      ECODE    ---       #173
      00FEDB5EH   LINE      ECODE    ---       #175
      00FEDB79H   LINE      ECODE    ---       #176
      00FEDB96H   LINE      ECODE    ---       #177
      00FEDB96H   LINE      ECODE    ---       #178
      00FEDBAAH   LINE      ECODE    ---       #180
      00FEDBC5H   LINE      ECODE    ---       #181
      00FEDBE2H   LINE      ECODE    ---       #182
      00FEDBE2H   LINE      ECODE    ---       #183
      00FEDBF9H   LINE      ECODE    ---       #185
      00FEDC14H   LINE      ECODE    ---       #186
      00FEDC31H   LINE      ECODE    ---       #187
      00FEDC34H   LINE      ECODE    ---       #189
      00FEDC45H   LINE      ECODE    ---       #191
      00FEDC57H   LINE      ECODE    ---       #193
      00FEDC72H   LINE      ECODE    ---       #194
      00FEDC8DH   LINE      ECODE    ---       #195
      00FEDC8DH   LINE      ECODE    ---       #196
      00FEDCA1H   LINE      ECODE    ---       #198
      00FEDCBCH   LINE      ECODE    ---       #199
      00FEDCD7H   LINE      ECODE    ---       #200
      00FEDCD7H   LINE      ECODE    ---       #201
      00FEDCEBH   LINE      ECODE    ---       #203
      00FEDD06H   LINE      ECODE    ---       #204
      00FEDD21H   LINE      ECODE    ---       #205
      00FEDD21H   LINE      ECODE    ---       #207
      00FEDD35H   LINE      ECODE    ---       #209
      00FEDD50H   LINE      ECODE    ---       #210
      00FEDD6BH   LINE      ECODE    ---       #211
      00FEDD6BH   LINE      ECODE    ---       #212
      00FEDD7FH   LINE      ECODE    ---       #214
      00FEDD9AH   LINE      ECODE    ---       #215
      00FEDDB5H   LINE      ECODE    ---       #216
      00FEDDB5H   LINE      ECODE    ---       #217
      00FEDDC9H   LINE      ECODE    ---       #219
      00FEDDE4H   LINE      ECODE    ---       #220
      00FEDDFFH   LINE      ECODE    ---       #221
      00FEDDFFH   LINE      ECODE    ---       #222
      00FEDE13H   LINE      ECODE    ---       #224
      00FEDE2EH   LINE      ECODE    ---       #225
      00FEDE49H   LINE      ECODE    ---       #226
      00FEDE49H   LINE      ECODE    ---       #227
      00FEDE5DH   LINE      ECODE    ---       #229
      00FEDE78H   LINE      ECODE    ---       #230
      00FEDE93H   LINE      ECODE    ---       #231
      00FEDE93H   LINE      ECODE    ---       #233
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_nvic
      00FF4401H   PUBLIC    ECODE    ---       NVIC_SetPriority?
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 44


      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF4401H   BLOCK     ECODE    ---       LVL=0
      00010F17H   SYMBOL    XDATA    INT       irqn
      00010F19H   SYMBOL    XDATA    BYTE      priority
      00FF4401H   LINE      ECODE    ---       #31
      00FF440BH   LINE      ECODE    ---       #33
      00FF441FH   LINE      ECODE    ---       #35
      00FF4443H   LINE      ECODE    ---       #36
      00FF446AH   LINE      ECODE    ---       #37
      00FF446CH   LINE      ECODE    ---       #38
      00FF4482H   LINE      ECODE    ---       #40
      00FF44A6H   LINE      ECODE    ---       #41
      00FF44CDH   LINE      ECODE    ---       #42
      00FF44CDH   LINE      ECODE    ---       #43
      00FF44CDH   LINE      ECODE    ---       #47
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_pwm
      00FED076H   PUBLIC    ECODE    ---       pwm_duty?
      00FEC809H   PUBLIC    ECODE    ---       pwm_set_gpio?
      00FED1DDH   PUBLIC    ECODE    ---       pwm_freq?
      00FECBEFH   PUBLIC    ECODE    ---       pwm_init?
      00FF4EC6H   PUBLIC    HCONST   ---       PWM_CCR_ADDR
      00FF4EE6H   PUBLIC    HCONST   ---       PWM_ARR_ADDR
      00FF4EEEH   PUBLIC    HCONST   ---       PWM_CCER_ADDR
      00FF4EFEH   PUBLIC    HCONST   ---       PWM_CCMR_ADDR
      00010BCEH   PUBLIC    XDATA    BYTE      ?pwm_init??BYTE
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 45


      00010BE2H   PUBLIC    XDATA    BYTE      ?pwm_freq??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FEC809H   BLOCK     ECODE    ---       LVL=0
      00010F1DH   SYMBOL    XDATA    INT       pwmch
      00FEC809H   LINE      ECODE    ---       #49
      00FEC80FH   LINE      ECODE    ---       #51
      00FEC986H   LINE      ECODE    ---       #53
      00FEC986H   LINE      ECODE    ---       #55
      00FEC992H   LINE      ECODE    ---       #56
      00FEC995H   LINE      ECODE    ---       #58
      00FEC995H   LINE      ECODE    ---       #60
      00FEC9A1H   LINE      ECODE    ---       #61
      00FEC9A4H   LINE      ECODE    ---       #63
      00FEC9A4H   LINE      ECODE    ---       #65
      00FEC9B0H   LINE      ECODE    ---       #66
      00FEC9B3H   LINE      ECODE    ---       #68
      00FEC9B3H   LINE      ECODE    ---       #70
      00FEC9BFH   LINE      ECODE    ---       #71
      00FEC9C2H   LINE      ECODE    ---       #73
      00FEC9C2H   LINE      ECODE    ---       #75
      00FEC9CEH   LINE      ECODE    ---       #76
      00FEC9D1H   LINE      ECODE    ---       #78
      00FEC9D1H   LINE      ECODE    ---       #80
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 46


      00FEC9DDH   LINE      ECODE    ---       #81
      00FEC9E0H   LINE      ECODE    ---       #84
      00FEC9E0H   LINE      ECODE    ---       #86
      00FEC9ECH   LINE      ECODE    ---       #87
      00FEC9EFH   LINE      ECODE    ---       #89
      00FEC9EFH   LINE      ECODE    ---       #91
      00FEC9FBH   LINE      ECODE    ---       #92
      00FEC9FEH   LINE      ECODE    ---       #94
      00FEC9FEH   LINE      ECODE    ---       #96
      00FECA0AH   LINE      ECODE    ---       #97
      00FECA0DH   LINE      ECODE    ---       #99
      00FECA0DH   LINE      ECODE    ---       #101
      00FECA19H   LINE      ECODE    ---       #102
      00FECA1CH   LINE      ECODE    ---       #104
      00FECA1CH   LINE      ECODE    ---       #106
      00FECA28H   LINE      ECODE    ---       #107
      00FECA2BH   LINE      ECODE    ---       #109
      00FECA2BH   LINE      ECODE    ---       #111
      00FECA37H   LINE      ECODE    ---       #112
      00FECA3AH   LINE      ECODE    ---       #115
      00FECA3AH   LINE      ECODE    ---       #117
      00FECA46H   LINE      ECODE    ---       #118
      00FECA49H   LINE      ECODE    ---       #120
      00FECA49H   LINE      ECODE    ---       #122
      00FECA55H   LINE      ECODE    ---       #123
      00FECA58H   LINE      ECODE    ---       #125
      00FECA58H   LINE      ECODE    ---       #127
      00FECA64H   LINE      ECODE    ---       #128
      00FECA67H   LINE      ECODE    ---       #130
      00FECA67H   LINE      ECODE    ---       #132
      00FECA73H   LINE      ECODE    ---       #133
      00FECA76H   LINE      ECODE    ---       #135
      00FECA76H   LINE      ECODE    ---       #137
      00FECA82H   LINE      ECODE    ---       #138
      00FECA85H   LINE      ECODE    ---       #140
      00FECA85H   LINE      ECODE    ---       #142
      00FECA91H   LINE      ECODE    ---       #143
      00FECA94H   LINE      ECODE    ---       #147
      00FECA94H   LINE      ECODE    ---       #149
      00FECAA0H   LINE      ECODE    ---       #150
      00FECAA3H   LINE      ECODE    ---       #152
      00FECAA3H   LINE      ECODE    ---       #154
      00FECAAFH   LINE      ECODE    ---       #155
      00FECAB2H   LINE      ECODE    ---       #157
      00FECAB2H   LINE      ECODE    ---       #159
      00FECABEH   LINE      ECODE    ---       #160
      00FECAC1H   LINE      ECODE    ---       #162
      00FECAC1H   LINE      ECODE    ---       #164
      00FECACDH   LINE      ECODE    ---       #165
      00FECAD0H   LINE      ECODE    ---       #167
      00FECAD0H   LINE      ECODE    ---       #169
      00FECADCH   LINE      ECODE    ---       #170
      00FECADFH   LINE      ECODE    ---       #172
      00FECADFH   LINE      ECODE    ---       #174
      00FECAEBH   LINE      ECODE    ---       #175
      00FECAEEH   LINE      ECODE    ---       #177
      00FECAEEH   LINE      ECODE    ---       #179
      00FECAFAH   LINE      ECODE    ---       #180
      00FECAFDH   LINE      ECODE    ---       #182
      00FECAFDH   LINE      ECODE    ---       #184
      00FECB09H   LINE      ECODE    ---       #185
      00FECB0CH   LINE      ECODE    ---       #189
      00FECB0CH   LINE      ECODE    ---       #191
      00FECB18H   LINE      ECODE    ---       #192
      00FECB1BH   LINE      ECODE    ---       #194
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 47


      00FECB1BH   LINE      ECODE    ---       #196
      00FECB27H   LINE      ECODE    ---       #197
      00FECB2AH   LINE      ECODE    ---       #199
      00FECB2AH   LINE      ECODE    ---       #201
      00FECB34H   LINE      ECODE    ---       #202
      00FECB37H   LINE      ECODE    ---       #204
      00FECB37H   LINE      ECODE    ---       #206
      00FECB43H   LINE      ECODE    ---       #207
      00FECB46H   LINE      ECODE    ---       #210
      00FECB46H   LINE      ECODE    ---       #212
      00FECB52H   LINE      ECODE    ---       #213
      00FECB55H   LINE      ECODE    ---       #215
      00FECB55H   LINE      ECODE    ---       #217
      00FECB61H   LINE      ECODE    ---       #218
      00FECB64H   LINE      ECODE    ---       #220
      00FECB64H   LINE      ECODE    ---       #222
      00FECB70H   LINE      ECODE    ---       #223
      00FECB72H   LINE      ECODE    ---       #225
      00FECB72H   LINE      ECODE    ---       #227
      00FECB7EH   LINE      ECODE    ---       #228
      00FECB80H   LINE      ECODE    ---       #232
      00FECB80H   LINE      ECODE    ---       #234
      00FECB8CH   LINE      ECODE    ---       #235
      00FECB8EH   LINE      ECODE    ---       #237
      00FECB8EH   LINE      ECODE    ---       #239
      00FECB9AH   LINE      ECODE    ---       #240
      00FECB9CH   LINE      ECODE    ---       #242
      00FECB9CH   LINE      ECODE    ---       #244
      00FECBA8H   LINE      ECODE    ---       #245
      00FECBAAH   LINE      ECODE    ---       #247
      00FECBAAH   LINE      ECODE    ---       #249
      00FECBB6H   LINE      ECODE    ---       #250
      00FECBB8H   LINE      ECODE    ---       #254
      00FECBB8H   LINE      ECODE    ---       #256
      00FECBC4H   LINE      ECODE    ---       #257
      00FECBC6H   LINE      ECODE    ---       #259
      00FECBC6H   LINE      ECODE    ---       #261
      00FECBD2H   LINE      ECODE    ---       #262
      00FECBD4H   LINE      ECODE    ---       #264
      00FECBD4H   LINE      ECODE    ---       #266
      00FECBE0H   LINE      ECODE    ---       #267
      00FECBE2H   LINE      ECODE    ---       #269
      00FECBE2H   LINE      ECODE    ---       #271
      00FECBEEH   LINE      ECODE    ---       #272
      00FECBEEH   LINE      ECODE    ---       #275
      00FECBEEH   LINE      ECODE    ---       #277
      ---         BLOCKEND  ---      ---       LVL=0

      00FECBEFH   BLOCK     ECODE    ---       LVL=0
      00010BCEH   SYMBOL    XDATA    INT       pwmch
      00010BD0H   SYMBOL    XDATA    DWORD     freq
      00010BD4H   SYMBOL    XDATA    DWORD     duty
      00FECBFFH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010BD8H   SYMBOL    XDATA    DWORD     match_temp
      00010BDCH   SYMBOL    XDATA    DWORD     period_temp
      00010BE0H   SYMBOL    XDATA    WORD      freq_div
      ---         BLOCKEND  ---      ---       LVL=1
      00FECBEFH   LINE      ECODE    ---       #290
      00FECBFFH   LINE      ECODE    ---       #291
      00FECBFFH   LINE      ECODE    ---       #295
      00FECC07H   LINE      ECODE    ---       #298
      00FECC0AH   LINE      ECODE    ---       #301
      00FECC14H   LINE      ECODE    ---       #305
      00FECC36H   LINE      ECODE    ---       #306
      00FECC58H   LINE      ECODE    ---       #307
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 48


      00FECC82H   LINE      ECODE    ---       #309
      00FECC92H   LINE      ECODE    ---       #311
      00FECCD0H   LINE      ECODE    ---       #312
      00FECCD2H   LINE      ECODE    ---       #315
      00FECCECH   LINE      ECODE    ---       #316
      00FECCECH   LINE      ECODE    ---       #319
      00FECCFDH   LINE      ECODE    ---       #322
      00FECD30H   LINE      ECODE    ---       #323
      00FECD6BH   LINE      ECODE    ---       #326
      00FECDC1H   LINE      ECODE    ---       #329
      00FECDD6H   LINE      ECODE    ---       #330
      00FECDE7H   LINE      ECODE    ---       #332
      00FECDF4H   LINE      ECODE    ---       #333
      00FECE01H   LINE      ECODE    ---       #334
      00FECE04H   LINE      ECODE    ---       #337
      00FECE46H   LINE      ECODE    ---       #338
      00FECE7DH   LINE      ECODE    ---       #341
      00FECEE3H   LINE      ECODE    ---       #345
      00FECEF8H   LINE      ECODE    ---       #346
      00FECF09H   LINE      ECODE    ---       #348
      00FECF16H   LINE      ECODE    ---       #349
      00FECF23H   LINE      ECODE    ---       #350
      00FECF23H   LINE      ECODE    ---       #353
      00FECF5DH   LINE      ECODE    ---       #354
      00FECF9DH   LINE      ECODE    ---       #357
      00FECFD3H   LINE      ECODE    ---       #358
      00FED00FH   LINE      ECODE    ---       #361
      00FED042H   LINE      ECODE    ---       #362
      00FED075H   LINE      ECODE    ---       #367
      ---         BLOCKEND  ---      ---       LVL=0

      00FED076H   BLOCK     ECODE    ---       LVL=0
      00010C91H   SYMBOL    XDATA    INT       pwmch
      00010C93H   SYMBOL    XDATA    DWORD     duty
      00FED086H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010C97H   SYMBOL    XDATA    DWORD     match_temp
      00010C9BH   SYMBOL    XDATA    DWORD     arr
      ---         BLOCKEND  ---      ---       LVL=1
      00FED076H   LINE      ECODE    ---       #377
      00FED086H   LINE      ECODE    ---       #378
      00FED086H   LINE      ECODE    ---       #380
      00FED100H   LINE      ECODE    ---       #384
      00FED110H   LINE      ECODE    ---       #386
      00FED14EH   LINE      ECODE    ---       #387
      00FED150H   LINE      ECODE    ---       #390
      00FED16AH   LINE      ECODE    ---       #391
      00FED16AH   LINE      ECODE    ---       #396
      00FED1A0H   LINE      ECODE    ---       #397
      00FED1DCH   LINE      ECODE    ---       #401
      ---         BLOCKEND  ---      ---       LVL=0

      00FED1DDH   BLOCK     ECODE    ---       LVL=0
      00010BE2H   SYMBOL    XDATA    INT       pwmch
      00010BE4H   SYMBOL    XDATA    DWORD     freq
      00010BE8H   SYMBOL    XDATA    DWORD     duty
      00FED1EDH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010BECH   SYMBOL    XDATA    DWORD     match_temp
      00010BF0H   SYMBOL    XDATA    DWORD     period_temp
      00010BF4H   SYMBOL    XDATA    WORD      freq_div
      ---         BLOCKEND  ---      ---       LVL=1
      00FED1DDH   LINE      ECODE    ---       #412
      00FED1EDH   LINE      ECODE    ---       #413
      00FED1EDH   LINE      ECODE    ---       #416
      00FED1F5H   LINE      ECODE    ---       #421
      00FED217H   LINE      ECODE    ---       #422
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 49


      00FED239H   LINE      ECODE    ---       #423
      00FED263H   LINE      ECODE    ---       #425
      00FED273H   LINE      ECODE    ---       #427
      00FED2B1H   LINE      ECODE    ---       #428
      00FED2B3H   LINE      ECODE    ---       #431
      00FED2CDH   LINE      ECODE    ---       #432
      00FED2CDH   LINE      ECODE    ---       #439
      00FED2DBH   LINE      ECODE    ---       #442
      00FED2F0H   LINE      ECODE    ---       #443
      00FED301H   LINE      ECODE    ---       #444
      00FED303H   LINE      ECODE    ---       #448
      00FED318H   LINE      ECODE    ---       #449
      00FED329H   LINE      ECODE    ---       #450
      00FED329H   LINE      ECODE    ---       #453
      00FED363H   LINE      ECODE    ---       #454
      00FED3A3H   LINE      ECODE    ---       #457
      00FED3D9H   LINE      ECODE    ---       #458
      00FED415H   LINE      ECODE    ---       #461
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_spi
      00FF3C20H   PUBLIC    ECODE    ---       spi_change_mode?
      00FF3B42H   PUBLIC    ECODE    ---       spi_change_pin?
      00FF3A41H   PUBLIC    ECODE    ---       spi_init?
      00FF3B2CH   PUBLIC    ECODE    ---       spi_mosi?
      00010CD7H   PUBLIC    XDATA    BYTE      ?spi_init??BYTE
      000000CFH   SFRSYM    DATA     BYTE      SPDAT
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 50


      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF3A41H   BLOCK     ECODE    ---       LVL=0
      00010CD7H   SYMBOL    XDATA    INT       spi_n
      00010CD9H   SYMBOL    XDATA    INT       sck_pin
      00010CDBH   SYMBOL    XDATA    INT       mosi_pin
      00010CDDH   SYMBOL    XDATA    INT       miso_pin
      00010CDFH   SYMBOL    XDATA    BYTE      mode
      00010CE0H   SYMBOL    XDATA    INT       mstr
      00010CE2H   SYMBOL    XDATA    INT       baud
      00FF3A41H   LINE      ECODE    ---       #39
      00FF3A5DH   LINE      ECODE    ---       #48
      00FF3A6BH   LINE      ECODE    ---       #50
      00FF3A7DH   LINE      ECODE    ---       #51
      00FF3A7DH   LINE      ECODE    ---       #53
      00FF3A8BH   LINE      ECODE    ---       #55
      00FF3A9DH   LINE      ECODE    ---       #56
      00FF3A9DH   LINE      ECODE    ---       #58
      00FF3AABH   LINE      ECODE    ---       #60
      00FF3ABDH   LINE      ECODE    ---       #61
      00FF3ABDH   LINE      ECODE    ---       #64
      00FF3AC0H   LINE      ECODE    ---       #65
      00FF3AD8H   LINE      ECODE    ---       #67
      00FF3AD8H   LINE      ECODE    ---       #68
      00FF3ADCH   LINE      ECODE    ---       #69
      00FF3ADEH   LINE      ECODE    ---       #70
      00FF3ADEH   LINE      ECODE    ---       #71
      00FF3AE1H   LINE      ECODE    ---       #72
      00FF3AE3H   LINE      ECODE    ---       #73
      00FF3AE3H   LINE      ECODE    ---       #74
      00FF3AE6H   LINE      ECODE    ---       #75
      00FF3AE8H   LINE      ECODE    ---       #76
      00FF3AE8H   LINE      ECODE    ---       #77
      00FF3AEBH   LINE      ECODE    ---       #78
      00FF3AEBH   LINE      ECODE    ---       #79
      00FF3AEBH   LINE      ECODE    ---       #81
      00FF3AEEH   LINE      ECODE    ---       #82
      00FF3AFDH   LINE      ECODE    ---       #84
      00FF3AFDH   LINE      ECODE    ---       #86
      00FF3AFFH   LINE      ECODE    ---       #87
      00FF3AFFH   LINE      ECODE    ---       #88
      00FF3B02H   LINE      ECODE    ---       #89
      00FF3B04H   LINE      ECODE    ---       #90
      00FF3B04H   LINE      ECODE    ---       #91
      00FF3B07H   LINE      ECODE    ---       #92
      00FF3B09H   LINE      ECODE    ---       #93
      00FF3B09H   LINE      ECODE    ---       #94
      00FF3B0CH   LINE      ECODE    ---       #95
      00FF3B0CH   LINE      ECODE    ---       #96
      00FF3B0CH   LINE      ECODE    ---       #99
      00FF3B16H   LINE      ECODE    ---       #101
      00FF3B22H   LINE      ECODE    ---       #103
      00FF3B25H   LINE      ECODE    ---       #104
      00FF3B28H   LINE      ECODE    ---       #105
      00FF3B28H   LINE      ECODE    ---       #109
      00FF3B28H   LINE      ECODE    ---       #110
      00FF3B2BH   LINE      ECODE    ---       #111
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3B2CH   BLOCK     ECODE    ---       LVL=0
      00010F3BH   SYMBOL    XDATA    BYTE      dat
      00FF3B2CH   LINE      ECODE    ---       #121
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 51


      00FF3B30H   LINE      ECODE    ---       #123
      00FF3B36H   LINE      ECODE    ---       #124
      00FF3B3BH   LINE      ECODE    ---       #125
      00FF3B3FH   LINE      ECODE    ---       #126
      00FF3B41H   LINE      ECODE    ---       #127
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3B42H   BLOCK     ECODE    ---       LVL=0
      00010F1FH   SYMBOL    XDATA    INT       spi_n
      00FF3B42H   LINE      ECODE    ---       #139
      00FF3B48H   LINE      ECODE    ---       #141
      00FF3B4BH   LINE      ECODE    ---       #142
      00FF3B66H   LINE      ECODE    ---       #145
      00FF3B66H   LINE      ECODE    ---       #146
      00FF3B72H   LINE      ECODE    ---       #147
      00FF3B7CH   LINE      ECODE    ---       #148
      00FF3B88H   LINE      ECODE    ---       #149
      00FF3B8AH   LINE      ECODE    ---       #150
      00FF3B8AH   LINE      ECODE    ---       #151
      00FF3B96H   LINE      ECODE    ---       #152
      00FF3BA0H   LINE      ECODE    ---       #153
      00FF3BACH   LINE      ECODE    ---       #154
      00FF3BAEH   LINE      ECODE    ---       #155
      00FF3BAEH   LINE      ECODE    ---       #156
      00FF3BBAH   LINE      ECODE    ---       #157
      00FF3BC4H   LINE      ECODE    ---       #158
      00FF3BD0H   LINE      ECODE    ---       #159
      00FF3BD2H   LINE      ECODE    ---       #160
      00FF3BD2H   LINE      ECODE    ---       #161
      00FF3BDEH   LINE      ECODE    ---       #162
      00FF3BE8H   LINE      ECODE    ---       #163
      00FF3BF4H   LINE      ECODE    ---       #164
      00FF3BF4H   LINE      ECODE    ---       #165
      00FF3BF4H   LINE      ECODE    ---       #168
      00FF3C0CH   LINE      ECODE    ---       #170
      00FF3C0CH   LINE      ECODE    ---       #171
      00FF3C10H   LINE      ECODE    ---       #172
      00FF3C12H   LINE      ECODE    ---       #173
      00FF3C12H   LINE      ECODE    ---       #174
      00FF3C15H   LINE      ECODE    ---       #175
      00FF3C17H   LINE      ECODE    ---       #176
      00FF3C17H   LINE      ECODE    ---       #177
      00FF3C1AH   LINE      ECODE    ---       #178
      00FF3C1CH   LINE      ECODE    ---       #179
      00FF3C1CH   LINE      ECODE    ---       #180
      00FF3C1FH   LINE      ECODE    ---       #181
      00FF3C1FH   LINE      ECODE    ---       #182
      00FF3C1FH   LINE      ECODE    ---       #183
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3C20H   BLOCK     ECODE    ---       LVL=0
      00010F3CH   SYMBOL    XDATA    BYTE      mode
      00FF3C20H   LINE      ECODE    ---       #192
      00FF3C24H   LINE      ECODE    ---       #194
      00FF3C27H   LINE      ECODE    ---       #195
      00FF3C36H   LINE      ECODE    ---       #197
      00FF3C36H   LINE      ECODE    ---       #199
      00FF3C38H   LINE      ECODE    ---       #200
      00FF3C38H   LINE      ECODE    ---       #201
      00FF3C3BH   LINE      ECODE    ---       #202
      00FF3C3DH   LINE      ECODE    ---       #203
      00FF3C3DH   LINE      ECODE    ---       #204
      00FF3C40H   LINE      ECODE    ---       #205
      00FF3C42H   LINE      ECODE    ---       #206
      00FF3C42H   LINE      ECODE    ---       #207
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 52


      00FF3C45H   LINE      ECODE    ---       #208
      00FF3C45H   LINE      ECODE    ---       #209
      00FF3C45H   LINE      ECODE    ---       #210
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_tim
      00FF32E3H   PUBLIC    ECODE    ---       ctimer_count_read?
      00FF3426H   PUBLIC    ECODE    ---       pit_timer_ms?
      00FF3287H   PUBLIC    ECODE    ---       ctimer_count_init?
      00FF33C1H   PUBLIC    ECODE    ---       ctimer_count_clean?
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 53


      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF3287H   BLOCK     ECODE    ---       LVL=0
      00010F21H   SYMBOL    XDATA    INT       tim_n
      00FF3287H   LINE      ECODE    ---       #39
      00FF328DH   LINE      ECODE    ---       #42
      00FF32A7H   LINE      ECODE    ---       #44
      00FF32A7H   LINE      ECODE    ---       #46
      00FF32AAH   LINE      ECODE    ---       #47
      00FF32ADH   LINE      ECODE    ---       #48
      00FF32B0H   LINE      ECODE    ---       #49
      00FF32B3H   LINE      ECODE    ---       #50
      00FF32B5H   LINE      ECODE    ---       #53
      00FF32B5H   LINE      ECODE    ---       #55
      00FF32B8H   LINE      ECODE    ---       #56
      00FF32BBH   LINE      ECODE    ---       #57
      00FF32BEH   LINE      ECODE    ---       #58
      00FF32C1H   LINE      ECODE    ---       #59
      00FF32C3H   LINE      ECODE    ---       #62
      00FF32C3H   LINE      ECODE    ---       #64
      00FF32C6H   LINE      ECODE    ---       #65
      00FF32C9H   LINE      ECODE    ---       #66
      00FF32CCH   LINE      ECODE    ---       #67
      00FF32CEH   LINE      ECODE    ---       #70
      00FF32CEH   LINE      ECODE    ---       #72
      00FF32D1H   LINE      ECODE    ---       #73
      00FF32D4H   LINE      ECODE    ---       #74
      00FF32D7H   LINE      ECODE    ---       #75
      00FF32D9H   LINE      ECODE    ---       #78
      00FF32D9H   LINE      ECODE    ---       #80
      00FF32DCH   LINE      ECODE    ---       #81
      00FF32DFH   LINE      ECODE    ---       #82
      00FF32E2H   LINE      ECODE    ---       #83
      00FF32E2H   LINE      ECODE    ---       #86
      00FF32E2H   LINE      ECODE    ---       #87
      ---         BLOCKEND  ---      ---       LVL=0

      00FF32E3H   BLOCK     ECODE    ---       LVL=0
      00010ED3H   SYMBOL    XDATA    INT       tim_n
      00FF32E9H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010ED5H   SYMBOL    XDATA    WORD      dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FF32E3H   LINE      ECODE    ---       #95
      00FF32E9H   LINE      ECODE    ---       #96
      00FF32E9H   LINE      ECODE    ---       #97
      00FF32F1H   LINE      ECODE    ---       #99
      00FF3311H   LINE      ECODE    ---       #101
      00FF3311H   LINE      ECODE    ---       #103
      00FF331FH   LINE      ECODE    ---       #104
      00FF3331H   LINE      ECODE    ---       #105
      00FF3334H   LINE      ECODE    ---       #107
      00FF3334H   LINE      ECODE    ---       #109
      00FF3342H   LINE      ECODE    ---       #110
      00FF3354H   LINE      ECODE    ---       #111
      00FF3356H   LINE      ECODE    ---       #113
      00FF3356H   LINE      ECODE    ---       #115
      00FF3364H   LINE      ECODE    ---       #116
      00FF3376H   LINE      ECODE    ---       #117
      00FF3378H   LINE      ECODE    ---       #119
      00FF3378H   LINE      ECODE    ---       #121
      00FF3386H   LINE      ECODE    ---       #122
      00FF3398H   LINE      ECODE    ---       #123
      00FF339AH   LINE      ECODE    ---       #125
      00FF339AH   LINE      ECODE    ---       #127
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 54


      00FF33A8H   LINE      ECODE    ---       #128
      00FF33BAH   LINE      ECODE    ---       #129
      00FF33BAH   LINE      ECODE    ---       #132
      00FF33BAH   LINE      ECODE    ---       #133
      00FF33C0H   LINE      ECODE    ---       #134
      ---         BLOCKEND  ---      ---       LVL=0

      00FF33C1H   BLOCK     ECODE    ---       LVL=0
      00010F23H   SYMBOL    XDATA    INT       tim_n
      00FF33C1H   LINE      ECODE    ---       #143
      00FF33C7H   LINE      ECODE    ---       #145
      00FF33E1H   LINE      ECODE    ---       #147
      00FF33E1H   LINE      ECODE    ---       #149
      00FF33E4H   LINE      ECODE    ---       #150
      00FF33E7H   LINE      ECODE    ---       #151
      00FF33EAH   LINE      ECODE    ---       #152
      00FF33EDH   LINE      ECODE    ---       #153
      00FF33EFH   LINE      ECODE    ---       #155
      00FF33EFH   LINE      ECODE    ---       #157
      00FF33F2H   LINE      ECODE    ---       #158
      00FF33F5H   LINE      ECODE    ---       #159
      00FF33F8H   LINE      ECODE    ---       #160
      00FF33FBH   LINE      ECODE    ---       #161
      00FF33FDH   LINE      ECODE    ---       #163
      00FF33FDH   LINE      ECODE    ---       #165
      00FF3400H   LINE      ECODE    ---       #166
      00FF3403H   LINE      ECODE    ---       #167
      00FF3406H   LINE      ECODE    ---       #168
      00FF3409H   LINE      ECODE    ---       #169
      00FF340BH   LINE      ECODE    ---       #171
      00FF340BH   LINE      ECODE    ---       #173
      00FF340EH   LINE      ECODE    ---       #174
      00FF3411H   LINE      ECODE    ---       #175
      00FF3414H   LINE      ECODE    ---       #176
      00FF3417H   LINE      ECODE    ---       #177
      00FF3419H   LINE      ECODE    ---       #179
      00FF3419H   LINE      ECODE    ---       #181
      00FF341CH   LINE      ECODE    ---       #182
      00FF341FH   LINE      ECODE    ---       #183
      00FF3422H   LINE      ECODE    ---       #184
      00FF3425H   LINE      ECODE    ---       #185
      00FF3425H   LINE      ECODE    ---       #187
      00FF3425H   LINE      ECODE    ---       #188
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3426H   BLOCK     ECODE    ---       LVL=0
      00010E63H   SYMBOL    XDATA    INT       tim_n
      00010E65H   SYMBOL    XDATA    WORD      time_ms
      00FF3432H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E67H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FF3426H   LINE      ECODE    ---       #198
      00FF3432H   LINE      ECODE    ---       #199
      00FF3432H   LINE      ECODE    ---       #201
      00FF3460H   LINE      ECODE    ---       #203
      00FF346CH   LINE      ECODE    ---       #205
      00FF3470H   LINE      ECODE    ---       #206
      00FF3478H   LINE      ECODE    ---       #207
      00FF3484H   LINE      ECODE    ---       #208
      00FF3487H   LINE      ECODE    ---       #209
      00FF348AH   LINE      ECODE    ---       #210
      00FF348DH   LINE      ECODE    ---       #211
      00FF349BH   LINE      ECODE    ---       #213
      00FF349FH   LINE      ECODE    ---       #214
      00FF34A7H   LINE      ECODE    ---       #215
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 55


      00FF34B3H   LINE      ECODE    ---       #216
      00FF34B6H   LINE      ECODE    ---       #217
      00FF34B9H   LINE      ECODE    ---       #218
      00FF34BBH   LINE      ECODE    ---       #219
      00FF34C9H   LINE      ECODE    ---       #221
      00FF34D1H   LINE      ECODE    ---       #222
      00FF34DDH   LINE      ECODE    ---       #223
      00FF34E0H   LINE      ECODE    ---       #224
      00FF34E3H   LINE      ECODE    ---       #225
      00FF34E5H   LINE      ECODE    ---       #226
      00FF34F3H   LINE      ECODE    ---       #228
      00FF34FBH   LINE      ECODE    ---       #229
      00FF3507H   LINE      ECODE    ---       #230
      00FF350AH   LINE      ECODE    ---       #231
      00FF350DH   LINE      ECODE    ---       #232
      00FF350FH   LINE      ECODE    ---       #233
      00FF351DH   LINE      ECODE    ---       #235
      00FF3525H   LINE      ECODE    ---       #236
      00FF3531H   LINE      ECODE    ---       #237
      00FF3534H   LINE      ECODE    ---       #238
      00FF3537H   LINE      ECODE    ---       #239
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_uart
      00FF2A91H   PUBLIC    ECODE    ---       uart_putchar?
      00FF2B0EH   PUBLIC    ECODE    ---       uart_putbuff?
      00FF2B5EH   PUBLIC    ECODE    ---       uart_putstr?
      00FF2774H   PUBLIC    ECODE    ---       uart_init?
      00010E99H   PUBLIC    XDATA    ---       busy
      00010C9FH   PUBLIC    XDATA    BYTE      ?uart_init??BYTE
      00010D8BH   PUBLIC    XDATA    BYTE      ?uart_putbuff??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000FEH   SFRSYM    DATA     BYTE      S4BUF
      000000ADH   SFRSYM    DATA     BYTE      S3BUF
      0000009BH   SFRSYM    DATA     BYTE      S2BUF
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 56


      00000087H   SFRSYM    DATA     BYTE      PCON
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000A8H.4 SFRSYM    DATA     BIT       ES
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000C9H   SFRSYM    DATA     BYTE      P5M1
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000CAH   SFRSYM    DATA     BYTE      P5M0
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF2774H   BLOCK     ECODE    ---       LVL=0
      00010C9FH   SYMBOL    XDATA    INT       uart_n
      00010CA1H   SYMBOL    XDATA    INT       uart_rx_pin
      00010CA3H   SYMBOL    XDATA    INT       uart_tx_pin
      00010CA5H   SYMBOL    XDATA    DWORD     baud
      00010CA9H   SYMBOL    XDATA    INT       tim_n
      00FF278CH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010CABH   SYMBOL    XDATA    WORD      brt
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2774H   LINE      ECODE    ---       #42
      00FF278CH   LINE      ECODE    ---       #43
      00FF278CH   LINE      ECODE    ---       #46
      00FF27C2H   LINE      ECODE    ---       #49
      00FF27E6H   LINE      ECODE    ---       #51
      00FF27E6H   LINE      ECODE    ---       #53
      00FF27F4H   LINE      ECODE    ---       #55
      00FF27F7H   LINE      ECODE    ---       #56
      00FF27FBH   LINE      ECODE    ---       #57
      00FF2803H   LINE      ECODE    ---       #58
      00FF280FH   LINE      ECODE    ---       #59
      00FF2812H   LINE      ECODE    ---       #60
      00FF2815H   LINE      ECODE    ---       #61
      00FF281AH   LINE      ECODE    ---       #62
      00FF281CH   LINE      ECODE    ---       #63
      00FF282AH   LINE      ECODE    ---       #65
      00FF282DH   LINE      ECODE    ---       #66
      00FF2835H   LINE      ECODE    ---       #67
      00FF2841H   LINE      ECODE    ---       #68
      00FF2844H   LINE      ECODE    ---       #69
      00FF2844H   LINE      ECODE    ---       #70
      00FF2847H   LINE      ECODE    ---       #71
      00FF2861H   LINE      ECODE    ---       #73
      00FF2865H   LINE      ECODE    ---       #74
      00FF2867H   LINE      ECODE    ---       #75
      00FF2883H   LINE      ECODE    ---       #77
      00FF2886H   LINE      ECODE    ---       #78
      00FF2888H   LINE      ECODE    ---       #79
      00FF28A4H   LINE      ECODE    ---       #81
      00FF28A7H   LINE      ECODE    ---       #82
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 57


      00FF28A9H   LINE      ECODE    ---       #83
      00FF28C5H   LINE      ECODE    ---       #85
      00FF28C8H   LINE      ECODE    ---       #86
      00FF28C8H   LINE      ECODE    ---       #87
      00FF28CDH   LINE      ECODE    ---       #88
      00FF28D0H   LINE      ECODE    ---       #89
      00FF28D3H   LINE      ECODE    ---       #92
      00FF28D3H   LINE      ECODE    ---       #94
      00FF28E1H   LINE      ECODE    ---       #96
      00FF28E4H   LINE      ECODE    ---       #97
      00FF28ECH   LINE      ECODE    ---       #98
      00FF28F8H   LINE      ECODE    ---       #99
      00FF28FBH   LINE      ECODE    ---       #100
      00FF28FBH   LINE      ECODE    ---       #102
      00FF28FEH   LINE      ECODE    ---       #103
      00FF291AH   LINE      ECODE    ---       #105
      00FF291EH   LINE      ECODE    ---       #106
      00FF2920H   LINE      ECODE    ---       #107
      00FF293CH   LINE      ECODE    ---       #109
      00FF293FH   LINE      ECODE    ---       #110
      00FF293FH   LINE      ECODE    ---       #112
      00FF2942H   LINE      ECODE    ---       #113
      00FF2947H   LINE      ECODE    ---       #114
      00FF294AH   LINE      ECODE    ---       #117
      00FF294AH   LINE      ECODE    ---       #119
      00FF2958H   LINE      ECODE    ---       #121
      00FF295BH   LINE      ECODE    ---       #122
      00FF2963H   LINE      ECODE    ---       #123
      00FF296FH   LINE      ECODE    ---       #124
      00FF2972H   LINE      ECODE    ---       #125
      00FF2974H   LINE      ECODE    ---       #126
      00FF2982H   LINE      ECODE    ---       #128
      00FF2985H   LINE      ECODE    ---       #129
      00FF298DH   LINE      ECODE    ---       #130
      00FF2999H   LINE      ECODE    ---       #131
      00FF299CH   LINE      ECODE    ---       #132
      00FF299CH   LINE      ECODE    ---       #134
      00FF299FH   LINE      ECODE    ---       #135
      00FF29BBH   LINE      ECODE    ---       #137
      00FF29BFH   LINE      ECODE    ---       #138
      00FF29C1H   LINE      ECODE    ---       #139
      00FF29DDH   LINE      ECODE    ---       #141
      00FF29E0H   LINE      ECODE    ---       #142
      00FF29E0H   LINE      ECODE    ---       #144
      00FF29E3H   LINE      ECODE    ---       #145
      00FF29E8H   LINE      ECODE    ---       #146
      00FF29EBH   LINE      ECODE    ---       #149
      00FF29EBH   LINE      ECODE    ---       #151
      00FF29F9H   LINE      ECODE    ---       #153
      00FF29FCH   LINE      ECODE    ---       #154
      00FF2A04H   LINE      ECODE    ---       #155
      00FF2A10H   LINE      ECODE    ---       #156
      00FF2A13H   LINE      ECODE    ---       #157
      00FF2A15H   LINE      ECODE    ---       #158
      00FF2A23H   LINE      ECODE    ---       #160
      00FF2A26H   LINE      ECODE    ---       #161
      00FF2A2EH   LINE      ECODE    ---       #162
      00FF2A3AH   LINE      ECODE    ---       #163
      00FF2A3DH   LINE      ECODE    ---       #164
      00FF2A3DH   LINE      ECODE    ---       #166
      00FF2A40H   LINE      ECODE    ---       #167
      00FF2A5CH   LINE      ECODE    ---       #169
      00FF2A60H   LINE      ECODE    ---       #170
      00FF2A62H   LINE      ECODE    ---       #171
      00FF2A7EH   LINE      ECODE    ---       #173
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 58


      00FF2A81H   LINE      ECODE    ---       #174
      00FF2A85H   LINE      ECODE    ---       #175
      00FF2A88H   LINE      ECODE    ---       #176
      00FF2A88H   LINE      ECODE    ---       #177
      00FF2A8BH   LINE      ECODE    ---       #178
      00FF2A90H   LINE      ECODE    ---       #179
      00FF2A90H   LINE      ECODE    ---       #182
      00FF2A90H   LINE      ECODE    ---       #184
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2A91H   BLOCK     ECODE    ---       LVL=0
      00010F1AH   SYMBOL    XDATA    INT       uart_n
      00010F1CH   SYMBOL    XDATA    BYTE      dat
      00FF2A91H   LINE      ECODE    ---       #193
      00FF2A9BH   LINE      ECODE    ---       #195
      00FF2AB3H   LINE      ECODE    ---       #197
      00FF2AB3H   LINE      ECODE    ---       #198
      00FF2ABCH   LINE      ECODE    ---       #199
      00FF2AC2H   LINE      ECODE    ---       #200
      00FF2AC8H   LINE      ECODE    ---       #201
      00FF2ACAH   LINE      ECODE    ---       #202
      00FF2ACAH   LINE      ECODE    ---       #203
      00FF2AD3H   LINE      ECODE    ---       #204
      00FF2AD9H   LINE      ECODE    ---       #205
      00FF2ADFH   LINE      ECODE    ---       #206
      00FF2AE1H   LINE      ECODE    ---       #207
      00FF2AE1H   LINE      ECODE    ---       #208
      00FF2AEAH   LINE      ECODE    ---       #209
      00FF2AF0H   LINE      ECODE    ---       #210
      00FF2AF6H   LINE      ECODE    ---       #211
      00FF2AF8H   LINE      ECODE    ---       #212
      00FF2AF8H   LINE      ECODE    ---       #213
      00FF2B01H   LINE      ECODE    ---       #214
      00FF2B07H   LINE      ECODE    ---       #215
      00FF2B0DH   LINE      ECODE    ---       #216
      00FF2B0DH   LINE      ECODE    ---       #217
      00FF2B0DH   LINE      ECODE    ---       #218
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2B0EH   BLOCK     ECODE    ---       LVL=0
      00010D8BH   SYMBOL    XDATA    INT       uart_n
      00010D8DH   SYMBOL    XDATA    ---       p
      00010D91H   SYMBOL    XDATA    DWORD     len
      00FF2B0EH   LINE      ECODE    ---       #229
      00FF2B1EH   LINE      ECODE    ---       #231
      00FF2B5DH   LINE      ECODE    ---       #233
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2B5EH   BLOCK     ECODE    ---       LVL=0
      00010E69H   SYMBOL    XDATA    INT       uart_n
      00010E6BH   SYMBOL    XDATA    ---       str
      00FF2B5EH   LINE      ECODE    ---       #243
      00FF2B6EH   LINE      ECODE    ---       #245
      00FF2B70H   LINE      ECODE    ---       #247
      00FF2B90H   LINE      ECODE    ---       #248
      00FF2BA2H   LINE      ECODE    ---       #249
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_fifo
      00FE5620H   PUBLIC    ECODE    ---       fifo_read_buffer?
      00FE4CC0H   PUBLIC    ECODE    ---       fifo_write_element?
      00FE5B95H   PUBLIC    ECODE    ---       fifo_read_tail_buffer?
      00FE4B41H   PUBLIC    ECODE    ---       fifo_clear?
      00FE4E73H   PUBLIC    ECODE    ---       fifo_write_buffer?
      00FE4C89H   PUBLIC    ECODE    ---       fifo_used?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 59


      00FE61C2H   PUBLIC    ECODE    ---       fifo_init?
      00FE53FEH   PUBLIC    ECODE    ---       fifo_read_element?
      00010C0AH   PUBLIC    XDATA    BYTE      ?fifo_write_buffer??BYTE
      00010D18H   PUBLIC    XDATA    BYTE      ?fifo_read_element??BYTE
      00010B58H   PUBLIC    XDATA    BYTE      ?fifo_read_buffer??BYTE
      00010B70H   PUBLIC    XDATA    BYTE      ?fifo_read_tail_buffer??BYTE
      00010C61H   PUBLIC    XDATA    BYTE      ?fifo_init??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FE4A01H   SYMBOL    ECODE    ---       fifo_head_offset
      00FE4AA1H   SYMBOL    ECODE    ---       fifo_end_offset

      00FE4A01H   BLOCK     ECODE    ---       LVL=0
      00010DF2H   SYMBOL    XDATA    ---       fifo
      00010DF6H   SYMBOL    XDATA    DWORD     offset
      00FE4A01H   LINE      ECODE    ---       #42
      00FE4A15H   LINE      ECODE    ---       #44
      00FE4A3DH   LINE      ECODE    ---       #46
      00FE4A3FH   LINE      ECODE    ---       #48
      00FE4A72H   LINE      ECODE    ---       #49
      00FE4AA0H   LINE      ECODE    ---       #50
      ---         BLOCKEND  ---      ---       LVL=0

      00FE4AA1H   BLOCK     ECODE    ---       LVL=0
      00010DFAH   SYMBOL    XDATA    ---       fifo
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 60


      00010DFEH   SYMBOL    XDATA    DWORD     offset
      00FE4AA1H   LINE      ECODE    ---       #60
      00FE4AB5H   LINE      ECODE    ---       #62
      00FE4ADDH   LINE      ECODE    ---       #64
      00FE4ADFH   LINE      ECODE    ---       #66
      00FE4B12H   LINE      ECODE    ---       #67
      00FE4B40H   LINE      ECODE    ---       #68
      ---         BLOCKEND  ---      ---       LVL=0

      00FE4B41H   BLOCK     ECODE    ---       LVL=0
      00010E6FH   SYMBOL    XDATA    ---       fifo
      00FE4B4BH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E73H   SYMBOL    XDATA    INT       return_state
      ---         BLOCKEND  ---      ---       LVL=1
      00FE4B41H   LINE      ECODE    ---       #77
      00FE4B4BH   LINE      ECODE    ---       #78
      00FE4B4BH   LINE      ECODE    ---       #80
      00FE4B53H   LINE      ECODE    ---       #81
      00FE4B53H   LINE      ECODE    ---       #88
      00FE4B67H   LINE      ECODE    ---       #89
      00FE4B7EH   LINE      ECODE    ---       #90
      00FE4B95H   LINE      ECODE    ---       #91
      00FE4BBFH   LINE      ECODE    ---       #92
      00FE4BDDH   LINE      ECODE    ---       #94
      00FE4C0EH   LINE      ECODE    ---       #95
      00FE4C41H   LINE      ECODE    ---       #96
      00FE4C74H   LINE      ECODE    ---       #97
      00FE4C74H   LINE      ECODE    ---       #98
      00FE4C82H   LINE      ECODE    ---       #99
      00FE4C82H   LINE      ECODE    ---       #100
      00FE4C88H   LINE      ECODE    ---       #101
      ---         BLOCKEND  ---      ---       LVL=0

      00FE4C89H   BLOCK     ECODE    ---       LVL=0
      00010ED7H   SYMBOL    XDATA    ---       fifo
      00FE4C89H   LINE      ECODE    ---       #110
      00FE4C93H   LINE      ECODE    ---       #113
      00FE4CBFH   LINE      ECODE    ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      00FE4CC0H   BLOCK     ECODE    ---       LVL=0
      00010D95H   SYMBOL    XDATA    ---       fifo
      00010D99H   SYMBOL    XDATA    DWORD     dat
      00FE4CD4H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010D9DH   SYMBOL    XDATA    INT       return_state
      ---         BLOCKEND  ---      ---       LVL=1
      00FE4CC0H   LINE      ECODE    ---       #124
      00FE4CD4H   LINE      ECODE    ---       #125
      00FE4CD4H   LINE      ECODE    ---       #127
      00FE4CDCH   LINE      ECODE    ---       #129
      00FE4CDCH   LINE      ECODE    ---       #131
      00FE4CF7H   LINE      ECODE    ---       #133
      00FE4D01H   LINE      ECODE    ---       #134
      00FE4D04H   LINE      ECODE    ---       #135
      00FE4D04H   LINE      ECODE    ---       #136
      00FE4D18H   LINE      ECODE    ---       #138
      00FE4D36H   LINE      ECODE    ---       #140
      00FE4D57H   LINE      ECODE    ---       #142
      00FE4D95H   LINE      ECODE    ---       #143
      00FE4DD6H   LINE      ECODE    ---       #144
      00FE4E19H   LINE      ECODE    ---       #145
      00FE4E19H   LINE      ECODE    ---       #146
      00FE4E2AH   LINE      ECODE    ---       #147
      00FE4E4CH   LINE      ECODE    ---       #148
      00FE4E4EH   LINE      ECODE    ---       #151
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 61


      00FE4E58H   LINE      ECODE    ---       #152
      00FE4E58H   LINE      ECODE    ---       #153
      00FE4E6CH   LINE      ECODE    ---       #154
      00FE4E6CH   LINE      ECODE    ---       #156
      00FE4E72H   LINE      ECODE    ---       #157
      ---         BLOCKEND  ---      ---       LVL=0

      00FE4E73H   BLOCK     ECODE    ---       LVL=0
      00010C0AH   SYMBOL    XDATA    ---       fifo
      00010C0EH   SYMBOL    XDATA    ---       dat
      00010C12H   SYMBOL    XDATA    DWORD     length
      00FE4E87H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010C16H   SYMBOL    XDATA    INT       return_state
      00010C18H   SYMBOL    XDATA    DWORD     temp_length
      ---         BLOCKEND  ---      ---       LVL=1
      00FE4E73H   LINE      ECODE    ---       #168
      00FE4E87H   LINE      ECODE    ---       #169
      00FE4E87H   LINE      ECODE    ---       #171
      00FE4E8FH   LINE      ECODE    ---       #172
      00FE4E9BH   LINE      ECODE    ---       #174
      00FE4E9BH   LINE      ECODE    ---       #176
      00FE4EA9H   LINE      ECODE    ---       #178
      00FE4EB3H   LINE      ECODE    ---       #179
      00FE4EB6H   LINE      ECODE    ---       #180
      00FE4EB6H   LINE      ECODE    ---       #181
      00FE4ED1H   LINE      ECODE    ---       #183
      00FE4EDBH   LINE      ECODE    ---       #184
      00FE4EDEH   LINE      ECODE    ---       #185
      00FE4EDEH   LINE      ECODE    ---       #186
      00FE4EF2H   LINE      ECODE    ---       #188
      00FE4F18H   LINE      ECODE    ---       #190
      00FE4F4EH   LINE      ECODE    ---       #192
      00FE4F69H   LINE      ECODE    ---       #194
      00FE4F8DH   LINE      ECODE    ---       #196
      00FE4F8DH   LINE      ECODE    ---       #198
      00FE4FD5H   LINE      ECODE    ---       #201
      00FE4FECH   LINE      ECODE    ---       #202
      00FE504EH   LINE      ECODE    ---       #206
      00FE5071H   LINE      ECODE    ---       #207
      00FE5074H   LINE      ECODE    ---       #208
      00FE5074H   LINE      ECODE    ---       #210
      00FE50C0H   LINE      ECODE    ---       #213
      00FE50D7H   LINE      ECODE    ---       #214
      00FE513FH   LINE      ECODE    ---       #218
      00FE5162H   LINE      ECODE    ---       #219
      00FE5165H   LINE      ECODE    ---       #220
      00FE5165H   LINE      ECODE    ---       #222
      00FE51B5H   LINE      ECODE    ---       #225
      00FE51CCH   LINE      ECODE    ---       #226
      00FE523AH   LINE      ECODE    ---       #230
      00FE525DH   LINE      ECODE    ---       #231
      00FE525DH   LINE      ECODE    ---       #232
      00FE525DH   LINE      ECODE    ---       #233
      00FE5260H   LINE      ECODE    ---       #236
      00FE5281H   LINE      ECODE    ---       #238
      00FE5281H   LINE      ECODE    ---       #240
      00FE52C9H   LINE      ECODE    ---       #243
      00FE52E0H   LINE      ECODE    ---       #244
      00FE52E3H   LINE      ECODE    ---       #245
      00FE52E3H   LINE      ECODE    ---       #247
      00FE532FH   LINE      ECODE    ---       #250
      00FE5346H   LINE      ECODE    ---       #251
      00FE5348H   LINE      ECODE    ---       #252
      00FE5348H   LINE      ECODE    ---       #254
      00FE5398H   LINE      ECODE    ---       #257
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 62


      00FE53AFH   LINE      ECODE    ---       #258
      00FE53AFH   LINE      ECODE    ---       #259
      00FE53AFH   LINE      ECODE    ---       #260
      00FE53AFH   LINE      ECODE    ---       #262
      00FE53D7H   LINE      ECODE    ---       #263
      00FE53D9H   LINE      ECODE    ---       #266
      00FE53E3H   LINE      ECODE    ---       #267
      00FE53E3H   LINE      ECODE    ---       #268
      00FE53F7H   LINE      ECODE    ---       #269
      00FE53F7H   LINE      ECODE    ---       #271
      00FE53FDH   LINE      ECODE    ---       #272
      ---         BLOCKEND  ---      ---       LVL=0

      00FE53FEH   BLOCK     ECODE    ---       LVL=0
      00010D18H   SYMBOL    XDATA    ---       fifo
      00010D1CH   SYMBOL    XDATA    ---       dat
      00010D20H   SYMBOL    XDATA    INT       flag
      00FE5412H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010D22H   SYMBOL    XDATA    INT       return_state
      ---         BLOCKEND  ---      ---       LVL=1
      00FE53FEH   LINE      ECODE    ---       #283
      00FE5412H   LINE      ECODE    ---       #284
      00FE5412H   LINE      ECODE    ---       #286
      00FE541AH   LINE      ECODE    ---       #288
      00FE541AH   LINE      ECODE    ---       #290
      00FE5428H   LINE      ECODE    ---       #292
      00FE5432H   LINE      ECODE    ---       #293
      00FE5435H   LINE      ECODE    ---       #296
      00FE5450H   LINE      ECODE    ---       #298
      00FE545AH   LINE      ECODE    ---       #299
      00FE545DH   LINE      ECODE    ---       #300
      00FE545DH   LINE      ECODE    ---       #302
      00FE5471H   LINE      ECODE    ---       #304
      00FE547BH   LINE      ECODE    ---       #305
      00FE547EH   LINE      ECODE    ---       #306
      00FE547EH   LINE      ECODE    ---       #308
      00FE5492H   LINE      ECODE    ---       #309
      00FE54B3H   LINE      ECODE    ---       #311
      00FE54F4H   LINE      ECODE    ---       #312
      00FE5536H   LINE      ECODE    ---       #313
      00FE5580H   LINE      ECODE    ---       #314
      00FE5580H   LINE      ECODE    ---       #315
      00FE5594H   LINE      ECODE    ---       #316
      00FE5594H   LINE      ECODE    ---       #318
      00FE55A0H   LINE      ECODE    ---       #320
      00FE55B2H   LINE      ECODE    ---       #322
      00FE55BCH   LINE      ECODE    ---       #323
      00FE55BEH   LINE      ECODE    ---       #324
      00FE55BEH   LINE      ECODE    ---       #325
      00FE55D2H   LINE      ECODE    ---       #326
      00FE55E3H   LINE      ECODE    ---       #327
      00FE5605H   LINE      ECODE    ---       #328
      00FE5619H   LINE      ECODE    ---       #329
      00FE5619H   LINE      ECODE    ---       #332
      00FE561FH   LINE      ECODE    ---       #333
      ---         BLOCKEND  ---      ---       LVL=0

      00FE5620H   BLOCK     ECODE    ---       LVL=0
      00010B58H   SYMBOL    XDATA    ---       fifo
      00010B5CH   SYMBOL    XDATA    ---       dat
      00010B60H   SYMBOL    XDATA    ---       length
      00010B64H   SYMBOL    XDATA    INT       flag
      00FE5634H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010B66H   SYMBOL    XDATA    INT       return_state
      00010B68H   SYMBOL    XDATA    DWORD     temp_length
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 63


      00010B6CH   SYMBOL    XDATA    DWORD     fifo_data_length
      ---         BLOCKEND  ---      ---       LVL=1
      00FE5620H   LINE      ECODE    ---       #345
      00FE5634H   LINE      ECODE    ---       #346
      00FE5634H   LINE      ECODE    ---       #349
      00FE563CH   LINE      ECODE    ---       #350
      00FE5648H   LINE      ECODE    ---       #351
      00FE5654H   LINE      ECODE    ---       #353
      00FE5654H   LINE      ECODE    ---       #355
      00FE5662H   LINE      ECODE    ---       #357
      00FE566CH   LINE      ECODE    ---       #358
      00FE566FH   LINE      ECODE    ---       #361
      00FE568AH   LINE      ECODE    ---       #363
      00FE56A5H   LINE      ECODE    ---       #364
      00FE56AFH   LINE      ECODE    ---       #365
      00FE56B2H   LINE      ECODE    ---       #366
      00FE56B2H   LINE      ECODE    ---       #368
      00FE56CAH   LINE      ECODE    ---       #369
      00FE56E9H   LINE      ECODE    ---       #371
      00FE5704H   LINE      ECODE    ---       #372
      00FE570EH   LINE      ECODE    ---       #373
      00FE571CH   LINE      ECODE    ---       #375
      00FE5730H   LINE      ECODE    ---       #376
      00FE5733H   LINE      ECODE    ---       #377
      00FE5733H   LINE      ECODE    ---       #380
      00FE5747H   LINE      ECODE    ---       #381
      00FE577DH   LINE      ECODE    ---       #382
      00FE579FH   LINE      ECODE    ---       #384
      00FE57C0H   LINE      ECODE    ---       #386
      00FE5814H   LINE      ECODE    ---       #387
      00FE586CH   LINE      ECODE    ---       #388
      00FE58C5H   LINE      ECODE    ---       #389
      00FE58C5H   LINE      ECODE    ---       #390
      00FE58C8H   LINE      ECODE    ---       #393
      00FE58ECH   LINE      ECODE    ---       #395
      00FE58ECH   LINE      ECODE    ---       #397
      00FE5936H   LINE      ECODE    ---       #398
      00FE5986H   LINE      ECODE    ---       #399
      00FE5989H   LINE      ECODE    ---       #400
      00FE5989H   LINE      ECODE    ---       #402
      00FE59D7H   LINE      ECODE    ---       #403
      00FE5A2BH   LINE      ECODE    ---       #404
      00FE5A2EH   LINE      ECODE    ---       #405
      00FE5A2EH   LINE      ECODE    ---       #407
      00FE5A80H   LINE      ECODE    ---       #408
      00FE5AD8H   LINE      ECODE    ---       #409
      00FE5AD8H   LINE      ECODE    ---       #410
      00FE5AD8H   LINE      ECODE    ---       #411
      00FE5AD8H   LINE      ECODE    ---       #412
      00FE5AECH   LINE      ECODE    ---       #413
      00FE5AECH   LINE      ECODE    ---       #415
      00FE5AFBH   LINE      ECODE    ---       #417
      00FE5B0DH   LINE      ECODE    ---       #419
      00FE5B17H   LINE      ECODE    ---       #420
      00FE5B19H   LINE      ECODE    ---       #421
      00FE5B19H   LINE      ECODE    ---       #422
      00FE5B2DH   LINE      ECODE    ---       #423
      00FE5B4BH   LINE      ECODE    ---       #424
      00FE5B7AH   LINE      ECODE    ---       #425
      00FE5B8EH   LINE      ECODE    ---       #426
      00FE5B8EH   LINE      ECODE    ---       #429
      00FE5B94H   LINE      ECODE    ---       #430
      ---         BLOCKEND  ---      ---       LVL=0

      00FE5B95H   BLOCK     ECODE    ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 64


      00010B70H   SYMBOL    XDATA    ---       fifo
      00010B74H   SYMBOL    XDATA    ---       dat
      00010B78H   SYMBOL    XDATA    ---       length
      00010B7CH   SYMBOL    XDATA    INT       flag
      00FE5BA9H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010B7EH   SYMBOL    XDATA    INT       return_state
      00010B80H   SYMBOL    XDATA    DWORD     temp_length
      00010B84H   SYMBOL    XDATA    DWORD     fifo_data_length
      ---         BLOCKEND  ---      ---       LVL=1
      00FE5B95H   LINE      ECODE    ---       #444
      00FE5BA9H   LINE      ECODE    ---       #445
      00FE5BA9H   LINE      ECODE    ---       #448
      00FE5BB1H   LINE      ECODE    ---       #449
      00FE5BBDH   LINE      ECODE    ---       #450
      00FE5BC9H   LINE      ECODE    ---       #452
      00FE5BC9H   LINE      ECODE    ---       #454
      00FE5BD7H   LINE      ECODE    ---       #456
      00FE5BE1H   LINE      ECODE    ---       #457
      00FE5BE4H   LINE      ECODE    ---       #460
      00FE5BFFH   LINE      ECODE    ---       #462
      00FE5C1AH   LINE      ECODE    ---       #463
      00FE5C24H   LINE      ECODE    ---       #464
      00FE5C27H   LINE      ECODE    ---       #465
      00FE5C27H   LINE      ECODE    ---       #467
      00FE5C3FH   LINE      ECODE    ---       #468
      00FE5C5EH   LINE      ECODE    ---       #470
      00FE5C79H   LINE      ECODE    ---       #471
      00FE5C83H   LINE      ECODE    ---       #472
      00FE5C91H   LINE      ECODE    ---       #474
      00FE5CA5H   LINE      ECODE    ---       #475
      00FE5CA8H   LINE      ECODE    ---       #476
      00FE5CA8H   LINE      ECODE    ---       #479
      00FE5CBCH   LINE      ECODE    ---       #480
      00FE5D17H   LINE      ECODE    ---       #482
      00FE5D38H   LINE      ECODE    ---       #484
      00FE5D9FH   LINE      ECODE    ---       #485
      00FE5E0AH   LINE      ECODE    ---       #486
      00FE5E76H   LINE      ECODE    ---       #487
      00FE5E76H   LINE      ECODE    ---       #488
      00FE5E79H   LINE      ECODE    ---       #491
      00FE5EABH   LINE      ECODE    ---       #492
      00FE5ECFH   LINE      ECODE    ---       #494
      00FE5ECFH   LINE      ECODE    ---       #496
      00FE5F25H   LINE      ECODE    ---       #497
      00FE5FA3H   LINE      ECODE    ---       #498
      00FE5FA6H   LINE      ECODE    ---       #499
      00FE5FA6H   LINE      ECODE    ---       #501
      00FE6000H   LINE      ECODE    ---       #502
      00FE6084H   LINE      ECODE    ---       #503
      00FE6087H   LINE      ECODE    ---       #504
      00FE6087H   LINE      ECODE    ---       #506
      00FE60E5H   LINE      ECODE    ---       #507
      00FE616FH   LINE      ECODE    ---       #508
      00FE616FH   LINE      ECODE    ---       #509
      00FE616FH   LINE      ECODE    ---       #510
      00FE616FH   LINE      ECODE    ---       #511
      00FE6183H   LINE      ECODE    ---       #512
      00FE6183H   LINE      ECODE    ---       #514
      00FE618FH   LINE      ECODE    ---       #516
      00FE61A1H   LINE      ECODE    ---       #518
      00FE61ABH   LINE      ECODE    ---       #519
      00FE61ADH   LINE      ECODE    ---       #520
      00FE61ADH   LINE      ECODE    ---       #521
      00FE61BBH   LINE      ECODE    ---       #522
      00FE61BBH   LINE      ECODE    ---       #525
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 65


      00FE61C1H   LINE      ECODE    ---       #526
      ---         BLOCKEND  ---      ---       LVL=0

      00FE61C2H   BLOCK     ECODE    ---       LVL=0
      00010C61H   SYMBOL    XDATA    ---       fifo
      00010C65H   SYMBOL    XDATA    INT       type
      00010C67H   SYMBOL    XDATA    ---       buffer_addr
      00010C6BH   SYMBOL    XDATA    DWORD     len
      00FE61D2H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010C6FH   SYMBOL    XDATA    INT       return_state
      ---         BLOCKEND  ---      ---       LVL=1
      00FE61C2H   LINE      ECODE    ---       #538
      00FE61D2H   LINE      ECODE    ---       #539
      00FE61D2H   LINE      ECODE    ---       #541
      00FE61DAH   LINE      ECODE    ---       #542
      00FE61DAH   LINE      ECODE    ---       #544
      00FE61F9H   LINE      ECODE    ---       #545
      00FE6207H   LINE      ECODE    ---       #546
      00FE621CH   LINE      ECODE    ---       #547
      00FE6233H   LINE      ECODE    ---       #548
      00FE624AH   LINE      ECODE    ---       #549
      00FE6269H   LINE      ECODE    ---       #550
      00FE6288H   LINE      ECODE    ---       #551
      00FE6288H   LINE      ECODE    ---       #552
      00FE628EH   LINE      ECODE    ---       #553
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       zf_function
      00FE8890H   PUBLIC    ECODE    ---       func_double_to_str?
      00FE857CH   PUBLIC    ECODE    ---       func_str_to_double?
      00FE8174H   PUBLIC    ECODE    ---       func_float_to_str?
      00FE7F10H   PUBLIC    ECODE    ---       func_str_to_float?
      00FE7A98H   PUBLIC    ECODE    ---       func_soft_delay?
      00FE7E15H   PUBLIC    ECODE    ---       func_uint_to_str?
      00FE7D76H   PUBLIC    ECODE    ---       func_str_to_uint?
      00FE8E93H   PUBLIC    ECODE    ---       func_hex_to_str?
      00FE8CC8H   PUBLIC    ECODE    ---       func_str_to_hex?
      00FE7BE0H   PUBLIC    ECODE    ---       func_int_to_str?
      00FE7AC0H   PUBLIC    ECODE    ---       func_str_to_int?
      00FE79F2H   PUBLIC    ECODE    ---       func_get_greatest_common_divisor?
      0001071CH   PUBLIC    XDATA    BYTE      ?func_double_to_str??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 66


      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FF4F7EH   SYMBOL    HCONST   ---       ?tpl?0001

      00FE79F2H   BLOCK     ECODE    ---       LVL=0
      00010E02H   SYMBOL    XDATA    DWORD     num1
      00010E06H   SYMBOL    XDATA    DWORD     num2
      00FE79F2H   LINE      ECODE    ---       #49
      00FE7A06H   LINE      ECODE    ---       #51
      00FE7A08H   LINE      ECODE    ---       #53
      00FE7A20H   LINE      ECODE    ---       #55
      00FE7A3DH   LINE      ECODE    ---       #56
      00FE7A3DH   LINE      ECODE    ---       #57
      00FE7A55H   LINE      ECODE    ---       #59
      00FE7A72H   LINE      ECODE    ---       #60
      00FE7A8DH   LINE      ECODE    ---       #62
      00FE7A97H   LINE      ECODE    ---       #63
      ---         BLOCKEND  ---      ---       LVL=0

      00FE7A98H   BLOCK     ECODE    ---       LVL=0
      00010EDBH   SYMBOL    XDATA    LONG      t
      00FE7A98H   LINE      ECODE    ---       #72
      00FE7AA2H   LINE      ECODE    ---       #74
      00FE7ABFH   LINE      ECODE    ---       #75
      ---         BLOCKEND  ---      ---       LVL=0

      00FE7AC0H   BLOCK     ECODE    ---       LVL=0
      00010DB3H   SYMBOL    XDATA    ---       str
      00FE7ACAH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010DB7H   SYMBOL    XDATA    BYTE      sign
      00010DB8H   SYMBOL    XDATA    LONG      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FE7AC0H   LINE      ECODE    ---       #84
      00FE7ACAH   LINE      ECODE    ---       #85
      00FE7ACAH   LINE      ECODE    ---       #86
      00FE7ACFH   LINE      ECODE    ---       #87
      00FE7ADBH   LINE      ECODE    ---       #89
      00FE7ADBH   LINE      ECODE    ---       #91
      00FE7AECH   LINE      ECODE    ---       #93
      00FE7AECH   LINE      ECODE    ---       #94
      00FE7AECH   LINE      ECODE    ---       #96
      00FE7AFEH   LINE      ECODE    ---       #98
      00FE7B04H   LINE      ECODE    ---       #99
      00FE7B17H   LINE      ECODE    ---       #100
      00FE7B19H   LINE      ECODE    ---       #101
      00FE7B2BH   LINE      ECODE    ---       #103
      00FE7B3EH   LINE      ECODE    ---       #104
      00FE7B3EH   LINE      ECODE    ---       #106
      00FE7B40H   LINE      ECODE    ---       #108
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 67


      00FE7B75H   LINE      ECODE    ---       #109
      00FE7B88H   LINE      ECODE    ---       #110
      00FE7BAEH   LINE      ECODE    ---       #112
      00FE7BB7H   LINE      ECODE    ---       #114
      00FE7BD5H   LINE      ECODE    ---       #115
      00FE7BD5H   LINE      ECODE    ---       #117
      00FE7BDFH   LINE      ECODE    ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FE7BE0H   BLOCK     ECODE    ---       LVL=0
      00010AAFH   SYMBOL    XDATA    ---       str
      00010AB3H   SYMBOL    XDATA    LONG      number
      00FE7BF4H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010AB7H   SYMBOL    XDATA    ---       data_temp
      00010AC7H   SYMBOL    XDATA    BYTE      temp_bit
      00010AC8H   SYMBOL    XDATA    LONG      number_temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FE7BE0H   LINE      ECODE    ---       #128
      00FE7BF4H   LINE      ECODE    ---       #129
      00FE7BF4H   LINE      ECODE    ---       #131
      00FE7BF9H   LINE      ECODE    ---       #132
      00FE7C05H   LINE      ECODE    ---       #134
      00FE7C05H   LINE      ECODE    ---       #136
      00FE7C16H   LINE      ECODE    ---       #138
      00FE7C16H   LINE      ECODE    ---       #139
      00FE7C16H   LINE      ECODE    ---       #141
      00FE7C26H   LINE      ECODE    ---       #143
      00FE7C4BH   LINE      ECODE    ---       #144
      00FE7C69H   LINE      ECODE    ---       #145
      00FE7C6BH   LINE      ECODE    ---       #146
      00FE7C79H   LINE      ECODE    ---       #148
      00FE7C88H   LINE      ECODE    ---       #149
      00FE7C8BH   LINE      ECODE    ---       #150
      00FE7C8BH   LINE      ECODE    ---       #152
      00FE7C8EH   LINE      ECODE    ---       #154
      00FE7CAAH   LINE      ECODE    ---       #155
      00FE7CF4H   LINE      ECODE    ---       #156
      00FE7D0DH   LINE      ECODE    ---       #157
      00FE7D1EH   LINE      ECODE    ---       #158
      00FE7D20H   LINE      ECODE    ---       #160
      00FE7D5EH   LINE      ECODE    ---       #161
      00FE7D6CH   LINE      ECODE    ---       #162
      00FE7D75H   LINE      ECODE    ---       #163
      00FE7D75H   LINE      ECODE    ---       #164
      ---         BLOCKEND  ---      ---       LVL=0

      00FE7D76H   BLOCK     ECODE    ---       LVL=0
      00010E0AH   SYMBOL    XDATA    ---       str
      00FE7D80H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E0EH   SYMBOL    XDATA    DWORD     temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FE7D76H   LINE      ECODE    ---       #173
      00FE7D80H   LINE      ECODE    ---       #174
      00FE7D80H   LINE      ECODE    ---       #175
      00FE7D8CH   LINE      ECODE    ---       #178
      00FE7D8CH   LINE      ECODE    ---       #180
      00FE7D9AH   LINE      ECODE    ---       #182
      00FE7D9AH   LINE      ECODE    ---       #183
      00FE7D9AH   LINE      ECODE    ---       #185
      00FE7D9CH   LINE      ECODE    ---       #187
      00FE7DD1H   LINE      ECODE    ---       #188
      00FE7DE4H   LINE      ECODE    ---       #189
      00FE7E0AH   LINE      ECODE    ---       #190
      00FE7E0AH   LINE      ECODE    ---       #192
      00FE7E14H   LINE      ECODE    ---       #193
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 68


      ---         BLOCKEND  ---      ---       LVL=0

      00FE7E15H   BLOCK     ECODE    ---       LVL=0
      00010B3FH   SYMBOL    XDATA    ---       str
      00010B43H   SYMBOL    XDATA    DWORD     number
      00FE7E29H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010B47H   SYMBOL    XDATA    ---       data_temp
      00010B57H   SYMBOL    XDATA    BYTE      temp_bit
      ---         BLOCKEND  ---      ---       LVL=1
      00FE7E15H   LINE      ECODE    ---       #203
      00FE7E29H   LINE      ECODE    ---       #204
      00FE7E29H   LINE      ECODE    ---       #206
      00FE7E2EH   LINE      ECODE    ---       #210
      00FE7E2EH   LINE      ECODE    ---       #212
      00FE7E3FH   LINE      ECODE    ---       #214
      00FE7E3FH   LINE      ECODE    ---       #215
      00FE7E3FH   LINE      ECODE    ---       #217
      00FE7E4DH   LINE      ECODE    ---       #219
      00FE7E5CH   LINE      ECODE    ---       #220
      00FE7E5FH   LINE      ECODE    ---       #221
      00FE7E5FH   LINE      ECODE    ---       #223
      00FE7E61H   LINE      ECODE    ---       #225
      00FE7E91H   LINE      ECODE    ---       #226
      00FE7EAAH   LINE      ECODE    ---       #227
      00FE7EB8H   LINE      ECODE    ---       #228
      00FE7EBAH   LINE      ECODE    ---       #230
      00FE7EF8H   LINE      ECODE    ---       #231
      00FE7F06H   LINE      ECODE    ---       #232
      00FE7F0FH   LINE      ECODE    ---       #233
      00FE7F0FH   LINE      ECODE    ---       #234
      ---         BLOCKEND  ---      ---       LVL=0

      00FE7F10H   BLOCK     ECODE    ---       LVL=0
      00010C2EH   SYMBOL    XDATA    ---       str
      00FE7F1AH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010C32H   SYMBOL    XDATA    BYTE      sign
      00010C33H   SYMBOL    XDATA    FLOAT     temp
      00010C37H   SYMBOL    XDATA    FLOAT     temp_point
      00010C3BH   SYMBOL    XDATA    FLOAT     point_bit
      ---         BLOCKEND  ---      ---       LVL=1
      00FE7F10H   LINE      ECODE    ---       #243
      00FE7F1AH   LINE      ECODE    ---       #244
      00FE7F1AH   LINE      ECODE    ---       #245
      00FE7F1FH   LINE      ECODE    ---       #246
      00FE7F2BH   LINE      ECODE    ---       #247
      00FE7F37H   LINE      ECODE    ---       #248
      00FE7F47H   LINE      ECODE    ---       #252
      00FE7F47H   LINE      ECODE    ---       #254
      00FE7F58H   LINE      ECODE    ---       #256
      00FE7F58H   LINE      ECODE    ---       #257
      00FE7F58H   LINE      ECODE    ---       #259
      00FE7F6AH   LINE      ECODE    ---       #261
      00FE7F70H   LINE      ECODE    ---       #262
      00FE7F83H   LINE      ECODE    ---       #263
      00FE7F85H   LINE      ECODE    ---       #264
      00FE7F97H   LINE      ECODE    ---       #266
      00FE7FAAH   LINE      ECODE    ---       #267
      00FE7FAAH   LINE      ECODE    ---       #270
      00FE7FACH   LINE      ECODE    ---       #272
      00FE7FEFH   LINE      ECODE    ---       #273
      00FE8002H   LINE      ECODE    ---       #274
      00FE8028H   LINE      ECODE    ---       #275
      00FE803DH   LINE      ECODE    ---       #277
      00FE8050H   LINE      ECODE    ---       #278
      00FE8052H   LINE      ECODE    ---       #280
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 69


      00FE8095H   LINE      ECODE    ---       #281
      00FE80B0H   LINE      ECODE    ---       #282
      00FE80C3H   LINE      ECODE    ---       #283
      00FE810AH   LINE      ECODE    ---       #284
      00FE8129H   LINE      ECODE    ---       #285
      00FE8129H   LINE      ECODE    ---       #286
      00FE8148H   LINE      ECODE    ---       #288
      00FE8151H   LINE      ECODE    ---       #290
      00FE8169H   LINE      ECODE    ---       #291
      00FE8169H   LINE      ECODE    ---       #293
      00FE8173H   LINE      ECODE    ---       #294
      ---         BLOCKEND  ---      ---       LVL=0

      00FE8174H   BLOCK     ECODE    ---       LVL=0
      00010842H   SYMBOL    XDATA    ---       str
      00010846H   SYMBOL    XDATA    FLOAT     number
      0001084AH   SYMBOL    XDATA    BYTE      point_bit
      00FE818CH   BLOCK     ECODE    NEAR LAB  LVL=1
      0001084BH   SYMBOL    XDATA    LONG      data_int
      0001084FH   SYMBOL    XDATA    LONG      data_float
      00010853H   SYMBOL    XDATA    ---       data_temp
      00010873H   SYMBOL    XDATA    ---       data_temp_point
      0001088BH   SYMBOL    XDATA    BYTE      temp_bit
      ---         BLOCKEND  ---      ---       LVL=1
      00FE8174H   LINE      ECODE    ---       #305
      00FE818CH   LINE      ECODE    ---       #306
      00FE818CH   LINE      ECODE    ---       #307
      00FE8198H   LINE      ECODE    ---       #308
      00FE81A4H   LINE      ECODE    ---       #311
      00FE81ACH   LINE      ECODE    ---       #315
      00FE81ACH   LINE      ECODE    ---       #317
      00FE81BDH   LINE      ECODE    ---       #319
      00FE81BDH   LINE      ECODE    ---       #320
      00FE81BDH   LINE      ECODE    ---       #323
      00FE81D9H   LINE      ECODE    ---       #324
      00FE81EBH   LINE      ECODE    ---       #326
      00FE8210H   LINE      ECODE    ---       #327
      00FE8212H   LINE      ECODE    ---       #328
      00FE822CH   LINE      ECODE    ---       #330
      00FE8251H   LINE      ECODE    ---       #331
      00FE8276H   LINE      ECODE    ---       #332
      00FE8285H   LINE      ECODE    ---       #333
      00FE8288H   LINE      ECODE    ---       #334
      00FE8288H   LINE      ECODE    ---       #337
      00FE82B2H   LINE      ECODE    ---       #338
      00FE82B4H   LINE      ECODE    ---       #340
      00FE82D2H   LINE      ECODE    ---       #341
      00FE82E5H   LINE      ECODE    ---       #342
      00FE8301H   LINE      ECODE    ---       #345
      00FE8306H   LINE      ECODE    ---       #346
      00FE8306H   LINE      ECODE    ---       #348
      00FE833AH   LINE      ECODE    ---       #349
      00FE8353H   LINE      ECODE    ---       #350
      00FE8361H   LINE      ECODE    ---       #351
      00FE8364H   LINE      ECODE    ---       #353
      00FE83F6H   LINE      ECODE    ---       #354
      00FE8404H   LINE      ECODE    ---       #355
      00FE8410H   LINE      ECODE    ---       #358
      00FE841CH   LINE      ECODE    ---       #360
      00FE8421H   LINE      ECODE    ---       #361
      00FE8446H   LINE      ECODE    ---       #362
      00FE8454H   LINE      ECODE    ---       #364
      00FE8463H   LINE      ECODE    ---       #365
      00FE8466H   LINE      ECODE    ---       #368
      00FE8468H   LINE      ECODE    ---       #370
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 70


      00FE849CH   LINE      ECODE    ---       #371
      00FE84B5H   LINE      ECODE    ---       #372
      00FE84C3H   LINE      ECODE    ---       #373
      00FE84CCH   LINE      ECODE    ---       #374
      00FE84CFH   LINE      ECODE    ---       #376
      00FE8561H   LINE      ECODE    ---       #377
      00FE856FH   LINE      ECODE    ---       #378
      00FE857BH   LINE      ECODE    ---       #379
      00FE857BH   LINE      ECODE    ---       #380
      00FE857BH   LINE      ECODE    ---       #382
      ---         BLOCKEND  ---      ---       LVL=0

      00FE857CH   BLOCK     ECODE    ---       LVL=0
      00010ACCH   SYMBOL    XDATA    ---       str
      00FE8586H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010AD0H   SYMBOL    XDATA    BYTE      sign
      00010AD1H   SYMBOL    XDATA    DOUBLE    temp
      00010AD9H   SYMBOL    XDATA    DOUBLE    temp_point
      00010AE1H   SYMBOL    XDATA    DOUBLE    point_bit
      ---         BLOCKEND  ---      ---       LVL=1
      00FE857CH   LINE      ECODE    ---       #391
      00FE8586H   LINE      ECODE    ---       #392
      00FE8586H   LINE      ECODE    ---       #393
      00FE858BH   LINE      ECODE    ---       #394
      00FE85A1H   LINE      ECODE    ---       #395
      00FE85B7H   LINE      ECODE    ---       #396
      00FE85D1H   LINE      ECODE    ---       #400
      00FE85D1H   LINE      ECODE    ---       #402
      00FE85E2H   LINE      ECODE    ---       #404
      00FE85E2H   LINE      ECODE    ---       #405
      00FE85E2H   LINE      ECODE    ---       #407
      00FE85F4H   LINE      ECODE    ---       #409
      00FE85FAH   LINE      ECODE    ---       #410
      00FE860DH   LINE      ECODE    ---       #411
      00FE860FH   LINE      ECODE    ---       #412
      00FE8621H   LINE      ECODE    ---       #414
      00FE8634H   LINE      ECODE    ---       #415
      00FE8634H   LINE      ECODE    ---       #418
      00FE8636H   LINE      ECODE    ---       #420
      00FE8691H   LINE      ECODE    ---       #421
      00FE86A4H   LINE      ECODE    ---       #422
      00FE86CDH   LINE      ECODE    ---       #423
      00FE86E2H   LINE      ECODE    ---       #425
      00FE86F5H   LINE      ECODE    ---       #426
      00FE86F8H   LINE      ECODE    ---       #428
      00FE8753H   LINE      ECODE    ---       #429
      00FE8780H   LINE      ECODE    ---       #430
      00FE8793H   LINE      ECODE    ---       #431
      00FE87DEH   LINE      ECODE    ---       #432
      00FE8815H   LINE      ECODE    ---       #433
      00FE8815H   LINE      ECODE    ---       #434
      00FE884CH   LINE      ECODE    ---       #436
      00FE8855H   LINE      ECODE    ---       #438
      00FE887DH   LINE      ECODE    ---       #439
      00FE887DH   LINE      ECODE    ---       #441
      00FE888FH   LINE      ECODE    ---       #443
      ---         BLOCKEND  ---      ---       LVL=0

      00FE8890H   BLOCK     ECODE    ---       LVL=0
      0001071CH   SYMBOL    XDATA    ---       str
      00010720H   SYMBOL    XDATA    DOUBLE    number
      00010728H   SYMBOL    XDATA    BYTE      point_bit
      00FE889EH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010729H   SYMBOL    XDATA    LONG      data_int
      0001072DH   SYMBOL    XDATA    LONG      data_float
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 71


      00010731H   SYMBOL    XDATA    ---       data_temp
      00010761H   SYMBOL    XDATA    ---       data_temp_point
      00010785H   SYMBOL    XDATA    BYTE      temp_bit
      ---         BLOCKEND  ---      ---       LVL=1
      00FE8890H   LINE      ECODE    ---       #454
      00FE889EH   LINE      ECODE    ---       #455
      00FE889EH   LINE      ECODE    ---       #456
      00FE88AAH   LINE      ECODE    ---       #457
      00FE88B6H   LINE      ECODE    ---       #460
      00FE88BEH   LINE      ECODE    ---       #464
      00FE88BEH   LINE      ECODE    ---       #466
      00FE88CFH   LINE      ECODE    ---       #468
      00FE88CFH   LINE      ECODE    ---       #469
      00FE88CFH   LINE      ECODE    ---       #472
      00FE88EFH   LINE      ECODE    ---       #473
      00FE890BH   LINE      ECODE    ---       #475
      00FE8930H   LINE      ECODE    ---       #476
      00FE8932H   LINE      ECODE    ---       #477
      00FE8950H   LINE      ECODE    ---       #479
      00FE8975H   LINE      ECODE    ---       #480
      00FE899AH   LINE      ECODE    ---       #481
      00FE89A9H   LINE      ECODE    ---       #482
      00FE89ACH   LINE      ECODE    ---       #483
      00FE89ACH   LINE      ECODE    ---       #486
      00FE89E8H   LINE      ECODE    ---       #487
      00FE89EAH   LINE      ECODE    ---       #489
      00FE8A1AH   LINE      ECODE    ---       #490
      00FE8A2DH   LINE      ECODE    ---       #491
      00FE8A4DH   LINE      ECODE    ---       #494
      00FE8A52H   LINE      ECODE    ---       #495
      00FE8A52H   LINE      ECODE    ---       #497
      00FE8A86H   LINE      ECODE    ---       #498
      00FE8A9FH   LINE      ECODE    ---       #499
      00FE8AADH   LINE      ECODE    ---       #500
      00FE8AB0H   LINE      ECODE    ---       #502
      00FE8B42H   LINE      ECODE    ---       #503
      00FE8B50H   LINE      ECODE    ---       #504
      00FE8B5CH   LINE      ECODE    ---       #507
      00FE8B68H   LINE      ECODE    ---       #509
      00FE8B6DH   LINE      ECODE    ---       #510
      00FE8B92H   LINE      ECODE    ---       #511
      00FE8BA0H   LINE      ECODE    ---       #512
      00FE8BB2H   LINE      ECODE    ---       #515
      00FE8BB4H   LINE      ECODE    ---       #517
      00FE8BE8H   LINE      ECODE    ---       #518
      00FE8C01H   LINE      ECODE    ---       #519
      00FE8C0FH   LINE      ECODE    ---       #520
      00FE8C18H   LINE      ECODE    ---       #521
      00FE8C1BH   LINE      ECODE    ---       #523
      00FE8CADH   LINE      ECODE    ---       #524
      00FE8CBBH   LINE      ECODE    ---       #525
      00FE8CC7H   LINE      ECODE    ---       #526
      00FE8CC7H   LINE      ECODE    ---       #527
      00FE8CC7H   LINE      ECODE    ---       #529
      ---         BLOCKEND  ---      ---       LVL=0

      00FE8CC8H   BLOCK     ECODE    ---       LVL=0
      00010CADH   SYMBOL    XDATA    ---       str
      00FE8CD2H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010CB1H   SYMBOL    XDATA    DWORD     str_len
      00010CB5H   SYMBOL    XDATA    DWORD     result_data
      00010CB9H   SYMBOL    XDATA    BYTE      temp
      00010CBAH   SYMBOL    XDATA    BYTE      flag
      ---         BLOCKEND  ---      ---       LVL=1
      00FE8CC8H   LINE      ECODE    ---       #538
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 72


      00FE8CD2H   LINE      ECODE    ---       #539
      00FE8CD2H   LINE      ECODE    ---       #540
      00FE8CECH   LINE      ECODE    ---       #541
      00FE8CF8H   LINE      ECODE    ---       #542
      00FE8CFDH   LINE      ECODE    ---       #543
      00FE8D02H   LINE      ECODE    ---       #547
      00FE8D02H   LINE      ECODE    ---       #549
      00FE8D13H   LINE      ECODE    ---       #551
      00FE8D13H   LINE      ECODE    ---       #552
      00FE8D13H   LINE      ECODE    ---       #554
      00FE8D1FH   LINE      ECODE    ---       #556
      00FE8D45H   LINE      ECODE    ---       #558
      00FE8D5EH   LINE      ECODE    ---       #559
      00FE8D61H   LINE      ECODE    ---       #560
      00FE8D87H   LINE      ECODE    ---       #562
      00FE8DA0H   LINE      ECODE    ---       #563
      00FE8DA2H   LINE      ECODE    ---       #564
      00FE8DCEH   LINE      ECODE    ---       #566
      00FE8DE7H   LINE      ECODE    ---       #567
      00FE8DE7H   LINE      ECODE    ---       #570
      00FE8DE7H   LINE      ECODE    ---       #571
      00FE8DE7H   LINE      ECODE    ---       #572
      00FE8E14H   LINE      ECODE    ---       #573
      00FE8E16H   LINE      ECODE    ---       #577
      00FE8E3CH   LINE      ECODE    ---       #579
      00FE8E4FH   LINE      ECODE    ---       #580
      00FE8E55H   LINE      ECODE    ---       #581
      00FE8E55H   LINE      ECODE    ---       #583
      00FE8E68H   LINE      ECODE    ---       #584
      00FE8E88H   LINE      ECODE    ---       #586
      00FE8E92H   LINE      ECODE    ---       #587
      ---         BLOCKEND  ---      ---       LVL=0

      00FE8E93H   BLOCK     ECODE    ---       LVL=0
      00010A6AH   SYMBOL    XDATA    ---       str
      00010A6EH   SYMBOL    XDATA    DWORD     number
      00FE8EA7H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010A72H   SYMBOL    XDATA    ---       hex_index
      00010A82H   SYMBOL    XDATA    ---       data_temp
      00010A8EH   SYMBOL    XDATA    BYTE      temp_bit
      ---         BLOCKEND  ---      ---       LVL=1
      00FE8E93H   LINE      ECODE    ---       #597
      00FE8EA7H   LINE      ECODE    ---       #598
      00FE8EA7H   LINE      ECODE    ---       #603
      00FE8EBDH   LINE      ECODE    ---       #605
      00FE8EC2H   LINE      ECODE    ---       #609
      00FE8EE7H   LINE      ECODE    ---       #610
      00FE8F0CH   LINE      ECODE    ---       #611
      00FE8F0CH   LINE      ECODE    ---       #613
      00FE8F1DH   LINE      ECODE    ---       #615
      00FE8F1DH   LINE      ECODE    ---       #616
      00FE8F1DH   LINE      ECODE    ---       #618
      00FE8F2BH   LINE      ECODE    ---       #620
      00FE8F3AH   LINE      ECODE    ---       #621
      00FE8F3DH   LINE      ECODE    ---       #622
      00FE8F3DH   LINE      ECODE    ---       #624
      00FE8F3FH   LINE      ECODE    ---       #626
      00FE8F6AH   LINE      ECODE    ---       #627
      00FE8F89H   LINE      ECODE    ---       #628
      00FE8F97H   LINE      ECODE    ---       #629
      00FE8F99H   LINE      ECODE    ---       #631
      00FE8FDEH   LINE      ECODE    ---       #632
      00FE8FECH   LINE      ECODE    ---       #633
      00FE8FF5H   LINE      ECODE    ---       #634
      00FE8FF5H   LINE      ECODE    ---       #635
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 73


      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SEEKFREE_FONT
      00FF212EH   PUBLIC    CODE     ---       ascii_font_8x16
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       SEEKFREE_IMU660RA
      00FEFBBBH   PUBLIC    ECODE    ---       imu660ra_get_gyro?
      00FEFCC6H   PUBLIC    ECODE    ---       imu660ra_gyro_transition?
      00FEFDB2H   PUBLIC    ECODE    ---       imu660ra_init?
      00FEFB65H   PUBLIC    ECODE    ---       imu660ra_get_acc?
      00FEFC11H   PUBLIC    ECODE    ---       imu660ra_acc_transition?
      00010D24H   PUBLIC    XDATA    INT       imu660ra_acc_x
      00010D26H   PUBLIC    XDATA    INT       imu660ra_acc_y
      00010D28H   PUBLIC    XDATA    INT       imu660ra_acc_z
      00010D2AH   PUBLIC    XDATA    INT       imu660ra_gyro_x
      00010D2CH   PUBLIC    XDATA    INT       imu660ra_gyro_y
      00010D2EH   PUBLIC    XDATA    INT       imu660ra_gyro_z
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 74


      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000C0H.3 SFRSYM    DATA     BIT       P43
      000000C0H.2 SFRSYM    DATA     BIT       P42
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C0H.1 SFRSYM    DATA     BIT       P41
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C0H.0 SFRSYM    DATA     BIT       P40
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FEFAB5H   SYMBOL    ECODE    ---       imu660ra_read_register
      00FEFA7BH   SYMBOL    ECODE    ---       imu660ra_write_registers
      00FEFA5DH   SYMBOL    ECODE    ---       imu660ra_write_register
      00FEF91EH   SYMBOL    ECODE    ---       imu660ra_simspi_wr_byte
      00FEFB19H   SYMBOL    ECODE    ---       imu660ra_self_check
      00FEF9EDH   SYMBOL    ECODE    ---       imu660ra_simspi_r_reg_bytes
      00FEF991H   SYMBOL    ECODE    ---       imu660ra_simspi_w_reg_bytes
      00FEF974H   SYMBOL    ECODE    ---       imu660ra_simspi_w_reg_byte
      00FEFAD9H   SYMBOL    ECODE    ---       imu660ra_read_registers

      00FEF91EH   BLOCK     ECODE    ---       LVL=0
      00010F3DH   SYMBOL    XDATA    BYTE      byte
      00FEF922H   BLOCK     ECODE    NEAR LAB  LVL=1
      0000021CH   SYMBOL    EDATA    BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FEF91EH   LINE      ECODE    ---       #294
      00FEF922H   LINE      ECODE    ---       #295
      00FEF922H   LINE      ECODE    ---       #297
      00FEF929H   LINE      ECODE    ---       #299
      00FEF938H   LINE      ECODE    ---       #300
      00FEF93FH   LINE      ECODE    ---       #301
      00FEF942H   LINE      ECODE    ---       #302
      00FEF945H   LINE      ECODE    ---       #303
      00FEF948H   LINE      ECODE    ---       #304
      00FEF94BH   LINE      ECODE    ---       #305
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 75


      00FEF958H   LINE      ECODE    ---       #306
      00FEF96FH   LINE      ECODE    ---       #307
      00FEF973H   LINE      ECODE    ---       #308
      ---         BLOCKEND  ---      ---       LVL=0

      00FEF974H   BLOCK     ECODE    ---       LVL=0
      00010F25H   SYMBOL    XDATA    BYTE      cmd
      00010F26H   SYMBOL    XDATA    BYTE      val
      00FEF974H   LINE      ECODE    ---       #316
      00FEF97EH   LINE      ECODE    ---       #318
      00FEF982H   LINE      ECODE    ---       #319
      00FEF989H   LINE      ECODE    ---       #320
      00FEF990H   LINE      ECODE    ---       #321
      ---         BLOCKEND  ---      ---       LVL=0

      00FEF991H   BLOCK     ECODE    ---       LVL=0
      00010DBCH   SYMBOL    XDATA    BYTE      cmd
      00010DBDH   SYMBOL    XDATA    ---       dat_addr
      00010DC1H   SYMBOL    XDATA    DWORD     len
      00FEF991H   LINE      ECODE    ---       #331
      00FEF9A9H   LINE      ECODE    ---       #333
      00FEF9ADH   LINE      ECODE    ---       #334
      00FEF9B4H   LINE      ECODE    ---       #335
      00FEF9B6H   LINE      ECODE    ---       #337
      00FEF9CFH   LINE      ECODE    ---       #338
      00FEF9ECH   LINE      ECODE    ---       #339
      ---         BLOCKEND  ---      ---       LVL=0

      00FEF9EDH   BLOCK     ECODE    ---       LVL=0
      00010DC5H   SYMBOL    XDATA    BYTE      cmd
      00010DC6H   SYMBOL    XDATA    ---       val
      00010DCAH   SYMBOL    XDATA    DWORD     num
      00FEF9EDH   LINE      ECODE    ---       #365
      00FEFA05H   LINE      ECODE    ---       #367
      00FEFA0FH   LINE      ECODE    ---       #368
      00FEFA16H   LINE      ECODE    ---       #369
      00FEFA18H   LINE      ECODE    ---       #371
      00FEFA3FH   LINE      ECODE    ---       #372
      00FEFA5CH   LINE      ECODE    ---       #373
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFA5DH   BLOCK     ECODE    ---       LVL=0
      00010F27H   SYMBOL    XDATA    BYTE      reg
      00010F28H   SYMBOL    XDATA    BYTE      dat
      00FEFA5DH   LINE      ECODE    ---       #384
      00FEFA67H   LINE      ECODE    ---       #386
      00FEFA6AH   LINE      ECODE    ---       #387
      00FEFA77H   LINE      ECODE    ---       #388
      00FEFA7AH   LINE      ECODE    ---       #389
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFA7BH   BLOCK     ECODE    ---       LVL=0
      00010DCEH   SYMBOL    XDATA    BYTE      reg
      00010DCFH   SYMBOL    XDATA    ---       dat
      00010DD3H   SYMBOL    XDATA    DWORD     len
      00FEFA7BH   LINE      ECODE    ---       #399
      00FEFA93H   LINE      ECODE    ---       #401
      00FEFA96H   LINE      ECODE    ---       #402
      00FEFAB1H   LINE      ECODE    ---       #403
      00FEFAB4H   LINE      ECODE    ---       #404
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFAB5H   BLOCK     ECODE    ---       LVL=0
      00010F3EH   SYMBOL    XDATA    BYTE      reg
      00FEFAB9H   BLOCK     ECODE    NEAR LAB  LVL=1
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 76


      0000021AH   SYMBOL    EDATA    ---       dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FEFAB5H   LINE      ECODE    ---       #413
      00FEFAB9H   LINE      ECODE    ---       #414
      00FEFAB9H   LINE      ECODE    ---       #416
      00FEFABCH   LINE      ECODE    ---       #417
      00FEFAD1H   LINE      ECODE    ---       #418
      00FEFAD4H   LINE      ECODE    ---       #419
      00FEFAD8H   LINE      ECODE    ---       #420
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFAD9H   BLOCK     ECODE    ---       LVL=0
      00010DD7H   SYMBOL    XDATA    BYTE      reg
      00010DD8H   SYMBOL    XDATA    ---       dat
      00010DDCH   SYMBOL    XDATA    DWORD     len
      00FEFAD9H   LINE      ECODE    ---       #431
      00FEFAF1H   LINE      ECODE    ---       #433
      00FEFAF4H   LINE      ECODE    ---       #434
      00FEFB15H   LINE      ECODE    ---       #435
      00FEFB18H   LINE      ECODE    ---       #436
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFB19H   BLOCK     ECODE    ---       LVL=0
      00000217H   SYMBOL    EDATA    BYTE      dat
      00010F3FH   SYMBOL    XDATA    BYTE      return_state
      00000218H   SYMBOL    EDATA    WORD      timeout_count
      00FEFB19H   LINE      ECODE    ---       #446
      00FEFB19H   LINE      ECODE    ---       #447
      00FEFB19H   LINE      ECODE    ---       #448
      00FEFB23H   LINE      ECODE    ---       #449
      00FEFB29H   LINE      ECODE    ---       #450
      00FEFB29H   LINE      ECODE    ---       #452
      00FEFB3FH   LINE      ECODE    ---       #454
      00FEFB45H   LINE      ECODE    ---       #455
      00FEFB47H   LINE      ECODE    ---       #456
      00FEFB47H   LINE      ECODE    ---       #457
      00FEFB4FH   LINE      ECODE    ---       #458
      00FEFB57H   LINE      ECODE    ---       #459
      00FEFB60H   LINE      ECODE    ---       #461
      00FEFB64H   LINE      ECODE    ---       #462
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFB65H   BLOCK     ECODE    ---       LVL=0
      00000209H   SYMBOL    EDATA    ---       dat
      00FEFB65H   LINE      ECODE    ---       #472
      00FEFB65H   LINE      ECODE    ---       #473
      00FEFB65H   LINE      ECODE    ---       #483
      00FEFB72H   LINE      ECODE    ---       #484
      00FEFB8AH   LINE      ECODE    ---       #485
      00FEFBA2H   LINE      ECODE    ---       #486
      00FEFBBAH   LINE      ECODE    ---       #488
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFBBBH   BLOCK     ECODE    ---       LVL=0
      00000210H   SYMBOL    EDATA    ---       dat
      00FEFBBBH   LINE      ECODE    ---       #497
      00FEFBBBH   LINE      ECODE    ---       #498
      00FEFBBBH   LINE      ECODE    ---       #508
      00FEFBC8H   LINE      ECODE    ---       #509
      00FEFBE0H   LINE      ECODE    ---       #510
      00FEFBF8H   LINE      ECODE    ---       #511
      00FEFC10H   LINE      ECODE    ---       #513
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFC11H   BLOCK     ECODE    ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 77


      00010E75H   SYMBOL    XDATA    INT       acc_value
      00FEFC17H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E77H   SYMBOL    XDATA    FLOAT     acc_dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FEFC11H   LINE      ECODE    ---       #522
      00FEFC17H   LINE      ECODE    ---       #523
      00FEFC17H   LINE      ECODE    ---       #524
      00FEFC23H   LINE      ECODE    ---       #525
      00FEFC35H   LINE      ECODE    ---       #527
      00FEFC35H   LINE      ECODE    ---       #528
      00FEFC55H   LINE      ECODE    ---       #529
      00FEFC57H   LINE      ECODE    ---       #530
      00FEFC57H   LINE      ECODE    ---       #531
      00FEFC77H   LINE      ECODE    ---       #532
      00FEFC79H   LINE      ECODE    ---       #533
      00FEFC79H   LINE      ECODE    ---       #534
      00FEFC99H   LINE      ECODE    ---       #535
      00FEFC9BH   LINE      ECODE    ---       #536
      00FEFC9BH   LINE      ECODE    ---       #537
      00FEFCBBH   LINE      ECODE    ---       #538
      00FEFCBBH   LINE      ECODE    ---       #539
      00FEFCBBH   LINE      ECODE    ---       #540
      00FEFCBBH   LINE      ECODE    ---       #541
      00FEFCBBH   LINE      ECODE    ---       #542
      00FEFCC5H   LINE      ECODE    ---       #543
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFCC6H   BLOCK     ECODE    ---       LVL=0
      00010E7BH   SYMBOL    XDATA    INT       gyro_value
      00FEFCCCH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E7DH   SYMBOL    XDATA    FLOAT     gyro_dat
      ---         BLOCKEND  ---      ---       LVL=1
      00FEFCC6H   LINE      ECODE    ---       #552
      00FEFCCCH   LINE      ECODE    ---       #553
      00FEFCCCH   LINE      ECODE    ---       #554
      00FEFCD8H   LINE      ECODE    ---       #555
      00FEFCF4H   LINE      ECODE    ---       #557
      00FEFCF4H   LINE      ECODE    ---       #558
      00FEFD16H   LINE      ECODE    ---       #559
      00FEFD19H   LINE      ECODE    ---       #560
      00FEFD19H   LINE      ECODE    ---       #561
      00FEFD3BH   LINE      ECODE    ---       #562
      00FEFD3DH   LINE      ECODE    ---       #563
      00FEFD3DH   LINE      ECODE    ---       #564
      00FEFD5FH   LINE      ECODE    ---       #565
      00FEFD61H   LINE      ECODE    ---       #566
      00FEFD61H   LINE      ECODE    ---       #567
      00FEFD83H   LINE      ECODE    ---       #568
      00FEFD85H   LINE      ECODE    ---       #569
      00FEFD85H   LINE      ECODE    ---       #570
      00FEFDA7H   LINE      ECODE    ---       #571
      00FEFDA7H   LINE      ECODE    ---       #572
      00FEFDA7H   LINE      ECODE    ---       #573
      00FEFDA7H   LINE      ECODE    ---       #574
      00FEFDA7H   LINE      ECODE    ---       #575
      00FEFDB1H   LINE      ECODE    ---       #576
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFDB2H   BLOCK     ECODE    ---       LVL=0
      0000021DH   SYMBOL    EDATA    BYTE      return_state
      00FEFDB2H   LINE      ECODE    ---       #585
      00FEFDB2H   LINE      ECODE    ---       #586
      00FEFDB2H   LINE      ECODE    ---       #587
      00FEFDB7H   LINE      ECODE    ---       #588
      00FEFDBFH   LINE      ECODE    ---       #590
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 78


      00FEFDC3H   LINE      ECODE    ---       #592
      00FEFDC3H   LINE      ECODE    ---       #594
      00FEFDCBH   LINE      ECODE    ---       #599
      00FEFDD7H   LINE      ECODE    ---       #600
      00FEFDDDH   LINE      ECODE    ---       #601
      00FEFDE0H   LINE      ECODE    ---       #602
      00FEFDE0H   LINE      ECODE    ---       #603
      00FEFDE7H   LINE      ECODE    ---       #604
      00FEFDEFH   LINE      ECODE    ---       #605
      00FEFDF6H   LINE      ECODE    ---       #606
      00FEFE07H   LINE      ECODE    ---       #607
      00FEFE0FH   LINE      ECODE    ---       #608
      00FEFE17H   LINE      ECODE    ---       #609
      00FEFE21H   LINE      ECODE    ---       #616
      00FEFE2DH   LINE      ECODE    ---       #619
      00FEFE33H   LINE      ECODE    ---       #621
      00FEFE33H   LINE      ECODE    ---       #622
      00FEFE3BH   LINE      ECODE    ---       #623
      00FEFE43H   LINE      ECODE    ---       #624
      00FEFE4BH   LINE      ECODE    ---       #626
      00FEFE53H   LINE      ECODE    ---       #633
      00FEFE5AH   LINE      ECODE    ---       #641
      00FEFE62H   LINE      ECODE    ---       #642
      00FEFE62H   LINE      ECODE    ---       #644
      00FEFE66H   LINE      ECODE    ---       #645
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SEEKFREE_TSL1401
      00FF3FDDH   PUBLIC    ECODE    ---       ccd_send_data?
      00FF3FB8H   PUBLIC    ECODE    ---       ccd_init?
      00FF408FH   PUBLIC    ECODE    ---       ccd_collect?
      00000008H   PUBLIC    EDATA    BYTE      tsl1401_finish_flag
      00000009H   PUBLIC    EDATA    ---       ccd_data_ch1
      00000109H   PUBLIC    EDATA    ---       ccd_data_ch2
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000B0H.6 SFRSYM    DATA     BIT       P36
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 79


      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF3FB8H   BLOCK     ECODE    ---       LVL=0
      00FF3FB8H   LINE      ECODE    ---       #48
      00FF3FB8H   LINE      ECODE    ---       #50
      00FF3FC4H   LINE      ECODE    ---       #51
      00FF3FD0H   LINE      ECODE    ---       #52
      00FF3FDCH   LINE      ECODE    ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3FDDH   BLOCK     ECODE    ---       LVL=0
      00010E4AH   SYMBOL    XDATA    INT       uart_n
      00010E4CH   SYMBOL    XDATA    ---       dat
      00FF3FEDH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E50H   SYMBOL    XDATA    BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FF3FDDH   LINE      ECODE    ---       #63
      00FF3FEDH   LINE      ECODE    ---       #64
      00FF3FEDH   LINE      ECODE    ---       #65
      00FF3FF2H   LINE      ECODE    ---       #66
      00FF3FFDH   LINE      ECODE    ---       #67
      00FF4009H   LINE      ECODE    ---       #68
      00FF4015H   LINE      ECODE    ---       #69
      00FF4020H   LINE      ECODE    ---       #71
      00FF4027H   LINE      ECODE    ---       #73
      00FF404EH   LINE      ECODE    ---       #74
      00FF4077H   LINE      ECODE    ---       #75
      00FF408EH   LINE      ECODE    ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      00FF408FH   BLOCK     ECODE    ---       LVL=0
      00010F40H   SYMBOL    XDATA    BYTE      i
      00FF408FH   LINE      ECODE    ---       #86
      00FF408FH   LINE      ECODE    ---       #87
      00FF408FH   LINE      ECODE    ---       #88
      00FF4094H   LINE      ECODE    ---       #90
      00FF4097H   LINE      ECODE    ---       #91
      00FF409AH   LINE      ECODE    ---       #92
      00FF409DH   LINE      ECODE    ---       #93
      00FF40A0H   LINE      ECODE    ---       #94
      00FF40A3H   LINE      ECODE    ---       #95
      00FF40A6H   LINE      ECODE    ---       #97
      00FF40ADH   LINE      ECODE    ---       #99
      00FF40B0H   LINE      ECODE    ---       #100
      00FF40CFH   LINE      ECODE    ---       #101
      00FF40EEH   LINE      ECODE    ---       #102
      00FF40F1H   LINE      ECODE    ---       #103
      00FF4108H   LINE      ECODE    ---       #104
      00FF410EH   LINE      ECODE    ---       #105
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SEEKFREE_GPS_TAU1201
      00FE3A06H   PUBLIC    ECODE    ---       get_two_points_distance?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 80


      00FE431BH   PUBLIC    ECODE    ---       gps_data_parse?
      00FE46CEH   PUBLIC    ECODE    ---       gps_init?
      00FE3D91H   PUBLIC    ECODE    ---       get_two_points_azimuth?
      00FE4534H   PUBLIC    ECODE    ---       gps_uart_callback?
      00010271H   PUBLIC    XDATA    BYTE      gps_tau1201_flag
      000102F2H   PUBLIC    XDATA    ---       gps_tau1201
      00010426H   PUBLIC    XDATA    INT       gps_gga_state
      00010428H   PUBLIC    XDATA    INT       gps_rmc_state
      00010786H   PUBLIC    XDATA    BYTE      ?get_two_points_distance??BYTE
      00010916H   PUBLIC    XDATA    BYTE      ?get_two_points_azimuth??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FF4E2CH   SYMBOL    HCONST   ---       ?tpl?0001
      00FF4E31H   SYMBOL    HCONST   ---       ?tpl?0002
      00FF4E4DH   SYMBOL    HCONST   ---       ?tpl?0003
      00FF4E58H   SYMBOL    HCONST   ---       ?tpl?0004
      00FF4E63H   SYMBOL    HCONST   ---       ?tpl?0005
      00FF4E6EH   SYMBOL    HCONST   ---       ?tpl?0006
      00FF4E79H   SYMBOL    HCONST   ---       ?tpl?0007
      00FF4E84H   SYMBOL    HCONST   ---       ?tpl?0008
      00FF4E8FH   SYMBOL    HCONST   ---       ?tpl?0009
      00FF4E9AH   SYMBOL    HCONST   ---       ?tpl?000A
      00FF4EA5H   SYMBOL    HCONST   ---       ?tpl?000B
      00FF4EB0H   SYMBOL    HCONST   ---       ?tpl?000C
      00FF4EBBH   SYMBOL    HCONST   ---       ?tpl?000D
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 81


      00FE2F10H   SYMBOL    ECODE    ---       get_double_number
      00FE2E76H   SYMBOL    ECODE    ---       get_float_number
      00FE2FC0H   SYMBOL    ECODE    ---       utc_to_btc
      00FE3903H   SYMBOL    ECODE    ---       gps_gngga_parse
      00FE318EH   SYMBOL    ECODE    ---       gps_gnrmc_parse
      00FE2DECH   SYMBOL    ECODE    ---       get_int_number
      00FE2D18H   SYMBOL    ECODE    ---       get_parameter_index
      0001025AH   SYMBOL    XDATA    ---       gps_tau1201_receiver_fifo
      00010272H   SYMBOL    XDATA    ---       gps_gga_buffer
      00010325H   SYMBOL    XDATA    BYTE      gps_tau1201_state
      00010326H   SYMBOL    XDATA    ---       gps_rmc_buffer
      000103A6H   SYMBOL    XDATA    ---       gps_tau1201_receiver_buffer

      00FE2D18H   BLOCK     ECODE    ---       LVL=0
      00010CE4H   SYMBOL    XDATA    BYTE      num
      00010CE5H   SYMBOL    XDATA    ---       str
      00FE2D26H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010CE9H   SYMBOL    XDATA    BYTE      i
      00010CEAH   SYMBOL    XDATA    BYTE      j
      00010CEBH   SYMBOL    XDATA    ---       temp
      00010CEFH   SYMBOL    XDATA    BYTE      len
      00010CF0H   SYMBOL    XDATA    BYTE      len1
      ---         BLOCKEND  ---      ---       LVL=1
      00FE2D18H   LINE      ECODE    ---       #79
      00FE2D26H   LINE      ECODE    ---       #80
      00FE2D26H   LINE      ECODE    ---       #81
      00FE2D30H   LINE      ECODE    ---       #82
      00FE2D4AH   LINE      ECODE    ---       #83
      00FE2D54H   LINE      ECODE    ---       #85
      00FE2D62H   LINE      ECODE    ---       #87
      00FE2D82H   LINE      ECODE    ---       #88
      00FE2D82H   LINE      ECODE    ---       #90
      00FE2D89H   LINE      ECODE    ---       #92
      00FE2DA3H   LINE      ECODE    ---       #94
      00FE2DAEH   LINE      ECODE    ---       #95
      00FE2DAEH   LINE      ECODE    ---       #96
      00FE2DBCH   LINE      ECODE    ---       #98
      00FE2DCCH   LINE      ECODE    ---       #99
      00FE2DCEH   LINE      ECODE    ---       #100
      00FE2DE7H   LINE      ECODE    ---       #103
      00FE2DEBH   LINE      ECODE    ---       #104
      ---         BLOCKEND  ---      ---       LVL=0

      00FE2DECH   BLOCK     ECODE    ---       LVL=0
      00010C3FH   SYMBOL    XDATA    ---       s
      00FE2DF6H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010C43H   SYMBOL    XDATA    ---       buf
      00010C4DH   SYMBOL    XDATA    BYTE      i
      00010C4EH   SYMBOL    XDATA    INT       return_value
      ---         BLOCKEND  ---      ---       LVL=1
      00FE2DECH   LINE      ECODE    ---       #113
      00FE2DF6H   LINE      ECODE    ---       #114
      00FE2DF6H   LINE      ECODE    ---       #116
      00FE2DFBH   LINE      ECODE    ---       #117
      00FE2E03H   LINE      ECODE    ---       #118
      00FE2E16H   LINE      ECODE    ---       #119
      00FE2E26H   LINE      ECODE    ---       #120
      00FE2E46H   LINE      ECODE    ---       #121
      00FE2E5DH   LINE      ECODE    ---       #122
      00FE2E6FH   LINE      ECODE    ---       #123
      00FE2E75H   LINE      ECODE    ---       #124
      ---         BLOCKEND  ---      ---       LVL=0

      00FE2E76H   BLOCK     ECODE    ---       LVL=0
      00010B88H   SYMBOL    XDATA    ---       s
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 82


      00FE2E80H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010B8CH   SYMBOL    XDATA    BYTE      i
      00010B8DH   SYMBOL    XDATA    ---       buf
      00010B9CH   SYMBOL    XDATA    FLOAT     return_value
      ---         BLOCKEND  ---      ---       LVL=1
      00FE2E76H   LINE      ECODE    ---       #133
      00FE2E80H   LINE      ECODE    ---       #134
      00FE2E80H   LINE      ECODE    ---       #135
      00FE2E85H   LINE      ECODE    ---       #137
      00FE2E91H   LINE      ECODE    ---       #139
      00FE2EA4H   LINE      ECODE    ---       #140
      00FE2EB4H   LINE      ECODE    ---       #141
      00FE2ED4H   LINE      ECODE    ---       #142
      00FE2EEBH   LINE      ECODE    ---       #143
      00FE2F05H   LINE      ECODE    ---       #144
      00FE2F0FH   LINE      ECODE    ---       #145
      ---         BLOCKEND  ---      ---       LVL=0

      00FE2F10H   BLOCK     ECODE    ---       LVL=0
      00010B23H   SYMBOL    XDATA    ---       s
      00FE2F1AH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010B27H   SYMBOL    XDATA    BYTE      i
      00010B28H   SYMBOL    XDATA    ---       buf
      00010B37H   SYMBOL    XDATA    DOUBLE    return_value
      ---         BLOCKEND  ---      ---       LVL=1
      00FE2F10H   LINE      ECODE    ---       #154
      00FE2F1AH   LINE      ECODE    ---       #155
      00FE2F1AH   LINE      ECODE    ---       #156
      00FE2F1FH   LINE      ECODE    ---       #158
      00FE2F35H   LINE      ECODE    ---       #160
      00FE2F48H   LINE      ECODE    ---       #161
      00FE2F58H   LINE      ECODE    ---       #162
      00FE2F78H   LINE      ECODE    ---       #163
      00FE2F8FH   LINE      ECODE    ---       #164
      00FE2FADH   LINE      ECODE    ---       #165
      00FE2FBFH   LINE      ECODE    ---       #166
      ---         BLOCKEND  ---      ---       LVL=0

      00FE2FC0H   BLOCK     ECODE    ---       LVL=0
      00010E9EH   SYMBOL    XDATA    ---       time
      00FE2FCAH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010EA2H   SYMBOL    XDATA    BYTE      day_num
      ---         BLOCKEND  ---      ---       LVL=1
      00FE2FC0H   LINE      ECODE    ---       #175
      00FE2FCAH   LINE      ECODE    ---       #176
      00FE2FCAH   LINE      ECODE    ---       #177
      00FE2FCFH   LINE      ECODE    ---       #179
      00FE2FF5H   LINE      ECODE    ---       #180
      00FE300DH   LINE      ECODE    ---       #182
      00FE3023H   LINE      ECODE    ---       #183
      00FE303BH   LINE      ECODE    ---       #185
      00FE304FH   LINE      ECODE    ---       #187
      00FE3055H   LINE      ECODE    ---       #188
      00FE30A0H   LINE      ECODE    ---       #190
      00FE30ABH   LINE      ECODE    ---       #191
      00FE30ADH   LINE      ECODE    ---       #195
      00FE30B3H   LINE      ECODE    ---       #196
      00FE3103H   LINE      ECODE    ---       #198
      00FE3109H   LINE      ECODE    ---       #199
      00FE3109H   LINE      ECODE    ---       #202
      00FE3122H   LINE      ECODE    ---       #204
      00FE3135H   LINE      ECODE    ---       #205
      00FE314CH   LINE      ECODE    ---       #206
      00FE3161H   LINE      ECODE    ---       #208
      00FE3177H   LINE      ECODE    ---       #209
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 83


      00FE318DH   LINE      ECODE    ---       #210
      00FE318DH   LINE      ECODE    ---       #212
      00FE318DH   LINE      ECODE    ---       #213
      ---         BLOCKEND  ---      ---       LVL=0

      00FE318EH   BLOCK     ECODE    ---       LVL=0
      000108D3H   SYMBOL    XDATA    ---       line
      000108D7H   SYMBOL    XDATA    ---       gps
      00FE31A2H   BLOCK     ECODE    NEAR LAB  LVL=1
      000108DBH   SYMBOL    XDATA    BYTE      state
      000108DCH   SYMBOL    XDATA    BYTE      temp
      000108DDH   SYMBOL    XDATA    DOUBLE    latitude
      000108E5H   SYMBOL    XDATA    DOUBLE    longitude
      000108EDH   SYMBOL    XDATA    DOUBLE    lati_cent_tmp
      000108F5H   SYMBOL    XDATA    DOUBLE    lati_second_tmp
      000108FDH   SYMBOL    XDATA    DOUBLE    long_cent_tmp
      00010905H   SYMBOL    XDATA    DOUBLE    long_second_tmp
      0001090DH   SYMBOL    XDATA    FLOAT     speed_tmp
      00010911H   SYMBOL    XDATA    ---       buf
      00010915H   SYMBOL    XDATA    BYTE      return_state
      ---         BLOCKEND  ---      ---       LVL=1
      00FE318EH   LINE      ECODE    ---       #223
      00FE31A2H   LINE      ECODE    ---       #224
      00FE31A2H   LINE      ECODE    ---       #225
      00FE31ACH   LINE      ECODE    ---       #227
      00FE31C2H   LINE      ECODE    ---       #228
      00FE31D8H   LINE      ECODE    ---       #230
      00FE3204H   LINE      ECODE    ---       #231
      00FE3230H   LINE      ECODE    ---       #232
      00FE323CH   LINE      ECODE    ---       #233
      00FE3250H   LINE      ECODE    ---       #234
      00FE3255H   LINE      ECODE    ---       #236
      00FE3279H   LINE      ECODE    ---       #238
      00FE328BH   LINE      ECODE    ---       #239
      00FE3297H   LINE      ECODE    ---       #241
      00FE329DH   LINE      ECODE    ---       #242
      00FE32B0H   LINE      ECODE    ---       #243
      00FE32E1H   LINE      ECODE    ---       #244
      00FE3312H   LINE      ECODE    ---       #246
      00FE3344H   LINE      ECODE    ---       #247
      00FE3376H   LINE      ECODE    ---       #249
      00FE33A7H   LINE      ECODE    ---       #250
      00FE33F1H   LINE      ECODE    ---       #251
      00FE341AH   LINE      ECODE    ---       #252
      00FE346AH   LINE      ECODE    ---       #253
      00FE3493H   LINE      ECODE    ---       #255
      00FE34C4H   LINE      ECODE    ---       #256
      00FE350EH   LINE      ECODE    ---       #257
      00FE3537H   LINE      ECODE    ---       #258
      00FE3587H   LINE      ECODE    ---       #259
      00FE35B0H   LINE      ECODE    ---       #261
      00FE360DH   LINE      ECODE    ---       #262
      00FE366AH   LINE      ECODE    ---       #264
      00FE3694H   LINE      ECODE    ---       #265
      00FE36C1H   LINE      ECODE    ---       #266
      00FE36F8H   LINE      ECODE    ---       #267
      00FE36F8H   LINE      ECODE    ---       #270
      00FE3741H   LINE      ECODE    ---       #271
      00FE378CH   LINE      ECODE    ---       #272
      00FE37D7H   LINE      ECODE    ---       #273
      00FE37EAH   LINE      ECODE    ---       #274
      00FE383FH   LINE      ECODE    ---       #275
      00FE3896H   LINE      ECODE    ---       #276
      00FE38F1H   LINE      ECODE    ---       #278
      00FE38FEH   LINE      ECODE    ---       #280
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 84


      00FE3902H   LINE      ECODE    ---       #281
      ---         BLOCKEND  ---      ---       LVL=0

      00FE3903H   BLOCK     ECODE    ---       LVL=0
      00010CBBH   SYMBOL    XDATA    ---       line
      00010CBFH   SYMBOL    XDATA    ---       gps
      00FE3919H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010CC3H   SYMBOL    XDATA    BYTE      state
      00010CC4H   SYMBOL    XDATA    ---       buf
      00010CC8H   SYMBOL    XDATA    BYTE      return_state
      ---         BLOCKEND  ---      ---       LVL=1
      00FE3903H   LINE      ECODE    ---       #291
      00FE3919H   LINE      ECODE    ---       #292
      00FE3919H   LINE      ECODE    ---       #293
      00FE391EH   LINE      ECODE    ---       #294
      00FE3932H   LINE      ECODE    ---       #295
      00FE3937H   LINE      ECODE    ---       #297
      00FE395BH   LINE      ECODE    ---       #299
      00FE3967H   LINE      ECODE    ---       #301
      00FE399AH   LINE      ECODE    ---       #302
      00FE39F9H   LINE      ECODE    ---       #303
      00FE39FFH   LINE      ECODE    ---       #304
      00FE39FFH   LINE      ECODE    ---       #306
      00FE3A03H   LINE      ECODE    ---       #307
      ---         BLOCKEND  ---      ---       LVL=0

      00FE3A06H   BLOCK     ECODE    ---       LVL=0
      00010786H   SYMBOL    XDATA    DOUBLE    latitude1
      0001078EH   SYMBOL    XDATA    DOUBLE    longitude1
      00010796H   SYMBOL    XDATA    DOUBLE    latitude2
      0001079EH   SYMBOL    XDATA    DOUBLE    longitude2
      00FE3A18H   BLOCK     ECODE    NEAR LAB  LVL=1
      000107A6H   SYMBOL    XDATA    DOUBLE    EARTH_RADIUS
      000107AEH   SYMBOL    XDATA    DOUBLE    rad_latitude1
      000107B6H   SYMBOL    XDATA    DOUBLE    rad_latitude2
      000107BEH   SYMBOL    XDATA    DOUBLE    rad_longitude1
      000107C6H   SYMBOL    XDATA    DOUBLE    rad_longitude2
      000107CEH   SYMBOL    XDATA    DOUBLE    distance
      000107D6H   SYMBOL    XDATA    DOUBLE    a
      000107DEH   SYMBOL    XDATA    DOUBLE    b
      ---         BLOCKEND  ---      ---       LVL=1
      00FE3A06H   LINE      ECODE    ---       #319
      00FE3A18H   LINE      ECODE    ---       #320
      00FE3A18H   LINE      ECODE    ---       #321
      00FE3A38H   LINE      ECODE    ---       #322
      00FE3A4EH   LINE      ECODE    ---       #323
      00FE3A64H   LINE      ECODE    ---       #324
      00FE3A7AH   LINE      ECODE    ---       #325
      00FE3A90H   LINE      ECODE    ---       #326
      00FE3AA6H   LINE      ECODE    ---       #327
      00FE3ABCH   LINE      ECODE    ---       #328
      00FE3AD2H   LINE      ECODE    ---       #330
      00FE3B18H   LINE      ECODE    ---       #331
      00FE3B5EH   LINE      ECODE    ---       #332
      00FE3BA4H   LINE      ECODE    ---       #333
      00FE3BEAH   LINE      ECODE    ---       #335
      00FE3C24H   LINE      ECODE    ---       #336
      00FE3C5EH   LINE      ECODE    ---       #338
      00FE3D44H   LINE      ECODE    ---       #339
      00FE3D7EH   LINE      ECODE    ---       #341
      00FE3D90H   LINE      ECODE    ---       #342
      ---         BLOCKEND  ---      ---       LVL=0

      00FE3D91H   BLOCK     ECODE    ---       LVL=0
      00010916H   SYMBOL    XDATA    DOUBLE    latitude1
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 85


      0001091EH   SYMBOL    XDATA    DOUBLE    longitude1
      00010926H   SYMBOL    XDATA    DOUBLE    latitude2
      0001092EH   SYMBOL    XDATA    DOUBLE    longitude2
      00FE3DA3H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010936H   SYMBOL    XDATA    DOUBLE    x
      0001093EH   SYMBOL    XDATA    DOUBLE    y
      00010946H   SYMBOL    XDATA    DOUBLE    angle
      ---         BLOCKEND  ---      ---       LVL=1
      00FE3D91H   LINE      ECODE    ---       #354
      00FE3DA3H   LINE      ECODE    ---       #355
      00FE3DA3H   LINE      ECODE    ---       #356
      00FE3DB9H   LINE      ECODE    ---       #357
      00FE3DCFH   LINE      ECODE    ---       #358
      00FE3DE5H   LINE      ECODE    ---       #361
      00FE3E2BH   LINE      ECODE    ---       #362
      00FE3E71H   LINE      ECODE    ---       #363
      00FE3EB7H   LINE      ECODE    ---       #364
      00FE3EFDH   LINE      ECODE    ---       #366
      00FE3F61H   LINE      ECODE    ---       #367
      00FE402BH   LINE      ECODE    ---       #369
      00FE408BH   LINE      ECODE    ---       #372
      00FE40CEH   LINE      ECODE    ---       #374
      00FE40ECH   LINE      ECODE    ---       #376
      00FE4115H   LINE      ECODE    ---       #377
      00FE4131H   LINE      ECODE    ---       #378
      00FE4134H   LINE      ECODE    ---       #380
      00FE4152H   LINE      ECODE    ---       #382
      00FE4175H   LINE      ECODE    ---       #383
      00FE4191H   LINE      ECODE    ---       #384
      00FE4194H   LINE      ECODE    ---       #387
      00FE41FDH   LINE      ECODE    ---       #388
      00FE4264H   LINE      ECODE    ---       #389
      00FE4264H   LINE      ECODE    ---       #391
      00FE4286H   LINE      ECODE    ---       #393
      00FE42B8H   LINE      ECODE    ---       #394
      00FE42BAH   LINE      ECODE    ---       #395
      00FE42D6H   LINE      ECODE    ---       #397
      00FE4308H   LINE      ECODE    ---       #398
      00FE4308H   LINE      ECODE    ---       #400
      00FE431AH   LINE      ECODE    ---       #401
      ---         BLOCKEND  ---      ---       LVL=0

      00FE431BH   BLOCK     ECODE    ---       LVL=0
      00010D30H   SYMBOL    XDATA    BYTE      return_state
      00010D31H   SYMBOL    XDATA    ---       check_buffer
      00010D36H   SYMBOL    XDATA    BYTE      bbc_xor_origin
      00010D37H   SYMBOL    XDATA    BYTE      bbc_xor_calculation
      00010D38H   SYMBOL    XDATA    DWORD     data_len
      00FE431BH   LINE      ECODE    ---       #410
      00FE431BH   LINE      ECODE    ---       #411
      00FE431BH   LINE      ECODE    ---       #412
      00FE4320H   LINE      ECODE    ---       #413
      00FE4336H   LINE      ECODE    ---       #414
      00FE433BH   LINE      ECODE    ---       #415
      00FE4340H   LINE      ECODE    ---       #416
      00FE434CH   LINE      ECODE    ---       #418
      00FE434CH   LINE      ECODE    ---       #420
      00FE435DH   LINE      ECODE    ---       #422
      00FE4367H   LINE      ECODE    ---       #423
      00FE438BH   LINE      ECODE    ---       #424
      00FE439DH   LINE      ECODE    ---       #425
      00FE43B5H   LINE      ECODE    ---       #427
      00FE43D7H   LINE      ECODE    ---       #428
      00FE440CH   LINE      ECODE    ---       #429
      00FE441AH   LINE      ECODE    ---       #432
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 86


      00FE4420H   LINE      ECODE    ---       #433
      00FE4423H   LINE      ECODE    ---       #434
      00FE4423H   LINE      ECODE    ---       #436
      00FE4436H   LINE      ECODE    ---       #437
      00FE4436H   LINE      ECODE    ---       #438
      00FE443EH   LINE      ECODE    ---       #440
      00FE444FH   LINE      ECODE    ---       #442
      00FE4459H   LINE      ECODE    ---       #443
      00FE447DH   LINE      ECODE    ---       #444
      00FE448FH   LINE      ECODE    ---       #446
      00FE44A7H   LINE      ECODE    ---       #448
      00FE44C9H   LINE      ECODE    ---       #449
      00FE44FEH   LINE      ECODE    ---       #450
      00FE450CH   LINE      ECODE    ---       #453
      00FE4512H   LINE      ECODE    ---       #454
      00FE4514H   LINE      ECODE    ---       #455
      00FE4514H   LINE      ECODE    ---       #457
      00FE4527H   LINE      ECODE    ---       #458
      00FE4527H   LINE      ECODE    ---       #459
      00FE452FH   LINE      ECODE    ---       #461
      00FE452FH   LINE      ECODE    ---       #462
      00FE4533H   LINE      ECODE    ---       #463
      ---         BLOCKEND  ---      ---       LVL=0

      00FE4534H   BLOCK     ECODE    ---       LVL=0
      00010D6CH   SYMBOL    XDATA    BYTE      uart_dat
      00FE4538H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010D6DH   SYMBOL    XDATA    ---       temp_gps
      00010D73H   SYMBOL    XDATA    DWORD     temp_length
      ---         BLOCKEND  ---      ---       LVL=1
      00FE4534H   LINE      ECODE    ---       #473
      00FE4538H   LINE      ECODE    ---       #474
      00FE4538H   LINE      ECODE    ---       #476
      00FE4544H   LINE      ECODE    ---       #478
      00FE4550H   LINE      ECODE    ---       #480
      00FE4572H   LINE      ECODE    ---       #481
      00FE457EH   LINE      ECODE    ---       #484
      00FE458CH   LINE      ECODE    ---       #485
      00FE45BCH   LINE      ECODE    ---       #488
      00FE45DDH   LINE      ECODE    ---       #491
      00FE45EEH   LINE      ECODE    ---       #493
      00FE45F8H   LINE      ECODE    ---       #494
      00FE460EH   LINE      ECODE    ---       #495
      00FE463CH   LINE      ECODE    ---       #496
      00FE463EH   LINE      ECODE    ---       #498
      00FE465FH   LINE      ECODE    ---       #501
      00FE466DH   LINE      ECODE    ---       #503
      00FE4677H   LINE      ECODE    ---       #504
      00FE468DH   LINE      ECODE    ---       #505
      00FE46BBH   LINE      ECODE    ---       #506
      00FE46BBH   LINE      ECODE    ---       #510
      00FE46C7H   LINE      ECODE    ---       #512
      00FE46CDH   LINE      ECODE    ---       #513
      00FE46CDH   LINE      ECODE    ---       #515
      ---         BLOCKEND  ---      ---       LVL=0

      00FE46CEH   BLOCK     ECODE    ---       LVL=0
      00010687H   SYMBOL    XDATA    ---       set_rate
      000106A3H   SYMBOL    XDATA    ---       open_gga
      000106AEH   SYMBOL    XDATA    ---       open_rmc
      000106B9H   SYMBOL    XDATA    ---       close_gll
      000106C4H   SYMBOL    XDATA    ---       close_gsa
      000106CFH   SYMBOL    XDATA    ---       close_grs
      000106DAH   SYMBOL    XDATA    ---       close_gsv
      000106E5H   SYMBOL    XDATA    ---       close_vtg
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 87


      000106F0H   SYMBOL    XDATA    ---       close_zda
      000106FBH   SYMBOL    XDATA    ---       close_gst
      00010706H   SYMBOL    XDATA    ---       close_txt
      00010711H   SYMBOL    XDATA    ---       close_txt_ant
      00FE46CEH   LINE      ECODE    ---       #524
      00FE46CEH   LINE      ECODE    ---       #525
      00FE46CEH   LINE      ECODE    ---       #526
      00FE46E4H   LINE      ECODE    ---       #527
      00FE46FAH   LINE      ECODE    ---       #528
      00FE4710H   LINE      ECODE    ---       #530
      00FE4726H   LINE      ECODE    ---       #531
      00FE473CH   LINE      ECODE    ---       #532
      00FE4752H   LINE      ECODE    ---       #533
      00FE4768H   LINE      ECODE    ---       #534
      00FE477EH   LINE      ECODE    ---       #535
      00FE4794H   LINE      ECODE    ---       #536
      00FE47AAH   LINE      ECODE    ---       #537
      00FE47C0H   LINE      ECODE    ---       #538
      00FE47D6H   LINE      ECODE    ---       #540
      00FE4804H   LINE      ECODE    ---       #541
      00FE480CH   LINE      ECODE    ---       #543
      00FE4832H   LINE      ECODE    ---       #545
      00FE4850H   LINE      ECODE    ---       #546
      00FE4858H   LINE      ECODE    ---       #548
      00FE4876H   LINE      ECODE    ---       #549
      00FE487EH   LINE      ECODE    ---       #550
      00FE489CH   LINE      ECODE    ---       #551
      00FE48A4H   LINE      ECODE    ---       #552
      00FE48C2H   LINE      ECODE    ---       #553
      00FE48CAH   LINE      ECODE    ---       #554
      00FE48E8H   LINE      ECODE    ---       #555
      00FE48F0H   LINE      ECODE    ---       #556
      00FE490EH   LINE      ECODE    ---       #557
      00FE4916H   LINE      ECODE    ---       #558
      00FE4934H   LINE      ECODE    ---       #559
      00FE493CH   LINE      ECODE    ---       #560
      00FE495AH   LINE      ECODE    ---       #561
      00FE4962H   LINE      ECODE    ---       #562
      00FE4980H   LINE      ECODE    ---       #563
      00FE4988H   LINE      ECODE    ---       #564
      00FE49A6H   LINE      ECODE    ---       #565
      00FE49AEH   LINE      ECODE    ---       #566
      00FE49CCH   LINE      ECODE    ---       #567
      00FE49D4H   LINE      ECODE    ---       #568
      00FE49F2H   LINE      ECODE    ---       #569
      00FE49FAH   LINE      ECODE    ---       #571
      00FE4A00H   LINE      ECODE    ---       #573
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SEEKFREE_IPS200_SPI
      00FEB570H   PUBLIC    ECODE    ---       ips200_show_float?
      00FEAFFEH   PUBLIC    ECODE    ---       ips200_draw_point?
      00FEB307H   PUBLIC    ECODE    ---       ips200_show_string?
      00FEB1C2H   PUBLIC    ECODE    ---       ips200_show_char?
      00FEAFD9H   PUBLIC    ECODE    ---       ips200_set_color?
      00FEB716H   PUBLIC    ECODE    ---       ips200_show_wave?
      00FEADA9H   PUBLIC    ECODE    ---       ips200_write_16bit_data_spi?
      00FEB8FFH   PUBLIC    ECODE    ---       ips200_init_spi?
      00FEB482H   PUBLIC    ECODE    ---       ips200_show_uint?
      00FEAFC6H   PUBLIC    ECODE    ---       ips200_set_font?
      00FEAF7BH   PUBLIC    ECODE    ---       ips200_set_dir?
      00FEB38EH   PUBLIC    ECODE    ---       ips200_show_int?
      00FEAE73H   PUBLIC    ECODE    ---       ips200_clear?
      00FEAD9CH   PUBLIC    ECODE    ---       ips200_write_8bit_data_spi?
      00FEAEF4H   PUBLIC    ECODE    ---       ips200_full?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 88


      00FEB03BH   PUBLIC    ECODE    ---       ips200_draw_line?
      00010BB8H   PUBLIC    XDATA    BYTE      ?ips200_draw_line??BYTE
      000109EDH   PUBLIC    XDATA    BYTE      ?ips200_show_float??BYTE
      00010A8FH   PUBLIC    XDATA    BYTE      ?ips200_show_wave??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000A0H.7 SFRSYM    DATA     BIT       P27
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000A0H.2 SFRSYM    DATA     BIT       P22
      000000A0H.1 SFRSYM    DATA     BIT       P21
      000000A0H.0 SFRSYM    DATA     BIT       P20
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FEADD8H   SYMBOL    ECODE    ---       ips200_write_command
      00FEADF7H   SYMBOL    ECODE    ---       ips200_write_8bit_data
      00FEAE0DH   SYMBOL    ECODE    ---       ips200_write_16bit_data
      00FEAE27H   SYMBOL    ECODE    ---       ips200_set_region
      00010CC9H   SYMBOL    XDATA    WORD      ips200_pencolor
      00010CCBH   SYMBOL    XDATA    WORD      ips200_bgcolor
      00010CCDH   SYMBOL    XDATA    INT       ips200_display_font
      00010CCFH   SYMBOL    XDATA    INT       ips200_display_dir
      00010CD1H   SYMBOL    XDATA    WORD      ips200_x_max
      00010CD3H   SYMBOL    XDATA    INT       ips200_display_type
      00010CD5H   SYMBOL    XDATA    WORD      ips200_y_max

      00FEAD9CH   BLOCK     ECODE    ---       LVL=0
      00010F41H   SYMBOL    XDATA    BYTE      dat
      00FEAD9CH   LINE      ECODE    ---       #65
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 89


      00FEADA0H   LINE      ECODE    ---       #67
      00FEADA8H   LINE      ECODE    ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      00FEADA9H   BLOCK     ECODE    ---       LVL=0
      00010EDFH   SYMBOL    XDATA    WORD      dat
      00FEADAFH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010EE1H   SYMBOL    XDATA    ---       dat1
      ---         BLOCKEND  ---      ---       LVL=1
      00FEADA9H   LINE      ECODE    ---       #78
      00FEADAFH   LINE      ECODE    ---       #79
      00FEADAFH   LINE      ECODE    ---       #81
      00FEADBDH   LINE      ECODE    ---       #82
      00FEADC7H   LINE      ECODE    ---       #84
      00FEADCFH   LINE      ECODE    ---       #85
      00FEADD7H   LINE      ECODE    ---       #86
      ---         BLOCKEND  ---      ---       LVL=0

      00FEADD8H   BLOCK     ECODE    ---       LVL=0
      00010F42H   SYMBOL    XDATA    BYTE      command
      00FEADD8H   LINE      ECODE    ---       #96
      00FEADDCH   LINE      ECODE    ---       #99
      00FEADDFH   LINE      ECODE    ---       #100
      00FEADE2H   LINE      ECODE    ---       #101
      00FEADE5H   LINE      ECODE    ---       #102
      00FEADEDH   LINE      ECODE    ---       #103
      00FEADF0H   LINE      ECODE    ---       #104
      00FEADF3H   LINE      ECODE    ---       #105
      00FEADF6H   LINE      ECODE    ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      00FEADF7H   BLOCK     ECODE    ---       LVL=0
      00010F43H   SYMBOL    XDATA    BYTE      dat
      00FEADF7H   LINE      ECODE    ---       #116
      00FEADFBH   LINE      ECODE    ---       #118
      00FEADFEH   LINE      ECODE    ---       #119
      00FEAE01H   LINE      ECODE    ---       #120
      00FEAE09H   LINE      ECODE    ---       #121
      00FEAE0CH   LINE      ECODE    ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAE0DH   BLOCK     ECODE    ---       LVL=0
      00010F29H   SYMBOL    XDATA    WORD      dat
      00FEAE0DH   LINE      ECODE    ---       #131
      00FEAE13H   LINE      ECODE    ---       #133
      00FEAE16H   LINE      ECODE    ---       #134
      00FEAE19H   LINE      ECODE    ---       #135
      00FEAE23H   LINE      ECODE    ---       #136
      00FEAE26H   LINE      ECODE    ---       #137
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAE27H   BLOCK     ECODE    ---       LVL=0
      00010E12H   SYMBOL    XDATA    WORD      x1
      00010E14H   SYMBOL    XDATA    WORD      y1
      00010E16H   SYMBOL    XDATA    WORD      x2
      00010E18H   SYMBOL    XDATA    WORD      y2
      00FEAE27H   LINE      ECODE    ---       #149
      00FEAE3FH   LINE      ECODE    ---       #153
      00FEAE44H   LINE      ECODE    ---       #154
      00FEAE4DH   LINE      ECODE    ---       #155
      00FEAE56H   LINE      ECODE    ---       #157
      00FEAE5BH   LINE      ECODE    ---       #158
      00FEAE64H   LINE      ECODE    ---       #159
      00FEAE6DH   LINE      ECODE    ---       #161
      00FEAE72H   LINE      ECODE    ---       #162
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 90


      ---         BLOCKEND  ---      ---       LVL=0

      00FEAE73H   BLOCK     ECODE    ---       LVL=0
      00010EE3H   SYMBOL    XDATA    WORD      i
      00010EE5H   SYMBOL    XDATA    WORD      j
      00FEAE73H   LINE      ECODE    ---       #172
      00FEAE73H   LINE      ECODE    ---       #173
      00FEAE73H   LINE      ECODE    ---       #175
      00FEAE76H   LINE      ECODE    ---       #177
      00FEAE95H   LINE      ECODE    ---       #178
      00FEAE9FH   LINE      ECODE    ---       #180
      00FEAEA9H   LINE      ECODE    ---       #182
      00FEAEB2H   LINE      ECODE    ---       #183
      00FEAED1H   LINE      ECODE    ---       #184
      00FEAEF0H   LINE      ECODE    ---       #186
      00FEAEF3H   LINE      ECODE    ---       #188
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAEF4H   BLOCK     ECODE    ---       LVL=0
      00010E81H   SYMBOL    XDATA    WORD      color
      00FEAEFAH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E83H   SYMBOL    XDATA    WORD      i
      00010E85H   SYMBOL    XDATA    WORD      j
      ---         BLOCKEND  ---      ---       LVL=1
      00FEAEF4H   LINE      ECODE    ---       #197
      00FEAEFAH   LINE      ECODE    ---       #198
      00FEAEFAH   LINE      ECODE    ---       #201
      00FEAEFDH   LINE      ECODE    ---       #203
      00FEAF1CH   LINE      ECODE    ---       #204
      00FEAF26H   LINE      ECODE    ---       #206
      00FEAF30H   LINE      ECODE    ---       #208
      00FEAF39H   LINE      ECODE    ---       #209
      00FEAF58H   LINE      ECODE    ---       #210
      00FEAF77H   LINE      ECODE    ---       #212
      00FEAF7AH   LINE      ECODE    ---       #214
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAF7BH   BLOCK     ECODE    ---       LVL=0
      00010F2BH   SYMBOL    XDATA    INT       dir
      00FEAF7BH   LINE      ECODE    ---       #223
      00FEAF81H   LINE      ECODE    ---       #225
      00FEAF8DH   LINE      ECODE    ---       #226
      00FEAF9BH   LINE      ECODE    ---       #228
      00FEAFA5H   LINE      ECODE    ---       #229
      00FEAFAFH   LINE      ECODE    ---       #230
      00FEAFB1H   LINE      ECODE    ---       #233
      00FEAFBBH   LINE      ECODE    ---       #234
      00FEAFC5H   LINE      ECODE    ---       #235
      00FEAFC5H   LINE      ECODE    ---       #236
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAFC6H   BLOCK     ECODE    ---       LVL=0
      00010F2DH   SYMBOL    XDATA    INT       font
      00FEAFC6H   LINE      ECODE    ---       #245
      00FEAFCCH   LINE      ECODE    ---       #247
      00FEAFD8H   LINE      ECODE    ---       #248
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAFD9H   BLOCK     ECODE    ---       LVL=0
      00010EE7H   SYMBOL    XDATA    WORD      pen
      00010EE9H   SYMBOL    XDATA    WORD      bgcolor
      00FEAFD9H   LINE      ECODE    ---       #258
      00FEAFE5H   LINE      ECODE    ---       #260
      00FEAFF1H   LINE      ECODE    ---       #261
      00FEAFFDH   LINE      ECODE    ---       #262
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 91


      ---         BLOCKEND  ---      ---       LVL=0

      00FEAFFEH   BLOCK     ECODE    ---       LVL=0
      00010E87H   SYMBOL    XDATA    WORD      x
      00010E89H   SYMBOL    XDATA    WORD      y
      00010E8BH   SYMBOL    XDATA    WORD      color
      00FEAFFEH   LINE      ECODE    ---       #273
      00FEB010H   LINE      ECODE    ---       #276
      00FEB013H   LINE      ECODE    ---       #278
      00FEB02EH   LINE      ECODE    ---       #279
      00FEB037H   LINE      ECODE    ---       #281
      00FEB03AH   LINE      ECODE    ---       #283
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB03BH   BLOCK     ECODE    ---       LVL=0
      00010BB8H   SYMBOL    XDATA    WORD      x_start
      00010BBAH   SYMBOL    XDATA    WORD      y_start
      00010BBCH   SYMBOL    XDATA    WORD      x_end
      00010BBEH   SYMBOL    XDATA    WORD      y_end
      00010BC0H   SYMBOL    XDATA    WORD      color
      00FEB053H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010BC2H   SYMBOL    XDATA    INT       dx
      00010BC4H   SYMBOL    XDATA    INT       dy
      00010BC6H   SYMBOL    XDATA    INT       sx
      00010BC8H   SYMBOL    XDATA    INT       sy
      00010BCAH   SYMBOL    XDATA    INT       err
      00010BCCH   SYMBOL    XDATA    INT       e2
      ---         BLOCKEND  ---      ---       LVL=1
      00FEB03BH   LINE      ECODE    ---       #296
      00FEB053H   LINE      ECODE    ---       #297
      00FEB053H   LINE      ECODE    ---       #304
      00FEB087H   LINE      ECODE    ---       #305
      00FEB0BBH   LINE      ECODE    ---       #306
      00FEB0DBH   LINE      ECODE    ---       #307
      00FEB0FBH   LINE      ECODE    ---       #308
      00FEB10FH   LINE      ECODE    ---       #311
      00FEB10FH   LINE      ECODE    ---       #317
      00FEB125H   LINE      ECODE    ---       #321
      00FEB145H   LINE      ECODE    ---       #324
      00FEB153H   LINE      ECODE    ---       #326
      00FEB167H   LINE      ECODE    ---       #328
      00FEB178H   LINE      ECODE    ---       #329
      00FEB189H   LINE      ECODE    ---       #330
      00FEB189H   LINE      ECODE    ---       #332
      00FEB19CH   LINE      ECODE    ---       #334
      00FEB1ADH   LINE      ECODE    ---       #335
      00FEB1BEH   LINE      ECODE    ---       #336
      00FEB1C1H   LINE      ECODE    ---       #338
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB1C2H   BLOCK     ECODE    ---       LVL=0
      00010DE0H   SYMBOL    XDATA    WORD      x
      00010DE2H   SYMBOL    XDATA    WORD      y
      00010DE4H   SYMBOL    XDATA    CHAR      dat
      00FEB1D2H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010DE5H   SYMBOL    XDATA    BYTE      i
      00010DE6H   SYMBOL    XDATA    BYTE      j
      00010DE7H   SYMBOL    XDATA    BYTE      temp_top
      00010DE8H   SYMBOL    XDATA    BYTE      temp_bottom
      ---         BLOCKEND  ---      ---       LVL=1
      00FEB1C2H   LINE      ECODE    ---       #349
      00FEB1D2H   LINE      ECODE    ---       #350
      00FEB1D2H   LINE      ECODE    ---       #355
      00FEB1D5H   LINE      ECODE    ---       #357
      00FEB1E2H   LINE      ECODE    ---       #360
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 92


      00FEB1E2H   LINE      ECODE    ---       #361
      00FEB1EAH   LINE      ECODE    ---       #363
      00FEB21BH   LINE      ECODE    ---       #365
      00FEB242H   LINE      ECODE    ---       #366
      00FEB269H   LINE      ECODE    ---       #367
      00FEB270H   LINE      ECODE    ---       #369
      00FEB277H   LINE      ECODE    ---       #371
      00FEB280H   LINE      ECODE    ---       #372
      00FEB282H   LINE      ECODE    ---       #375
      00FEB28BH   LINE      ECODE    ---       #376
      00FEB28BH   LINE      ECODE    ---       #377
      00FEB292H   LINE      ECODE    ---       #378
      00FEB2A9H   LINE      ECODE    ---       #379
      00FEB2B0H   LINE      ECODE    ---       #381
      00FEB2B7H   LINE      ECODE    ---       #383
      00FEB2C0H   LINE      ECODE    ---       #384
      00FEB2C2H   LINE      ECODE    ---       #387
      00FEB2CBH   LINE      ECODE    ---       #388
      00FEB2CBH   LINE      ECODE    ---       #389
      00FEB2D2H   LINE      ECODE    ---       #390
      00FEB2E9H   LINE      ECODE    ---       #391
      00FEB303H   LINE      ECODE    ---       #392
      00FEB303H   LINE      ECODE    ---       #393
      00FEB303H   LINE      ECODE    ---       #395
      00FEB303H   LINE      ECODE    ---       #396
      00FEB303H   LINE      ECODE    ---       #398
      00FEB306H   LINE      ECODE    ---       #400
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB307H   BLOCK     ECODE    ---       LVL=0
      00010D9FH   SYMBOL    XDATA    WORD      x
      00010DA1H   SYMBOL    XDATA    WORD      y
      00010DA3H   SYMBOL    XDATA    ---       dat
      00FEB31DH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010DA7H   SYMBOL    XDATA    WORD      j
      ---         BLOCKEND  ---      ---       LVL=1
      00FEB307H   LINE      ECODE    ---       #411
      00FEB31DH   LINE      ECODE    ---       #412
      00FEB31DH   LINE      ECODE    ---       #414
      00FEB325H   LINE      ECODE    ---       #415
      00FEB327H   LINE      ECODE    ---       #417
      00FEB331H   LINE      ECODE    ---       #420
      00FEB331H   LINE      ECODE    ---       #421
      00FEB364H   LINE      ECODE    ---       #422
      00FEB373H   LINE      ECODE    ---       #423
      00FEB373H   LINE      ECODE    ---       #424
      00FEB373H   LINE      ECODE    ---       #426
      00FEB373H   LINE      ECODE    ---       #427
      00FEB373H   LINE      ECODE    ---       #428
      00FEB38DH   LINE      ECODE    ---       #429
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB38EH   BLOCK     ECODE    ---       LVL=0
      00010AE9H   SYMBOL    XDATA    WORD      x
      00010AEBH   SYMBOL    XDATA    WORD      y
      00010AEDH   SYMBOL    XDATA    LONG      dat
      00010AF1H   SYMBOL    XDATA    BYTE      num
      00FEB3A8H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010AF2H   SYMBOL    XDATA    LONG      dat_temp
      00010AF6H   SYMBOL    XDATA    LONG      offset
      00010AFAH   SYMBOL    XDATA    ---       data_buffer
      ---         BLOCKEND  ---      ---       LVL=1
      00FEB38EH   LINE      ECODE    ---       #441
      00FEB3A8H   LINE      ECODE    ---       #442
      00FEB3A8H   LINE      ECODE    ---       #445
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 93


      00FEB3BCH   LINE      ECODE    ---       #446
      00FEB3CAH   LINE      ECODE    ---       #449
      00FEB3DBH   LINE      ECODE    ---       #450
      00FEB3F5H   LINE      ECODE    ---       #452
      00FEB401H   LINE      ECODE    ---       #454
      00FEB403H   LINE      ECODE    ---       #456
      00FEB41CH   LINE      ECODE    ---       #457
      00FEB434H   LINE      ECODE    ---       #458
      00FEB453H   LINE      ECODE    ---       #459
      00FEB453H   LINE      ECODE    ---       #460
      00FEB469H   LINE      ECODE    ---       #461
      00FEB481H   LINE      ECODE    ---       #462
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB482H   BLOCK     ECODE    ---       LVL=0
      00010B06H   SYMBOL    XDATA    WORD      x
      00010B08H   SYMBOL    XDATA    WORD      y
      00010B0AH   SYMBOL    XDATA    DWORD     dat
      00010B0EH   SYMBOL    XDATA    BYTE      num
      00FEB49CH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010B0FH   SYMBOL    XDATA    DWORD     dat_temp
      00010B13H   SYMBOL    XDATA    LONG      offset
      00010B17H   SYMBOL    XDATA    ---       data_buffer
      ---         BLOCKEND  ---      ---       LVL=1
      00FEB482H   LINE      ECODE    ---       #474
      00FEB49CH   LINE      ECODE    ---       #475
      00FEB49CH   LINE      ECODE    ---       #476
      00FEB4B0H   LINE      ECODE    ---       #477
      00FEB4BEH   LINE      ECODE    ---       #479
      00FEB4CFH   LINE      ECODE    ---       #480
      00FEB4E3H   LINE      ECODE    ---       #482
      00FEB4EFH   LINE      ECODE    ---       #484
      00FEB4F1H   LINE      ECODE    ---       #486
      00FEB50AH   LINE      ECODE    ---       #487
      00FEB522H   LINE      ECODE    ---       #488
      00FEB541H   LINE      ECODE    ---       #489
      00FEB541H   LINE      ECODE    ---       #490
      00FEB557H   LINE      ECODE    ---       #491
      00FEB56FH   LINE      ECODE    ---       #492
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB570H   BLOCK     ECODE    ---       LVL=0
      000109EDH   SYMBOL    XDATA    WORD      x
      000109EFH   SYMBOL    XDATA    WORD      y
      000109F1H   SYMBOL    XDATA    DOUBLE    dat
      000109F9H   SYMBOL    XDATA    BYTE      num
      000109FAH   SYMBOL    XDATA    BYTE      pointnum
      00FEB586H   BLOCK     ECODE    NEAR LAB  LVL=1
      000109FBH   SYMBOL    XDATA    DOUBLE    dat_temp
      00010A03H   SYMBOL    XDATA    DOUBLE    offset
      00010A0BH   SYMBOL    XDATA    ---       data_buffer
      ---         BLOCKEND  ---      ---       LVL=1
      00FEB570H   LINE      ECODE    ---       #508
      00FEB586H   LINE      ECODE    ---       #509
      00FEB586H   LINE      ECODE    ---       #511
      00FEB5AAH   LINE      ECODE    ---       #512
      00FEB5C4H   LINE      ECODE    ---       #514
      00FEB5D5H   LINE      ECODE    ---       #515
      00FEB5F7H   LINE      ECODE    ---       #517
      00FEB606H   LINE      ECODE    ---       #519
      00FEB608H   LINE      ECODE    ---       #521
      00FEB635H   LINE      ECODE    ---       #522
      00FEB64DH   LINE      ECODE    ---       #523
      00FEB6C9H   LINE      ECODE    ---       #524
      00FEB6C9H   LINE      ECODE    ---       #525
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 94


      00FEB6FDH   LINE      ECODE    ---       #526
      00FEB715H   LINE      ECODE    ---       #527
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB716H   BLOCK     ECODE    ---       LVL=0
      00010A8FH   SYMBOL    XDATA    WORD      x
      00010A91H   SYMBOL    XDATA    WORD      y
      00010A93H   SYMBOL    XDATA    ---       wave
      00010A97H   SYMBOL    XDATA    WORD      width
      00010A99H   SYMBOL    XDATA    WORD      value_max
      00010A9BH   SYMBOL    XDATA    WORD      dis_width
      00010A9DH   SYMBOL    XDATA    WORD      dis_value_max
      00FEB72CH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010A9FH   SYMBOL    XDATA    DWORD     i
      00010AA3H   SYMBOL    XDATA    DWORD     j
      00010AA7H   SYMBOL    XDATA    DWORD     width_index
      00010AABH   SYMBOL    XDATA    DWORD     value_max_index
      ---         BLOCKEND  ---      ---       LVL=1
      00FEB716H   LINE      ECODE    ---       #545
      00FEB72CH   LINE      ECODE    ---       #546
      00FEB72CH   LINE      ECODE    ---       #548
      00FEB744H   LINE      ECODE    ---       #549
      00FEB75CH   LINE      ECODE    ---       #552
      00FEB75FH   LINE      ECODE    ---       #554
      00FEB796H   LINE      ECODE    ---       #555
      00FEB7A4H   LINE      ECODE    ---       #557
      00FEB7B2H   LINE      ECODE    ---       #559
      00FEB7BBH   LINE      ECODE    ---       #560
      00FEB7E8H   LINE      ECODE    ---       #561
      00FEB815H   LINE      ECODE    ---       #563
      00FEB818H   LINE      ECODE    ---       #566
      00FEB827H   LINE      ECODE    ---       #568
      00FEB84FH   LINE      ECODE    ---       #569
      00FEB88EH   LINE      ECODE    ---       #570
      00FEB8CEH   LINE      ECODE    ---       #571
      00FEB8FEH   LINE      ECODE    ---       #572
      ---         BLOCKEND  ---      ---       LVL=0

      00FEB8FFH   BLOCK     ECODE    ---       LVL=0
      00FEB8FFH   LINE      ECODE    ---       #638
      00FEB8FFH   LINE      ECODE    ---       #640
      00FEB902H   LINE      ECODE    ---       #641
      00FEB905H   LINE      ECODE    ---       #642
      00FEB908H   LINE      ECODE    ---       #645
      00FEB92FH   LINE      ECODE    ---       #650
      00FEB939H   LINE      ECODE    ---       #651
      00FEB949H   LINE      ECODE    ---       #654
      00FEB94CH   LINE      ECODE    ---       #655
      00FEB94FH   LINE      ECODE    ---       #656
      00FEB957H   LINE      ECODE    ---       #657
      00FEB95AH   LINE      ECODE    ---       #658
      00FEB962H   LINE      ECODE    ---       #661
      00FEB965H   LINE      ECODE    ---       #663
      00FEB96AH   LINE      ECODE    ---       #664
      00FEB972H   LINE      ECODE    ---       #666
      00FEB977H   LINE      ECODE    ---       #667
      00FEB983H   LINE      ECODE    ---       #669
      00FEB987H   LINE      ECODE    ---       #670
      00FEB989H   LINE      ECODE    ---       #671
      00FEB997H   LINE      ECODE    ---       #673
      00FEB99CH   LINE      ECODE    ---       #674
      00FEB99EH   LINE      ECODE    ---       #675
      00FEB9ACH   LINE      ECODE    ---       #677
      00FEB9B1H   LINE      ECODE    ---       #678
      00FEB9B3H   LINE      ECODE    ---       #681
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 95


      00FEB9B8H   LINE      ECODE    ---       #682
      00FEB9B8H   LINE      ECODE    ---       #684
      00FEB9BDH   LINE      ECODE    ---       #685
      00FEB9C2H   LINE      ECODE    ---       #687
      00FEB9C7H   LINE      ECODE    ---       #688
      00FEB9CCH   LINE      ECODE    ---       #689
      00FEB9D1H   LINE      ECODE    ---       #690
      00FEB9D5H   LINE      ECODE    ---       #691
      00FEB9DAH   LINE      ECODE    ---       #692
      00FEB9DFH   LINE      ECODE    ---       #694
      00FEB9E4H   LINE      ECODE    ---       #695
      00FEB9E9H   LINE      ECODE    ---       #697
      00FEB9EEH   LINE      ECODE    ---       #698
      00FEB9F3H   LINE      ECODE    ---       #700
      00FEB9F8H   LINE      ECODE    ---       #701
      00FEB9FDH   LINE      ECODE    ---       #703
      00FEBA02H   LINE      ECODE    ---       #704
      00FEBA07H   LINE      ECODE    ---       #706
      00FEBA0CH   LINE      ECODE    ---       #707
      00FEBA11H   LINE      ECODE    ---       #709
      00FEBA16H   LINE      ECODE    ---       #710
      00FEBA1BH   LINE      ECODE    ---       #712
      00FEBA20H   LINE      ECODE    ---       #713
      00FEBA25H   LINE      ECODE    ---       #715
      00FEBA2AH   LINE      ECODE    ---       #716
      00FEBA2FH   LINE      ECODE    ---       #717
      00FEBA34H   LINE      ECODE    ---       #719
      00FEBA39H   LINE      ECODE    ---       #720
      00FEBA3EH   LINE      ECODE    ---       #721
      00FEBA43H   LINE      ECODE    ---       #722
      00FEBA48H   LINE      ECODE    ---       #723
      00FEBA4DH   LINE      ECODE    ---       #724
      00FEBA52H   LINE      ECODE    ---       #725
      00FEBA57H   LINE      ECODE    ---       #726
      00FEBA5CH   LINE      ECODE    ---       #727
      00FEBA61H   LINE      ECODE    ---       #728
      00FEBA66H   LINE      ECODE    ---       #729
      00FEBA6BH   LINE      ECODE    ---       #730
      00FEBA70H   LINE      ECODE    ---       #731
      00FEBA75H   LINE      ECODE    ---       #732
      00FEBA7AH   LINE      ECODE    ---       #733
      00FEBA7FH   LINE      ECODE    ---       #735
      00FEBA84H   LINE      ECODE    ---       #736
      00FEBA89H   LINE      ECODE    ---       #737
      00FEBA8EH   LINE      ECODE    ---       #738
      00FEBA93H   LINE      ECODE    ---       #739
      00FEBA98H   LINE      ECODE    ---       #740
      00FEBA9DH   LINE      ECODE    ---       #741
      00FEBAA2H   LINE      ECODE    ---       #742
      00FEBAA7H   LINE      ECODE    ---       #743
      00FEBAACH   LINE      ECODE    ---       #744
      00FEBAB1H   LINE      ECODE    ---       #745
      00FEBAB6H   LINE      ECODE    ---       #746
      00FEBABBH   LINE      ECODE    ---       #747
      00FEBAC0H   LINE      ECODE    ---       #748
      00FEBAC5H   LINE      ECODE    ---       #749
      00FEBACAH   LINE      ECODE    ---       #751
      00FEBACFH   LINE      ECODE    ---       #753
      00FEBAD4H   LINE      ECODE    ---       #755
      00FEBAD7H   LINE      ECODE    ---       #758
      00FEBADBH   LINE      ECODE    ---       #759
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       isr
      00FF37D9H   PUBLIC    ECODE    ---       UART1_Isr?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 96


      00FF382AH   PUBLIC    ECODE    ---       UART2_Isr?
      00FF3872H   PUBLIC    ECODE    ---       UART3_Isr?
      00FF3898H   PUBLIC    ECODE    ---       UART4_Isr?
      00FF38F6H   PUBLIC    ECODE    ---       INT0_Isr?
      00FF38FEH   PUBLIC    ECODE    ---       INT1_Isr?
      00FF38FFH   PUBLIC    ECODE    ---       INT2_Isr?
      00FF3907H   PUBLIC    ECODE    ---       INT3_Isr?
      00FF390FH   PUBLIC    ECODE    ---       INT4_Isr?
      00FF3917H   PUBLIC    ECODE    ---       TM0_Isr?
      00FF395EH   PUBLIC    ECODE    ---       TM1_Isr?
      00FF39A5H   PUBLIC    ECODE    ---       TM2_Isr?
      00FF39ADH   PUBLIC    ECODE    ---       TM3_Isr?
      00FF39F7H   PUBLIC    ECODE    ---       TM4_Isr?
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000FEH   SFRSYM    DATA     BYTE      S4BUF
      0000009BH   SFRSYM    DATA     BYTE      S2BUF
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000C7H   SFRSYM    DATA     BYTE      IAP_CONTR
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000C8H.2 SFRSYM    DATA     BIT       P52
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF37D9H   BLOCK     ECODE    ---       LVL=0
      00FF37E1H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010F44H   SYMBOL    XDATA    BYTE      res
      00010F45H   SYMBOL    XDATA    BYTE      dwon_count
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 97


      ---         BLOCKEND  ---      ---       LVL=1
      00FF37D9H   LINE      ECODE    ---       #23
      00FF37E1H   LINE      ECODE    ---       #24
      00FF37E1H   LINE      ECODE    ---       #27
      00FF37E6H   LINE      ECODE    ---       #29
      00FF37E9H   LINE      ECODE    ---       #30
      00FF37EEH   LINE      ECODE    ---       #31
      00FF37EEH   LINE      ECODE    ---       #32
      00FF37F3H   LINE      ECODE    ---       #34
      00FF37F6H   LINE      ECODE    ---       #35
      00FF37FCH   LINE      ECODE    ---       #37
      00FF3805H   LINE      ECODE    ---       #39
      00FF3816H   LINE      ECODE    ---       #40
      00FF381AH   LINE      ECODE    ---       #41
      00FF381CH   LINE      ECODE    ---       #44
      00FF3821H   LINE      ECODE    ---       #45
      00FF3821H   LINE      ECODE    ---       #46
      ---         BLOCKEND  ---      ---       LVL=0

      00FF382AH   BLOCK     ECODE    ---       LVL=0
      00FF382AH   LINE      ECODE    ---       #50
      00FF3840H   LINE      ECODE    ---       #52
      00FF3845H   LINE      ECODE    ---       #54
      00FF3848H   LINE      ECODE    ---       #55
      00FF384DH   LINE      ECODE    ---       #56
      00FF384DH   LINE      ECODE    ---       #57
      00FF3852H   LINE      ECODE    ---       #59
      00FF3855H   LINE      ECODE    ---       #61
      00FF385BH   LINE      ECODE    ---       #62
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3872H   BLOCK     ECODE    ---       LVL=0
      00FF3872H   LINE      ECODE    ---       #67
      00FF387AH   LINE      ECODE    ---       #69
      00FF387FH   LINE      ECODE    ---       #71
      00FF3882H   LINE      ECODE    ---       #72
      00FF3887H   LINE      ECODE    ---       #73
      00FF3887H   LINE      ECODE    ---       #74
      00FF388CH   LINE      ECODE    ---       #76
      00FF388FH   LINE      ECODE    ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3898H   BLOCK     ECODE    ---       LVL=0
      00FF3898H   LINE      ECODE    ---       #84
      00FF38AEH   LINE      ECODE    ---       #86
      00FF38B3H   LINE      ECODE    ---       #88
      00FF38B6H   LINE      ECODE    ---       #89
      00FF38BBH   LINE      ECODE    ---       #90
      00FF38BBH   LINE      ECODE    ---       #91
      00FF38C0H   LINE      ECODE    ---       #93
      00FF38C3H   LINE      ECODE    ---       #96
      00FF38D1H   LINE      ECODE    ---       #100
      00FF38DFH   LINE      ECODE    ---       #101
      00FF38DFH   LINE      ECODE    ---       #103
      ---         BLOCKEND  ---      ---       LVL=0

      00FF38F6H   BLOCK     ECODE    ---       LVL=0
      00FF38F6H   LINE      ECODE    ---       #106
      00FF38F8H   LINE      ECODE    ---       #108
      00FF38FBH   LINE      ECODE    ---       #109
      ---         BLOCKEND  ---      ---       LVL=0

      00FF38FEH   BLOCK     ECODE    ---       LVL=0
      00FF38FEH   LINE      ECODE    ---       #110
      00FF38FEH   LINE      ECODE    ---       #113
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 98


      ---         BLOCKEND  ---      ---       LVL=0

      00FF38FFH   BLOCK     ECODE    ---       LVL=0
      00FF38FFH   LINE      ECODE    ---       #114
      00FF3901H   LINE      ECODE    ---       #116
      00FF3904H   LINE      ECODE    ---       #117
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3907H   BLOCK     ECODE    ---       LVL=0
      00FF3907H   LINE      ECODE    ---       #118
      00FF3909H   LINE      ECODE    ---       #120
      00FF390CH   LINE      ECODE    ---       #121
      ---         BLOCKEND  ---      ---       LVL=0

      00FF390FH   BLOCK     ECODE    ---       LVL=0
      00FF390FH   LINE      ECODE    ---       #123
      00FF3911H   LINE      ECODE    ---       #125
      00FF3914H   LINE      ECODE    ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3917H   BLOCK     ECODE    ---       LVL=0
      00FF3917H   LINE      ECODE    ---       #128
      00FF392DH   LINE      ECODE    ---       #130
      00FF393BH   LINE      ECODE    ---       #132
      00FF3947H   LINE      ECODE    ---       #133
      ---         BLOCKEND  ---      ---       LVL=0

      00FF395EH   BLOCK     ECODE    ---       LVL=0
      00FF395EH   LINE      ECODE    ---       #135
      00FF3974H   LINE      ECODE    ---       #137
      00FF3982H   LINE      ECODE    ---       #139
      00FF398EH   LINE      ECODE    ---       #140
      ---         BLOCKEND  ---      ---       LVL=0

      00FF39A5H   BLOCK     ECODE    ---       LVL=0
      00FF39A5H   LINE      ECODE    ---       #142
      00FF39A7H   LINE      ECODE    ---       #144
      00FF39AAH   LINE      ECODE    ---       #149
      ---         BLOCKEND  ---      ---       LVL=0

      00FF39ADH   BLOCK     ECODE    ---       LVL=0
      00FF39ADH   LINE      ECODE    ---       #150
      00FF39C3H   LINE      ECODE    ---       #152
      00FF39C6H   LINE      ECODE    ---       #153
      00FF39D4H   LINE      ECODE    ---       #155
      00FF39E0H   LINE      ECODE    ---       #156
      ---         BLOCKEND  ---      ---       LVL=0

      00FF39F7H   BLOCK     ECODE    ---       LVL=0
      00FF39F7H   LINE      ECODE    ---       #159
      00FF3A0DH   LINE      ECODE    ---       #161
      00FF3A10H   LINE      ECODE    ---       #163
      00FF3A1EH   LINE      ECODE    ---       #165
      00FF3A2AH   LINE      ECODE    ---       #166
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       main
      00FEA314H   PUBLIC    ECODE    ---       uart_rx_interrupt_handler?
      00FE9F4AH   PUBLIC    ECODE    ---       main?
      00FE9F78H   PUBLIC    ECODE    ---       _m?
      00FEAA75H   PUBLIC    ECODE    ---       tim1_pit_hanlder?
      00FEA3B6H   PUBLIC    ECODE    ---       tim3_pit_hanlder?
      00FEAD7CH   PUBLIC    ECODE    ---       tim4_pit_hanlder?
      00010538H   PUBLIC    XDATA    BYTE      debug_data
      00010539H   PUBLIC    XDATA    DWORD     debug_time
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 99


      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      0001053DH   SYMBOL    XDATA    ---       uart_fifo_buffer
      000105BDH   SYMBOL    XDATA    ---       uart_fifo

      00FE9F4AH   BLOCK     ECODE    ---       LVL=0
      00FE9F4AH   LINE      ECODE    ---       #55
      ---         BLOCKEND  ---      ---       LVL=0

      00FE9F78H   BLOCK     ECODE    ---       LVL=0
      00FE9F78H   LINE      ECODE    ---       #55
      00FE9F78H   LINE      ECODE    ---       #58
      00FE9F7CH   LINE      ECODE    ---       #61
      00FE9FA2H   LINE      ECODE    ---       #62
      00FE9FC8H   LINE      ECODE    ---       #63
      00FE9FCEH   LINE      ECODE    ---       #65
      00FE9FD2H   LINE      ECODE    ---       #67
      00FE9FD6H   LINE      ECODE    ---       #68
      00FE9FDAH   LINE      ECODE    ---       #69
      00FE9FDEH   LINE      ECODE    ---       #70
      00FE9FE2H   LINE      ECODE    ---       #71
      00FE9FE6H   LINE      ECODE    ---       #76
      00FE9FF0H   LINE      ECODE    ---       #77
      00FE9FFAH   LINE      ECODE    ---       #78
      00FEA004H   LINE      ECODE    ---       #81
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 100


      00FEA016H   LINE      ECODE    ---       #83
      00FEA01AH   LINE      ECODE    ---       #85
      00FEA026H   LINE      ECODE    ---       #86
      00FEA032H   LINE      ECODE    ---       #87
      00FEA044H   LINE      ECODE    ---       #88
      00FEA056H   LINE      ECODE    ---       #89
      00FEA068H   LINE      ECODE    ---       #90
      00FEA06CH   LINE      ECODE    ---       #92
      00FEA06CH   LINE      ECODE    ---       #105
      00FEA070H   LINE      ECODE    ---       #107
      00FEA074H   LINE      ECODE    ---       #110
      00FEA08CH   LINE      ECODE    ---       #111
      00FEA0A4H   LINE      ECODE    ---       #113
      00FEA0BCH   LINE      ECODE    ---       #115
      00FEA0E8H   LINE      ECODE    ---       #116
      00FEA118H   LINE      ECODE    ---       #117
      00FEA144H   LINE      ECODE    ---       #118
      00FEA170H   LINE      ECODE    ---       #120
      00FEA19CH   LINE      ECODE    ---       #121
      00FEA1C8H   LINE      ECODE    ---       #123
      00FEA1F4H   LINE      ECODE    ---       #125
      00FEA224H   LINE      ECODE    ---       #126
      00FEA250H   LINE      ECODE    ---       #127
      00FEA27CH   LINE      ECODE    ---       #128
      00FEA2A8H   LINE      ECODE    ---       #129
      00FEA2D4H   LINE      ECODE    ---       #130
      00FEA300H   LINE      ECODE    ---       #131
      00FEA30CH   LINE      ECODE    ---       #135
      00FEA310H   LINE      ECODE    ---       #194
      00FEA313H   LINE      ECODE    ---       #195
      ---         BLOCKEND  ---      ---       LVL=0

      00FEA314H   BLOCK     ECODE    ---       LVL=0
      00010F46H   SYMBOL    XDATA    BYTE      dat
      00FEA314H   LINE      ECODE    ---       #197
      00FEA318H   LINE      ECODE    ---       #199
      00FEA33AH   LINE      ECODE    ---       #200
      00FEA343H   LINE      ECODE    ---       #202
      00FEA357H   LINE      ECODE    ---       #203
      00FEA35BH   LINE      ECODE    ---       #204
      00FEA360H   LINE      ECODE    ---       #205
      00FEA36CH   LINE      ECODE    ---       #206
      00FEA370H   LINE      ECODE    ---       #207
      00FEA372H   LINE      ECODE    ---       #208
      00FEA37BH   LINE      ECODE    ---       #209
      00FEA389H   LINE      ECODE    ---       #210
      00FEA392H   LINE      ECODE    ---       #211
      00FEA3A0H   LINE      ECODE    ---       #212
      00FEA3A9H   LINE      ECODE    ---       #213
      00FEA3B5H   LINE      ECODE    ---       #214
      ---         BLOCKEND  ---      ---       LVL=0

      00FEA3B6H   BLOCK     ECODE    ---       LVL=0
      00FEA412H   BLOCK     ECODE    NEAR LAB  LVL=1
      000105D4H   SYMBOL    XDATA    LONG      ctimer_speed_temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FEA63DH   BLOCK     ECODE    NEAR LAB  LVL=1
      000105D8H   SYMBOL    XDATA    BYTE      pre_turn_fun_lock
      ---         BLOCKEND  ---      ---       LVL=1
      00FEA8E6H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010EEBH   SYMBOL    XDATA    FLOAT     angle_diff
      ---         BLOCKEND  ---      ---       LVL=1
      00FEA3B6H   LINE      ECODE    ---       #216
      00FEA3B6H   LINE      ECODE    ---       #219
      00FEA3CDH   LINE      ECODE    ---       #220
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 101


      00FEA3DDH   LINE      ECODE    ---       #221
      00FEA3E9H   LINE      ECODE    ---       #223
      00FEA3F2H   LINE      ECODE    ---       #225
      00FEA3F7H   LINE      ECODE    ---       #227
      00FEA3FBH   LINE      ECODE    ---       #229
      00FEA3FBH   LINE      ECODE    ---       #231
      00FEA406H   LINE      ECODE    ---       #232
      00FEA412H   LINE      ECODE    ---       #233
      00FEA412H   LINE      ECODE    ---       #235
      00FEA424H   LINE      ECODE    ---       #243
      00FEA462H   LINE      ECODE    ---       #245
      00FEA48EH   LINE      ECODE    ---       #246
      00FEA4B2H   LINE      ECODE    ---       #247
      00FEA4B8H   LINE      ECODE    ---       #248
      00FEA4BDH   LINE      ECODE    ---       #250
      00FEA4C2H   LINE      ECODE    ---       #251
      00FEA4C2H   LINE      ECODE    ---       #252
      00FEA4DAH   LINE      ECODE    ---       #254
      00FEA500H   LINE      ECODE    ---       #256
      00FEA5BAH   LINE      ECODE    ---       #260
      00FEA5E2H   LINE      ECODE    ---       #262
      00FEA5EDH   LINE      ECODE    ---       #263
      00FEA5F3H   LINE      ECODE    ---       #264
      00FEA5F3H   LINE      ECODE    ---       #265
      00FEA5FCH   LINE      ECODE    ---       #266
      00FEA601H   LINE      ECODE    ---       #267
      00FEA611H   LINE      ECODE    ---       #268
      00FEA614H   LINE      ECODE    ---       #269
      00FEA61CH   LINE      ECODE    ---       #272
      00FEA63DH   LINE      ECODE    ---       #275
      00FEA63DH   LINE      ECODE    ---       #277
      00FEA646H   LINE      ECODE    ---       #279
      00FEA683H   LINE      ECODE    ---       #281
      00FEA689H   LINE      ECODE    ---       #282
      00FEA68FH   LINE      ECODE    ---       #283
      00FEA68FH   LINE      ECODE    ---       #289
      00FEA68FH   LINE      ECODE    ---       #295
      00FEA6D3H   LINE      ECODE    ---       #297
      00FEA6D3H   LINE      ECODE    ---       #298
      00FEA6DFH   LINE      ECODE    ---       #299
      00FEA6E5H   LINE      ECODE    ---       #300
      00FEA6EBH   LINE      ECODE    ---       #301
      00FEA6EEH   LINE      ECODE    ---       #302
      00FEA6EEH   LINE      ECODE    ---       #303
      00FEA6FAH   LINE      ECODE    ---       #304
      00FEA700H   LINE      ECODE    ---       #305
      00FEA706H   LINE      ECODE    ---       #306
      00FEA709H   LINE      ECODE    ---       #307
      00FEA709H   LINE      ECODE    ---       #308
      00FEA715H   LINE      ECODE    ---       #309
      00FEA71BH   LINE      ECODE    ---       #310
      00FEA721H   LINE      ECODE    ---       #311
      00FEA723H   LINE      ECODE    ---       #312
      00FEA723H   LINE      ECODE    ---       #313
      00FEA72CH   LINE      ECODE    ---       #314
      00FEA732H   LINE      ECODE    ---       #315
      00FEA738H   LINE      ECODE    ---       #316
      00FEA73AH   LINE      ECODE    ---       #317
      00FEA73AH   LINE      ECODE    ---       #318
      00FEA743H   LINE      ECODE    ---       #319
      00FEA749H   LINE      ECODE    ---       #320
      00FEA74FH   LINE      ECODE    ---       #321
      00FEA751H   LINE      ECODE    ---       #322
      00FEA751H   LINE      ECODE    ---       #323
      00FEA75AH   LINE      ECODE    ---       #324
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 102


      00FEA760H   LINE      ECODE    ---       #325
      00FEA766H   LINE      ECODE    ---       #326
      00FEA768H   LINE      ECODE    ---       #327
      00FEA768H   LINE      ECODE    ---       #328
      00FEA771H   LINE      ECODE    ---       #329
      00FEA777H   LINE      ECODE    ---       #330
      00FEA77DH   LINE      ECODE    ---       #331
      00FEA77FH   LINE      ECODE    ---       #332
      00FEA77FH   LINE      ECODE    ---       #334
      00FEA788H   LINE      ECODE    ---       #336
      00FEA78EH   LINE      ECODE    ---       #337
      00FEA794H   LINE      ECODE    ---       #338
      00FEA794H   LINE      ECODE    ---       #340
      00FEA794H   LINE      ECODE    ---       #341
      00FEA794H   LINE      ECODE    ---       #342
      00FEA794H   LINE      ECODE    ---       #343
      00FEA794H   LINE      ECODE    ---       #345
      00FEA7B8H   LINE      ECODE    ---       #347
      00FEA884H   LINE      ECODE    ---       #353
      00FEA89AH   LINE      ECODE    ---       #355
      00FEA8B5H   LINE      ECODE    ---       #356
      00FEA8B5H   LINE      ECODE    ---       #357
      00FEA8CBH   LINE      ECODE    ---       #359
      00FEA8E6H   LINE      ECODE    ---       #360
      00FEA8E6H   LINE      ECODE    ---       #365
      00FEA8E6H   LINE      ECODE    ---       #366
      00FEA8F2H   LINE      ECODE    ---       #369
      00FEA8FBH   LINE      ECODE    ---       #371
      00FEA91BH   LINE      ECODE    ---       #372
      00FEA920H   LINE      ECODE    ---       #373
      00FEA920H   LINE      ECODE    ---       #374
      00FEA956H   LINE      ECODE    ---       #375
      00FEA987H   LINE      ECODE    ---       #376
      00FEA9B8H   LINE      ECODE    ---       #382
      00FEAA10H   LINE      ECODE    ---       #387
      00FEAA16H   LINE      ECODE    ---       #388
      00FEAA21H   LINE      ECODE    ---       #390
      00FEAA2CH   LINE      ECODE    ---       #391
      00FEAA34H   LINE      ECODE    ---       #392
      00FEAA34H   LINE      ECODE    ---       #409
      00FEAA40H   LINE      ECODE    ---       #411
      00FEAA45H   LINE      ECODE    ---       #412
      00FEAA49H   LINE      ECODE    ---       #413
      00FEAA5DH   LINE      ECODE    ---       #414
      00FEAA61H   LINE      ECODE    ---       #415
      00FEAA63H   LINE      ECODE    ---       #416
      00FEAA63H   LINE      ECODE    ---       #419
      00FEAA6CH   LINE      ECODE    ---       #421
      00FEAA74H   LINE      ECODE    ---       #422
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAA75H   BLOCK     ECODE    ---       LVL=0
      00FEAA77H   BLOCK     ECODE    NEAR LAB  LVL=1
      000105D9H   SYMBOL    XDATA    FLOAT     filtered_gyro_z
      000105DDH   SYMBOL    XDATA    FLOAT     filtered_yaw
      ---         BLOCKEND  ---      ---       LVL=1
      00FEAA75H   LINE      ECODE    ---       #459
      00FEAA77H   LINE      ECODE    ---       #460
      00FEAA77H   LINE      ECODE    ---       #465
      00FEAA7BH   LINE      ECODE    ---       #466
      00FEAA7FH   LINE      ECODE    ---       #469
      00FEAAA1H   LINE      ECODE    ---       #470
      00FEAAC3H   LINE      ECODE    ---       #471
      00FEAAE5H   LINE      ECODE    ---       #474
      00FEAB0BH   LINE      ECODE    ---       #475
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 103


      00FEAB37H   LINE      ECODE    ---       #477
      00FEAB43H   LINE      ECODE    ---       #478
      00FEAB43H   LINE      ECODE    ---       #479
      00FEAB6BH   LINE      ECODE    ---       #482
      00FEAB9FH   LINE      ECODE    ---       #483
      00FEABB3H   LINE      ECODE    ---       #485
      00FEABFBH   LINE      ECODE    ---       #486
      00FEAC43H   LINE      ECODE    ---       #487
      00FEAC8BH   LINE      ECODE    ---       #490
      00FEAD05H   LINE      ECODE    ---       #493
      00FEAD11H   LINE      ECODE    ---       #496
      00FEAD45H   LINE      ECODE    ---       #497
      00FEAD65H   LINE      ECODE    ---       #498
      00FEAD79H   LINE      ECODE    ---       #499
      ---         BLOCKEND  ---      ---       LVL=0

      00FEAD7CH   BLOCK     ECODE    ---       LVL=0
      00FEAD7CH   LINE      ECODE    ---       #504
      00FEAD7CH   LINE      ECODE    ---       #506
      00FEAD85H   LINE      ECODE    ---       #509
      00FEAD89H   LINE      ECODE    ---       #510
      00FEAD8DH   LINE      ECODE    ---       #512
      00FEAD91H   LINE      ECODE    ---       #513
      00FEAD9BH   LINE      ECODE    ---       #518
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       beep
      00FEFFEBH   PUBLIC    ECODE    ---       remind_once?
      00FEFFE4H   PUBLIC    ECODE    ---       remind_off?
      00FEFFCDH   PUBLIC    ECODE    ---       beep_init?
      00FEFFDDH   PUBLIC    ECODE    ---       remind_on?
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000E8H.7 SFRSYM    DATA     BIT       P67
      000000C8H.2 SFRSYM    DATA     BIT       P52
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 104


      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FEFFCDH   BLOCK     ECODE    ---       LVL=0
      00FEFFCDH   LINE      ECODE    ---       #5
      00FEFFCDH   LINE      ECODE    ---       #7
      00FEFFD9H   LINE      ECODE    ---       #8
      00FEFFDCH   LINE      ECODE    ---       #9
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFFDDH   BLOCK     ECODE    ---       LVL=0
      00FEFFDDH   LINE      ECODE    ---       #10
      00FEFFDDH   LINE      ECODE    ---       #12
      00FEFFE0H   LINE      ECODE    ---       #13
      00FEFFE3H   LINE      ECODE    ---       #14
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFFE4H   BLOCK     ECODE    ---       LVL=0
      00FEFFE4H   LINE      ECODE    ---       #15
      00FEFFE4H   LINE      ECODE    ---       #17
      00FEFFE7H   LINE      ECODE    ---       #18
      00FEFFEAH   LINE      ECODE    ---       #19
      ---         BLOCKEND  ---      ---       LVL=0

      00FEFFEBH   BLOCK     ECODE    ---       LVL=0
      00FEFFEBH   LINE      ECODE    ---       #20
      00FEFFEBH   LINE      ECODE    ---       #22
      00FEFFEEH   LINE      ECODE    ---       #23
      00FEFFF1H   LINE      ECODE    ---       #24
      00FEFFF9H   LINE      ECODE    ---       #25
      00FEFFFCH   LINE      ECODE    ---       #26
      00FEFFFFH   LINE      ECODE    ---       #27
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       config
      000109BAH   PUBLIC    XDATA    BYTE      anomaly_change_point_num
      000109BBH   PUBLIC    XDATA    DOUBLE    anomaly_detection_angle
      000109C3H   PUBLIC    XDATA    BYTE      element_processing_state
      000109C4H   PUBLIC    XDATA    BYTE      anomaly_detection_flag
      000109C5H   PUBLIC    XDATA    DOUBLE    start_angle
      000109CDH   PUBLIC    XDATA    BYTE      start_flag
      000109CEH   PUBLIC    XDATA    BYTE      g_ctimer_read_finish_flag
      000109CFH   PUBLIC    XDATA    DWORD     heartbeat_time
      000109D3H   PUBLIC    XDATA    BYTE      g_ccd_process_flag
      000109D4H   PUBLIC    XDATA    LONG      encoder
      000109D8H   PUBLIC    XDATA    CHAR      take_point_num
      000109D9H   PUBLIC    XDATA    ---       tim0_irq_handler
      000109DDH   PUBLIC    XDATA    ---       tim1_irq_handler
      000109E1H   PUBLIC    XDATA    ---       tim2_irq_handler
      000109E5H   PUBLIC    XDATA    ---       tim3_irq_handler
      000109E9H   PUBLIC    XDATA    ---       tim4_irq_handler
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 105


      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       key
      00FF4323H   PUBLIC    ECODE    ---       key_scan?
      00010EA3H   PUBLIC    XDATA    INT       if_take_point_key
      00010EA5H   PUBLIC    XDATA    INT       key_num
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000F8H.3 SFRSYM    DATA     BIT       P73
      000000F8H.2 SFRSYM    DATA     BIT       P72
      000000F8H.1 SFRSYM    DATA     BIT       P71
      000000F8H.0 SFRSYM    DATA     BIT       P70
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 106


      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF4323H   BLOCK     ECODE    ---       LVL=0
      00010F2FH   SYMBOL    XDATA    INT       mode
      00FF4329H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010EA7H   SYMBOL    XDATA    BYTE      key_up
      ---         BLOCKEND  ---      ---       LVL=1
      00FF4323H   LINE      ECODE    ---       #8
      00FF4329H   LINE      ECODE    ---       #9
      00FF4329H   LINE      ECODE    ---       #12
      00FF433BH   LINE      ECODE    ---       #13
      00FF436BH   LINE      ECODE    ---       #22
      00FF4370H   LINE      ECODE    ---       #23
      00FF4378H   LINE      ECODE    ---       #25
      00FF438FH   LINE      ECODE    ---       #26
      00FF43A6H   LINE      ECODE    ---       #27
      00FF43BDH   LINE      ECODE    ---       #28
      00FF43D4H   LINE      ECODE    ---       #30
      00FF43FEH   LINE      ECODE    ---       #31
      00FF4400H   LINE      ECODE    ---       #32
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       lora
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 107


      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      ---         MODULE    ---      ---       motor
      00FF3753H   PUBLIC    ECODE    ---       speed_set?
      00FF3538H   PUBLIC    ECODE    ---       motor_init?
      00FF35B0H   PUBLIC    ECODE    ---       botoom_right_motor_set_speed?
      00FF3599H   PUBLIC    ECODE    ---       botoom_left_motor_set_speed?
      00FF372CH   PUBLIC    ECODE    ---       side_right_motor_set_speed?
      00FF37B0H   PUBLIC    ECODE    ---       speed_set_zero?
      00FF3666H   PUBLIC    ECODE    ---       tail_right_motor_set_speed?
      00FF3705H   PUBLIC    ECODE    ---       side_left_motor_set_speed?
      00FF35C7H   PUBLIC    ECODE    ---       tail_left_motor_set_speed?
      00010DA9H   PUBLIC    XDATA    BYTE      ?speed_set??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 108


      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF3538H   BLOCK     ECODE    ---       LVL=0
      00FF3538H   LINE      ECODE    ---       #7
      00FF3538H   LINE      ECODE    ---       #9
      00FF3550H   LINE      ECODE    ---       #10
      00FF3568H   LINE      ECODE    ---       #11
      00FF3580H   LINE      ECODE    ---       #12
      00FF3598H   LINE      ECODE    ---       #15
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3599H   BLOCK     ECODE    ---       LVL=0
      00010F31H   SYMBOL    XDATA    WORD      botoom_left_motor_speed
      00FF3599H   LINE      ECODE    ---       #20
      00FF359FH   LINE      ECODE    ---       #22
      00FF35AFH   LINE      ECODE    ---       #23
      ---         BLOCKEND  ---      ---       LVL=0

      00FF35B0H   BLOCK     ECODE    ---       LVL=0
      00010F33H   SYMBOL    XDATA    WORD      botoom_right_motor_speed
      00FF35B0H   LINE      ECODE    ---       #25
      00FF35B6H   LINE      ECODE    ---       #27
      00FF35C6H   LINE      ECODE    ---       #28
      ---         BLOCKEND  ---      ---       LVL=0

      00FF35C7H   BLOCK     ECODE    ---       LVL=0
      00010EEFH   SYMBOL    XDATA    FLOAT     tail_left_motor_speed
      00FF35C7H   LINE      ECODE    ---       #29
      00FF35D1H   LINE      ECODE    ---       #31
      00FF35E7H   LINE      ECODE    ---       #32
      00FF35F7H   LINE      ECODE    ---       #35
      00FF361FH   LINE      ECODE    ---       #36
      00FF362FH   LINE      ECODE    ---       #37
      00FF363DH   LINE      ECODE    ---       #38
      00FF3649H   LINE      ECODE    ---       #39
      00FF3665H   LINE      ECODE    ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3666H   BLOCK     ECODE    ---       LVL=0
      00010EF3H   SYMBOL    XDATA    FLOAT     tail_right_motor_speed
      00FF3666H   LINE      ECODE    ---       #41
      00FF3670H   LINE      ECODE    ---       #43
      00FF3686H   LINE      ECODE    ---       #44
      00FF3696H   LINE      ECODE    ---       #47
      00FF36BEH   LINE      ECODE    ---       #48
      00FF36CEH   LINE      ECODE    ---       #49
      00FF36DCH   LINE      ECODE    ---       #50
      00FF36E8H   LINE      ECODE    ---       #51
      00FF3704H   LINE      ECODE    ---       #52
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3705H   BLOCK     ECODE    ---       LVL=0
      00010EF7H   SYMBOL    XDATA    FLOAT     side_left_motor_speed
      00FF3705H   LINE      ECODE    ---       #53
      00FF370FH   LINE      ECODE    ---       #55
      00FF372BH   LINE      ECODE    ---       #56
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 109


      ---         BLOCKEND  ---      ---       LVL=0

      00FF372CH   BLOCK     ECODE    ---       LVL=0
      00010EFBH   SYMBOL    XDATA    FLOAT     side_right_motor_speed
      00FF372CH   LINE      ECODE    ---       #57
      00FF3736H   LINE      ECODE    ---       #59
      00FF3752H   LINE      ECODE    ---       #60
      ---         BLOCKEND  ---      ---       LVL=0

      00FF3753H   BLOCK     ECODE    ---       LVL=0
      00010DA9H   SYMBOL    XDATA    WORD      bottom_speed
      00010DABH   SYMBOL    XDATA    FLOAT     speed_pid_num
      00010DAFH   SYMBOL    XDATA    FLOAT     rate_pid_num
      00FF3753H   LINE      ECODE    ---       #62
      00FF3763H   LINE      ECODE    ---       #64
      00FF376DH   LINE      ECODE    ---       #65
      00FF3777H   LINE      ECODE    ---       #66
      00FF3793H   LINE      ECODE    ---       #67
      00FF37AFH   LINE      ECODE    ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      00FF37B0H   BLOCK     ECODE    ---       LVL=0
      00FF37B0H   LINE      ECODE    ---       #72
      00FF37B0H   LINE      ECODE    ---       #74
      00FF37BAH   LINE      ECODE    ---       #75
      00FF37C4H   LINE      ECODE    ---       #76
      00FF37CEH   LINE      ECODE    ---       #77
      00FF37D8H   LINE      ECODE    ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       my_atan2
      00FEFE67H   PUBLIC    ECODE    ---       my_atan2?
      00010BA0H   PUBLIC    XDATA    BYTE      ?my_atan2??BYTE

      00FEFE67H   BLOCK     ECODE    ---       LVL=0
      00010BA0H   SYMBOL    XDATA    DOUBLE    y
      00010BA8H   SYMBOL    XDATA    DOUBLE    x
      00FEFEF8H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010BB0H   SYMBOL    XDATA    DOUBLE    theta
      ---         BLOCKEND  ---      ---       LVL=1
      00FEFE67H   LINE      ECODE    ---       #4
      00FEFE79H   LINE      ECODE    ---       #6
      00FEFE97H   LINE      ECODE    ---       #7
      00FEFEB3H   LINE      ECODE    ---       #8
      00FEFEC4H   LINE      ECODE    ---       #9
      00FEFEE0H   LINE      ECODE    ---       #10
      00FEFEF1H   LINE      ECODE    ---       #11
      00FEFEF1H   LINE      ECODE    ---       #12
      00FEFEF8H   LINE      ECODE    ---       #13
      00FEFEF8H   LINE      ECODE    ---       #14
      00FEFEF8H   LINE      ECODE    ---       #15
      00FEFEF8H   LINE      ECODE    ---       #17
      00FEFF36H   LINE      ECODE    ---       #20
      00FEFF52H   LINE      ECODE    ---       #21
      00FEFF66H   LINE      ECODE    ---       #23
      00FEFF66H   LINE      ECODE    ---       #24
      00FEFF82H   LINE      ECODE    ---       #25
      00FEFFA8H   LINE      ECODE    ---       #26
      00FEFFA8H   LINE      ECODE    ---       #27
      00FEFFCCH   LINE      ECODE    ---       #28
      00FEFFCCH   LINE      ECODE    ---       #29
      00FEFFCCH   LINE      ECODE    ---       #30
      00FEFFCCH   LINE      ECODE    ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 110


      ---         MODULE    ---      ---       pid
      00FEF0DFH   PUBLIC    ECODE    ---       pid_angle?
      00FEF5B0H   PUBLIC    ECODE    ---       pid_speed?
      00FEF7E8H   PUBLIC    ECODE    ---       pid_ccd?
      00FEF359H   PUBLIC    ECODE    ---       pid_rate?
      000105E1H   PUBLIC    XDATA    FLOAT     target_speed
      000105E5H   PUBLIC    XDATA    FLOAT     target_rate
      000105E9H   PUBLIC    XDATA    FLOAT     rate_integral_sum
      000105EDH   PUBLIC    XDATA    FLOAT     kd_angle
      000105F1H   PUBLIC    XDATA    FLOAT     ki_angle
      000105F5H   PUBLIC    XDATA    FLOAT     kd_speed
      000105F9H   PUBLIC    XDATA    FLOAT     kp_angle
      000105FDH   PUBLIC    XDATA    FLOAT     ki_speed
      00010601H   PUBLIC    XDATA    FLOAT     speed_out
      00010605H   PUBLIC    XDATA    FLOAT     angle_error_sum
      00010609H   PUBLIC    XDATA    FLOAT     kp_speed
      0001060DH   PUBLIC    XDATA    FLOAT     speed_side_out
      00010611H   PUBLIC    XDATA    FLOAT     speed_error_sum
      00010615H   PUBLIC    XDATA    FLOAT     speed_ccd_out
      00010619H   PUBLIC    XDATA    FLOAT     kd_ccd
      0001061DH   PUBLIC    XDATA    FLOAT     kd_rate
      00010621H   PUBLIC    XDATA    FLOAT     ki_ccd
      00010626H   PUBLIC    XDATA    BYTE      time1
      00010627H   PUBLIC    XDATA    FLOAT     ki_rate
      0001062BH   PUBLIC    XDATA    FLOAT     kp_ccd
      0001062FH   PUBLIC    XDATA    FLOAT     kp_rate
      00010637H   PUBLIC    XDATA    FLOAT     rate_error_sum
      0001063BH   PUBLIC    XDATA    FLOAT     target_angle
      0001063FH   PUBLIC    XDATA    FLOAT     ctimer_speed
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 111


      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00010625H   SYMBOL    XDATA    BYTE      speed_side_out_lock
      00010633H   SYMBOL    XDATA    FLOAT     rate_integral_limit

      00FEF0DFH   BLOCK     ECODE    ---       LVL=0
      00010E1AH   SYMBOL    XDATA    FLOAT     target_angle
      00010E1EH   SYMBOL    XDATA    FLOAT     current_angle
      00FEF0F3H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010643H   SYMBOL    XDATA    FLOAT     angle_error
      00010647H   SYMBOL    XDATA    FLOAT     angle_last
      0001064BH   SYMBOL    XDATA    FLOAT     angle_integral
      0001064FH   SYMBOL    XDATA    FLOAT     output
      ---         BLOCKEND  ---      ---       LVL=1
      00FEF0DFH   LINE      ECODE    ---       #58
      00FEF0F3H   LINE      ECODE    ---       #59
      00FEF0F3H   LINE      ECODE    ---       #65
      00FEF115H   LINE      ECODE    ---       #66
      00FEF12BH   LINE      ECODE    ---       #67
      00FEF146H   LINE      ECODE    ---       #68
      00FEF15CH   LINE      ECODE    ---       #69
      00FEF177H   LINE      ECODE    ---       #75
      00FEF1A6H   LINE      ECODE    ---       #86
      00FEF1D2H   LINE      ECODE    ---       #90
      00FEF1E4H   LINE      ECODE    ---       #92
      00FEF1F4H   LINE      ECODE    ---       #94
      00FEF1F6H   LINE      ECODE    ---       #97
      00FEF206H   LINE      ECODE    ---       #99
      00FEF216H   LINE      ECODE    ---       #100
      00FEF216H   LINE      ECODE    ---       #103
      00FEF26EH   LINE      ECODE    ---       #106
      00FEF27AH   LINE      ECODE    ---       #107
      00FEF27AH   LINE      ECODE    ---       #108
      00FEF299H   LINE      ECODE    ---       #109
      00FEF2ADH   LINE      ECODE    ---       #110
      00FEF321H   LINE      ECODE    ---       #111
      00FEF335H   LINE      ECODE    ---       #112
      00FEF33AH   LINE      ECODE    ---       #113
      00FEF33CH   LINE      ECODE    ---       #116
      00FEF342H   LINE      ECODE    ---       #117
      00FEF34EH   LINE      ECODE    ---       #118
      00FEF34EH   LINE      ECODE    ---       #119
      00FEF358H   LINE      ECODE    ---       #120
      ---         BLOCKEND  ---      ---       LVL=0

      00FEF359H   BLOCK     ECODE    ---       LVL=0
      00010E22H   SYMBOL    XDATA    FLOAT     target_rate
      00010E26H   SYMBOL    XDATA    FLOAT     current_rate
      00FEF36DH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010653H   SYMBOL    XDATA    FLOAT     rate_error
      00010657H   SYMBOL    XDATA    FLOAT     rate_last
      0001065BH   SYMBOL    XDATA    FLOAT     rate_integral
      0001065FH   SYMBOL    XDATA    FLOAT     output
      ---         BLOCKEND  ---      ---       LVL=1
      00FEF359H   LINE      ECODE    ---       #161
      00FEF36DH   LINE      ECODE    ---       #162
      00FEF36DH   LINE      ECODE    ---       #170
      00FEF38FH   LINE      ECODE    ---       #171
      00FEF3B7H   LINE      ECODE    ---       #174
      00FEF3D6H   LINE      ECODE    ---       #175
      00FEF400H   LINE      ECODE    ---       #176
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 112


      00FEF42AH   LINE      ECODE    ---       #177
      00FEF44CH   LINE      ECODE    ---       #196
      00FEF47CH   LINE      ECODE    ---       #197
      00FEF4B4H   LINE      ECODE    ---       #205
      00FEF514H   LINE      ECODE    ---       #207
      00FEF528H   LINE      ECODE    ---       #216
      00FEF542H   LINE      ECODE    ---       #218
      00FEF556H   LINE      ECODE    ---       #219
      00FEF558H   LINE      ECODE    ---       #220
      00FEF578H   LINE      ECODE    ---       #222
      00FEF590H   LINE      ECODE    ---       #223
      00FEF590H   LINE      ECODE    ---       #227
      00FEF599H   LINE      ECODE    ---       #228
      00FEF5A5H   LINE      ECODE    ---       #229
      00FEF5AFH   LINE      ECODE    ---       #230
      ---         BLOCKEND  ---      ---       LVL=0

      00FEF5B0H   BLOCK     ECODE    ---       LVL=0
      00010E2AH   SYMBOL    XDATA    FLOAT     target_speed
      00010E2EH   SYMBOL    XDATA    FLOAT     current_speed
      00FEF5C4H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010663H   SYMBOL    XDATA    FLOAT     speed_error
      00010667H   SYMBOL    XDATA    FLOAT     speed_error_last
      0001066BH   SYMBOL    XDATA    FLOAT     speed_integral
      0001066FH   SYMBOL    XDATA    FLOAT     speed_integral_sum
      00010673H   SYMBOL    XDATA    FLOAT     output
      ---         BLOCKEND  ---      ---       LVL=1
      00FEF5B0H   LINE      ECODE    ---       #236
      00FEF5C4H   LINE      ECODE    ---       #237
      00FEF5C4H   LINE      ECODE    ---       #272
      00FEF5E6H   LINE      ECODE    ---       #273
      00FEF605H   LINE      ECODE    ---       #274
      00FEF61BH   LINE      ECODE    ---       #275
      00FEF62BH   LINE      ECODE    ---       #276
      00FEF641H   LINE      ECODE    ---       #277
      00FEF651H   LINE      ECODE    ---       #278
      00FEF673H   LINE      ECODE    ---       #283
      00FEF69BH   LINE      ECODE    ---       #284
      00FEF6C1H   LINE      ECODE    ---       #285
      00FEF6D5H   LINE      ECODE    ---       #287
      00FEF705H   LINE      ECODE    ---       #288
      00FEF711H   LINE      ECODE    ---       #295
      00FEF77BH   LINE      ECODE    ---       #299
      00FEF78FH   LINE      ECODE    ---       #301
      00FEF7A5H   LINE      ECODE    ---       #303
      00FEF7B5H   LINE      ECODE    ---       #304
      00FEF7B7H   LINE      ECODE    ---       #305
      00FEF7CDH   LINE      ECODE    ---       #307
      00FEF7DDH   LINE      ECODE    ---       #308
      00FEF7DDH   LINE      ECODE    ---       #309
      00FEF7E7H   LINE      ECODE    ---       #311
      ---         BLOCKEND  ---      ---       LVL=0

      00FEF7E8H   BLOCK     ECODE    ---       LVL=0
      00010E32H   SYMBOL    XDATA    FLOAT     target_ccd
      00010E36H   SYMBOL    XDATA    FLOAT     current_ccd
      00FEF7FCH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010677H   SYMBOL    XDATA    FLOAT     ccd_error
      0001067BH   SYMBOL    XDATA    FLOAT     ccd_error_last
      0001067FH   SYMBOL    XDATA    FLOAT     ccd_integral
      00010683H   SYMBOL    XDATA    FLOAT     output
      ---         BLOCKEND  ---      ---       LVL=1
      00FEF7E8H   LINE      ECODE    ---       #313
      00FEF7FCH   LINE      ECODE    ---       #314
      00FEF7FCH   LINE      ECODE    ---       #320
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 113


      00FEF81EH   LINE      ECODE    ---       #321
      00FEF83DH   LINE      ECODE    ---       #325
      00FEF8B1H   LINE      ECODE    ---       #326
      00FEF8C5H   LINE      ECODE    ---       #328
      00FEF8DBH   LINE      ECODE    ---       #330
      00FEF8EBH   LINE      ECODE    ---       #331
      00FEF8EDH   LINE      ECODE    ---       #332
      00FEF903H   LINE      ECODE    ---       #334
      00FEF913H   LINE      ECODE    ---       #335
      00FEF913H   LINE      ECODE    ---       #336
      00FEF91DH   LINE      ECODE    ---       #337
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       point_processing
      00FEE6F0H   PUBLIC    ECODE    ---       correct_point?
      00FEE2C5H   PUBLIC    ECODE    ---       take_point?
      00FEE428H   PUBLIC    ECODE    ---       bytes_to_double?
      00FEE3A1H   PUBLIC    ECODE    ---       double_to_bytes?
      00FEE511H   PUBLIC    ECODE    ---       eeprom_to_array?
      00FEE359H   PUBLIC    ECODE    ---       selection_status?
      00FEE625H   PUBLIC    ECODE    ---       get_point_error?
      00FEDE94H   PUBLIC    ECODE    ---       test_point_init?
      00FEE4DAH   PUBLIC    ECODE    ---       eeprom_read_double?
      00FEE0B7H   PUBLIC    ECODE    ---       read_gps_point_task?
      00FEE48DH   PUBLIC    ECODE    ---       eeprom_write_double?
      00010000H   PUBLIC    XDATA    BYTE      got_point_num
      00010001H   PUBLIC    XDATA    DOUBLE    two_points_distance
      00010009H   PUBLIC    XDATA    ---       point_data_array
      00010225H   PUBLIC    XDATA    DWORD     read_start_addr
      00010229H   PUBLIC    XDATA    DWORD     start_addr
      0001022DH   PUBLIC    XDATA    ---       point_error_data
      0001023DH   PUBLIC    XDATA    BYTE      read_point_status_node
      0001023EH   PUBLIC    XDATA    BYTE      get_data_error_flag
      0001023FH   PUBLIC    XDATA    ---       point_corrected_data
      0001024FH   PUBLIC    XDATA    BYTE      receive
      00010C71H   PUBLIC    XDATA    BYTE      ?take_point??BYTE
      00010C81H   PUBLIC    XDATA    BYTE      ?eeprom_write_double??BYTE
      00010D48H   PUBLIC    XDATA    BYTE      ?get_point_error??BYTE
      00010D54H   PUBLIC    XDATA    BYTE      ?correct_point??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 114


      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FEDE94H   BLOCK     ECODE    ---       LVL=0
      00FEDE94H   LINE      ECODE    ---       #31
      00FEDE94H   LINE      ECODE    ---       #33
      00FEDEB6H   LINE      ECODE    ---       #34
      00FEDED8H   LINE      ECODE    ---       #35
      00FEDEE2H   LINE      ECODE    ---       #36
      00FEDF04H   LINE      ECODE    ---       #37
      00FEDF26H   LINE      ECODE    ---       #38
      00FEDF30H   LINE      ECODE    ---       #39
      00FEDF52H   LINE      ECODE    ---       #40
      00FEDF74H   LINE      ECODE    ---       #41
      00FEDF7EH   LINE      ECODE    ---       #42
      00FEDFA0H   LINE      ECODE    ---       #43
      00FEDFC2H   LINE      ECODE    ---       #44
      00FEDFCCH   LINE      ECODE    ---       #45
      00FEDFEEH   LINE      ECODE    ---       #46
      00FEE010H   LINE      ECODE    ---       #47
      00FEE01AH   LINE      ECODE    ---       #48
      00FEE03CH   LINE      ECODE    ---       #49
      00FEE05EH   LINE      ECODE    ---       #50
      00FEE068H   LINE      ECODE    ---       #51
      00FEE08AH   LINE      ECODE    ---       #52
      00FEE0ACH   LINE      ECODE    ---       #53
      00FEE0B6H   LINE      ECODE    ---       #54
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE0B7H   BLOCK     ECODE    ---       LVL=0
      00FEE0C3H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010F47H   SYMBOL    XDATA    BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE0B7H   LINE      ECODE    ---       #58
      00FEE0B7H   LINE      ECODE    ---       #60
      00FEE0C3H   LINE      ECODE    ---       #70
      00FEE0C3H   LINE      ECODE    ---       #72
      00FEE0CBH   LINE      ECODE    ---       #75
      00FEE102H   LINE      ECODE    ---       #76
      00FEE119H   LINE      ECODE    ---       #77
      00FEE150H   LINE      ECODE    ---       #78
      00FEE167H   LINE      ECODE    ---       #79
      00FEE187H   LINE      ECODE    ---       #80
      00FEE1A6H   LINE      ECODE    ---       #81
      00FEE1BDH   LINE      ECODE    ---       #82
      00FEE1DEH   LINE      ECODE    ---       #84
      00FEE1DEH   LINE      ECODE    ---       #85
      00FEE1F8H   LINE      ECODE    ---       #92
      00FEE1FEH   LINE      ECODE    ---       #93
      00FEE1FEH   LINE      ECODE    ---       #94
      00FEE20AH   LINE      ECODE    ---       #96
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 115


      00FEE29EH   LINE      ECODE    ---       #97
      00FEE2C4H   LINE      ECODE    ---       #98
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE2C5H   BLOCK     ECODE    ---       LVL=0
      00010C71H   SYMBOL    XDATA    DOUBLE    longitude
      00010C79H   SYMBOL    XDATA    DOUBLE    latitude
      00FEE2C5H   LINE      ECODE    ---       #102
      00FEE2D7H   LINE      ECODE    ---       #104
      00FEE309H   LINE      ECODE    ---       #105
      00FEE341H   LINE      ECODE    ---       #106
      00FEE358H   LINE      ECODE    ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE359H   BLOCK     ECODE    ---       LVL=0
      00010F35H   SYMBOL    XDATA    INT       state
      00FEE35FH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010250H   SYMBOL    XDATA    BYTE      receive
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE359H   LINE      ECODE    ---       #109
      00FEE35FH   LINE      ECODE    ---       #110
      00FEE35FH   LINE      ECODE    ---       #112
      00FEE369H   LINE      ECODE    ---       #114
      00FEE389H   LINE      ECODE    ---       #115
      00FEE3A0H   LINE      ECODE    ---       #116
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE3A1H   BLOCK     ECODE    ---       LVL=0
      00010C50H   SYMBOL    XDATA    DOUBLE    value
      00FEE3B3H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010251H   SYMBOL    XDATA    ---       result
      00010C58H   SYMBOL    XDATA    ---       converter
      00010C60H   SYMBOL    XDATA    BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE3A1H   LINE      ECODE    ---       #120
      00FEE3B3H   LINE      ECODE    ---       #121
      00FEE3B3H   LINE      ECODE    ---       #130
      00FEE3D7H   LINE      ECODE    ---       #131
      00FEE3DEH   LINE      ECODE    ---       #132
      00FEE408H   LINE      ECODE    ---       #133
      00FEE41FH   LINE      ECODE    ---       #134
      00FEE427H   LINE      ECODE    ---       #135
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE428H   BLOCK     ECODE    ---       LVL=0
      00010CF1H   SYMBOL    XDATA    ---       bytes
      00FEE432H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010CF5H   SYMBOL    XDATA    ---       converter
      00010CFDH   SYMBOL    XDATA    BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE428H   LINE      ECODE    ---       #137
      00FEE432H   LINE      ECODE    ---       #138
      00FEE432H   LINE      ECODE    ---       #145
      00FEE439H   LINE      ECODE    ---       #146
      00FEE463H   LINE      ECODE    ---       #147
      00FEE47AH   LINE      ECODE    ---       #148
      00FEE48CH   LINE      ECODE    ---       #149
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE48DH   BLOCK     ECODE    ---       LVL=0
      00010C81H   SYMBOL    XDATA    DWORD     addr
      00010C85H   SYMBOL    XDATA    DOUBLE    value
      00FEE497H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010C8DH   SYMBOL    XDATA    ---       bytes
      ---         BLOCKEND  ---      ---       LVL=1
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 116


      00FEE48DH   LINE      ECODE    ---       #152
      00FEE497H   LINE      ECODE    ---       #153
      00FEE497H   LINE      ECODE    ---       #154
      00FEE4B7H   LINE      ECODE    ---       #155
      00FEE4D9H   LINE      ECODE    ---       #156
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE4DAH   BLOCK     ECODE    ---       LVL=0
      00010D3CH   SYMBOL    XDATA    DWORD     addr
      00FEE4E4H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010D40H   SYMBOL    XDATA    ---       bytes
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE4DAH   LINE      ECODE    ---       #158
      00FEE4E4H   LINE      ECODE    ---       #159
      00FEE4E4H   LINE      ECODE    ---       #161
      00FEE504H   LINE      ECODE    ---       #162
      00FEE510H   LINE      ECODE    ---       #163
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE511H   BLOCK     ECODE    ---       LVL=0
      00010259H   SYMBOL    XDATA    BYTE      receive_data
      00010F48H   SYMBOL    XDATA    BYTE      i
      00FEE511H   LINE      ECODE    ---       #165
      00FEE511H   LINE      ECODE    ---       #166
      00FEE511H   LINE      ECODE    ---       #170
      00FEE519H   LINE      ECODE    ---       #172
      00FEE550H   LINE      ECODE    ---       #173
      00FEE58DH   LINE      ECODE    ---       #175
      00FEE5B3H   LINE      ECODE    ---       #176
      00FEE5D2H   LINE      ECODE    ---       #177
      00FEE5E9H   LINE      ECODE    ---       #178
      00FEE60AH   LINE      ECODE    ---       #180
      00FEE60AH   LINE      ECODE    ---       #181
      00FEE624H   LINE      ECODE    ---       #183
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE625H   BLOCK     ECODE    ---       LVL=0
      00010D48H   SYMBOL    XDATA    ---       collected_point
      00010D4CH   SYMBOL    XDATA    ---       measured_point
      00010D50H   SYMBOL    XDATA    ---       out_error
      00FEE625H   LINE      ECODE    ---       #192
      00FEE639H   LINE      ECODE    ---       #196
      00FEE690H   LINE      ECODE    ---       #197
      00FEE6EFH   LINE      ECODE    ---       #198
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE6F0H   BLOCK     ECODE    ---       LVL=0
      00010D54H   SYMBOL    XDATA    ---       measured_point
      00010D58H   SYMBOL    XDATA    ---       point_error
      00010D5CH   SYMBOL    XDATA    ---       out_corrected
      00FEE6F0H   LINE      ECODE    ---       #200
      00FEE704H   LINE      ECODE    ---       #205
      00FEE75BH   LINE      ECODE    ---       #206
      00FEE7BAH   LINE      ECODE    ---       #207
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       quaternion_pose_calculating
      00FEC3CAH   PUBLIC    ECODE    ---       get_euler_angles?
      00FEBC78H   PUBLIC    ECODE    ---       quaternion_update?
      00FEC705H   PUBLIC    ECODE    ---       reset_quaternion?
      00FEC7B2H   PUBLIC    ECODE    ---       low_pass_filter?
      00FEC3A4H   PUBLIC    ECODE    ---       get_quaternion?
      0001094EH   PUBLIC    XDATA    FLOAT     actual_angular_speed
      00010952H   PUBLIC    XDATA    FLOAT     ax
      00010956H   PUBLIC    XDATA    FLOAT     ay
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 117


      0001095AH   PUBLIC    XDATA    FLOAT     az
      0001095EH   PUBLIC    XDATA    FLOAT     gx
      00010962H   PUBLIC    XDATA    FLOAT     gy
      00010966H   PUBLIC    XDATA    FLOAT     gz
      0001096AH   PUBLIC    XDATA    ---       q
      0001097AH   PUBLIC    XDATA    ---       angles
      000107E6H   PUBLIC    XDATA    BYTE      ?quaternion_update??BYTE
      00010D60H   PUBLIC    XDATA    BYTE      ?low_pass_filter??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FEBADCH   SYMBOL    ECODE    ---       quaternion_normalize

      00FEBADCH   BLOCK     ECODE    ---       LVL=0
      00010E3AH   SYMBOL    XDATA    ---       q
      00FEBAE6H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E3EH   SYMBOL    XDATA    FLOAT     norm
      ---         BLOCKEND  ---      ---       LVL=1
      00FEBADCH   LINE      ECODE    ---       #17
      00FEBAE6H   LINE      ECODE    ---       #18
      00FEBBC4H   LINE      ECODE    ---       #19
      00FEBBD5H   LINE      ECODE    ---       #21
      00FEBBFBH   LINE      ECODE    ---       #22
      00FEBC23H   LINE      ECODE    ---       #23
      00FEBC4DH   LINE      ECODE    ---       #24
      00FEBC77H   LINE      ECODE    ---       #25
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 118


      ---         BLOCKEND  ---      ---       LVL=0

      00FEBC78H   BLOCK     ECODE    ---       LVL=0
      000107E6H   SYMBOL    XDATA    FLOAT     gx
      000107EAH   SYMBOL    XDATA    FLOAT     gy
      000107EEH   SYMBOL    XDATA    FLOAT     gz
      000107F2H   SYMBOL    XDATA    FLOAT     ax
      000107F6H   SYMBOL    XDATA    FLOAT     ay
      000107FAH   SYMBOL    XDATA    FLOAT     az
      000107FEH   SYMBOL    XDATA    FLOAT     dt
      00FEBC8CH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010802H   SYMBOL    XDATA    FLOAT     norm
      00010806H   SYMBOL    XDATA    FLOAT     vx
      0001080AH   SYMBOL    XDATA    FLOAT     vy
      0001080EH   SYMBOL    XDATA    FLOAT     vz
      00010812H   SYMBOL    XDATA    FLOAT     ex
      00010816H   SYMBOL    XDATA    FLOAT     ey
      0001081AH   SYMBOL    XDATA    FLOAT     ez
      0001081EH   SYMBOL    XDATA    FLOAT     integralFBx
      00010822H   SYMBOL    XDATA    FLOAT     integralFBy
      00010826H   SYMBOL    XDATA    FLOAT     integralFBz
      0001082AH   SYMBOL    XDATA    FLOAT     ki
      0001082EH   SYMBOL    XDATA    FLOAT     kp
      00010832H   SYMBOL    XDATA    FLOAT     q0
      00010836H   SYMBOL    XDATA    FLOAT     q1
      0001083AH   SYMBOL    XDATA    FLOAT     q2
      0001083EH   SYMBOL    XDATA    FLOAT     q3
      ---         BLOCKEND  ---      ---       LVL=1
      00FEBC78H   LINE      ECODE    ---       #28
      00FEBC8CH   LINE      ECODE    ---       #32
      00FEBCB0H   LINE      ECODE    ---       #33
      00FEBCC2H   LINE      ECODE    ---       #34
      00FEBCD2H   LINE      ECODE    ---       #38
      00FEBD42H   LINE      ECODE    ---       #39
      00FEBD53H   LINE      ECODE    ---       #40
      00FEBD72H   LINE      ECODE    ---       #41
      00FEBD91H   LINE      ECODE    ---       #42
      00FEBDB0H   LINE      ECODE    ---       #45
      00FEBDFEH   LINE      ECODE    ---       #46
      00FEBE4CH   LINE      ECODE    ---       #47
      00FEBED2H   LINE      ECODE    ---       #50
      00FEBF14H   LINE      ECODE    ---       #51
      00FEBF56H   LINE      ECODE    ---       #52
      00FEBF98H   LINE      ECODE    ---       #55
      00FEBFD5H   LINE      ECODE    ---       #56
      00FEC012H   LINE      ECODE    ---       #57
      00FEC04FH   LINE      ECODE    ---       #60
      00FEC08CH   LINE      ECODE    ---       #61
      00FEC0C9H   LINE      ECODE    ---       #62
      00FEC106H   LINE      ECODE    ---       #65
      00FEC11AH   LINE      ECODE    ---       #66
      00FEC12EH   LINE      ECODE    ---       #67
      00FEC142H   LINE      ECODE    ---       #68
      00FEC156H   LINE      ECODE    ---       #70
      00FEC1EBH   LINE      ECODE    ---       #71
      00FEC27AH   LINE      ECODE    ---       #72
      00FEC309H   LINE      ECODE    ---       #73
      00FEC398H   LINE      ECODE    ---       #76
      00FEC3A3H   LINE      ECODE    ---       #77
      ---         BLOCKEND  ---      ---       LVL=0

      00FEC3A4H   BLOCK     ECODE    ---       LVL=0
      00FEC3AEH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010EFFH   SYMBOL    XDATA    ---       ?DR12?
      ---         BLOCKEND  ---      ---       LVL=1
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 119


      00FEC3A4H   LINE      ECODE    ---       #80
      00FEC3AEH   LINE      ECODE    ---       #81
      00FEC3C9H   LINE      ECODE    ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      00FEC3CAH   BLOCK     ECODE    ---       LVL=0
      00FEC3D4H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010BF6H   SYMBOL    XDATA    ---       ?DR12?
      00010BFAH   SYMBOL    XDATA    ---       euler
      00010C06H   SYMBOL    XDATA    FLOAT     sinp
      ---         BLOCKEND  ---      ---       LVL=1
      00FEC3CAH   LINE      ECODE    ---       #85
      00FEC3D4H   LINE      ECODE    ---       #92
      00FEC494H   LINE      ECODE    ---       #95
      00FEC4E2H   LINE      ECODE    ---       #96
      00FEC502H   LINE      ECODE    ---       #97
      00FEC546H   LINE      ECODE    ---       #99
      00FEC566H   LINE      ECODE    ---       #103
      00FEC626H   LINE      ECODE    ---       #107
      00FEC643H   LINE      ECODE    ---       #108
      00FEC660H   LINE      ECODE    ---       #109
      00FEC67DH   LINE      ECODE    ---       #113
      00FEC6A1H   LINE      ECODE    ---       #114
      00FEC6C5H   LINE      ECODE    ---       #115
      00FEC6E9H   LINE      ECODE    ---       #117
      00FEC704H   LINE      ECODE    ---       #118
      ---         BLOCKEND  ---      ---       LVL=0

      00FEC705H   BLOCK     ECODE    ---       LVL=0
      00FEC705H   LINE      ECODE    ---       #123
      00FEC705H   LINE      ECODE    ---       #125
      00FEC715H   LINE      ECODE    ---       #126
      00FEC721H   LINE      ECODE    ---       #127
      00FEC72DH   LINE      ECODE    ---       #128
      00FEC739H   LINE      ECODE    ---       #131
      00FEC75DH   LINE      ECODE    ---       #132
      00FEC781H   LINE      ECODE    ---       #133
      00FEC78DH   LINE      ECODE    ---       #136
      00FEC7B1H   LINE      ECODE    ---       #137
      ---         BLOCKEND  ---      ---       LVL=0

      00FEC7B2H   BLOCK     ECODE    ---       LVL=0
      00010D60H   SYMBOL    XDATA    FLOAT     input
      00010D64H   SYMBOL    XDATA    FLOAT     prev_output
      00010D68H   SYMBOL    XDATA    FLOAT     alpha
      00FEC7B2H   LINE      ECODE    ---       #147
      00FEC7C6H   LINE      ECODE    ---       #148
      00FEC808H   LINE      ECODE    ---       #149
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       exception_handling
      00FF2BA3H   PUBLIC    ECODE    ---       check_cross_border?
      00FF2EA2H   PUBLIC    ECODE    ---       posture_exception_handling?
      00010A44H   PUBLIC    XDATA    BYTE      ?check_cross_border??BYTE
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 120


      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FF2BA3H   BLOCK     ECODE    ---       LVL=0
      00010A44H   SYMBOL    XDATA    WORD      current_idx
      00010A46H   SYMBOL    XDATA    ---       path_array
      00010A4AH   SYMBOL    XDATA    FLOAT     current_lat
      00010A4EH   SYMBOL    XDATA    FLOAT     current_lon
      00FF2BB3H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010A52H   SYMBOL    XDATA    DOUBLE    dist
      00010A5AH   SYMBOL    XDATA    DOUBLE    prev_dist
      00010A62H   SYMBOL    XDATA    DOUBLE    next_dist
      ---         BLOCKEND  ---      ---       LVL=1
      00FF2BA3H   LINE      ECODE    ---       #9
      00FF2BB3H   LINE      ECODE    ---       #10
      00FF2BB3H   LINE      ECODE    ---       #17
      00FF2BD8H   LINE      ECODE    ---       #19
      00FF2BDCH   LINE      ECODE    ---       #20
      00FF2BDCH   LINE      ECODE    ---       #24
      00FF2CD0H   LINE      ECODE    ---       #30
      00FF2D8AH   LINE      ECODE    ---       #35
      00FF2E48H   LINE      ECODE    ---       #41
      00FF2EA1H   LINE      ECODE    ---       #42
      ---         BLOCKEND  ---      ---       LVL=0

      00FF2EA2H   BLOCK     ECODE    ---       LVL=0
      00FF2EA2H   LINE      ECODE    ---       #44
      00FF2EA2H   LINE      ECODE    ---       #46
      00FF2ECEH   LINE      ECODE    ---       #49
      00FF2EDAH   LINE      ECODE    ---       #50
      00FF2EE6H   LINE      ECODE    ---       #51
      00FF2EFAH   LINE      ECODE    ---       #52
      00FF2EFEH   LINE      ECODE    ---       #53
      00FF2F03H   LINE      ECODE    ---       #54
      00FF2F07H   LINE      ECODE    ---       #55
      00FF2F17H   LINE      ECODE    ---       #56
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 121


      00FF2F17H   LINE      ECODE    ---       #57
      00FF2F43H   LINE      ECODE    ---       #60
      00FF2F4FH   LINE      ECODE    ---       #61
      00FF2F5BH   LINE      ECODE    ---       #62
      00FF2F6FH   LINE      ECODE    ---       #63
      00FF2F73H   LINE      ECODE    ---       #64
      00FF2F78H   LINE      ECODE    ---       #65
      00FF2F7CH   LINE      ECODE    ---       #66
      00FF2F8CH   LINE      ECODE    ---       #67
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       task
      00FE93D6H   PUBLIC    ECODE    ---       ramp_function?
      00FE9E98H   PUBLIC    ECODE    ---       Complete_function?
      00FE9EBAH   PUBLIC    ECODE    ---       point_task_function?
      00FE9AEFH   PUBLIC    ECODE    ---       turn_function?
      00FE8FF6H   PUBLIC    ECODE    ---       normal_function?
      00FE9680H   PUBLIC    ECODE    ---       arch_function?
      00FE98C2H   PUBLIC    ECODE    ---       barricade_function?
      00FE908EH   PUBLIC    ECODE    ---       grass_function?
      00FE9175H   PUBLIC    ECODE    ---       bumpy_function?
      00010986H   PUBLIC    XDATA    BYTE      normal_task_flag
      00010987H   PUBLIC    XDATA    BYTE      arch_task_flag
      00010988H   PUBLIC    XDATA    BYTE      barricade_task_flag
      00010989H   PUBLIC    XDATA    BYTE      grass_task_flag
      0001098AH   PUBLIC    XDATA    BYTE      bumpy_task_flag
      0001098BH   PUBLIC    XDATA    BYTE      ramp_task_flag
      0001098CH   PUBLIC    XDATA    BYTE      Complete_task_flag
      0001098DH   PUBLIC    XDATA    BYTE      turn_task_flag
      0001098EH   PUBLIC    XDATA    BYTE      now_task_node
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 122


      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0

      00FE8FF6H   BLOCK     ECODE    ---       LVL=0
      00FE8FF6H   LINE      ECODE    ---       #41
      00FE8FF6H   LINE      ECODE    ---       #44
      00FE8FFBH   LINE      ECODE    ---       #46
      00FE901DH   LINE      ECODE    ---       #47
      00FE903FH   LINE      ECODE    ---       #49
      00FE9061H   LINE      ECODE    ---       #50
      00FE9067H   LINE      ECODE    ---       #51
      00FE908DH   LINE      ECODE    ---       #52
      ---         BLOCKEND  ---      ---       LVL=0

      00FE908EH   BLOCK     ECODE    ---       LVL=0
      00010F03H   SYMBOL    XDATA    ---       now_task_node
      00FE9098H   BLOCK     ECODE    NEAR LAB  LVL=1
      0001098FH   SYMBOL    XDATA    BYTE      grass_fun_node
      00010990H   SYMBOL    XDATA    BYTE      grass_running_time
      ---         BLOCKEND  ---      ---       LVL=1
      00FE908EH   LINE      ECODE    ---       #54
      00FE9098H   LINE      ECODE    ---       #55
      00FE9098H   LINE      ECODE    ---       #58
      00FE90ACH   LINE      ECODE    ---       #60
      00FE90ACH   LINE      ECODE    ---       #62
      00FE90BCH   LINE      ECODE    ---       #63
      00FE90C8H   LINE      ECODE    ---       #64
      00FE90EEH   LINE      ECODE    ---       #65
      00FE90F4H   LINE      ECODE    ---       #66
      00FE90F8H   LINE      ECODE    ---       #68
      00FE90FAH   LINE      ECODE    ---       #69
      00FE90FAH   LINE      ECODE    ---       #71
      00FE9105H   LINE      ECODE    ---       #72
      00FE9115H   LINE      ECODE    ---       #73
      00FE9121H   LINE      ECODE    ---       #74
      00FE9147H   LINE      ECODE    ---       #75
      00FE9153H   LINE      ECODE    ---       #76
      00FE9159H   LINE      ECODE    ---       #78
      00FE915BH   LINE      ECODE    ---       #79
      00FE915BH   LINE      ECODE    ---       #81
      00FE916AH   LINE      ECODE    ---       #82
      00FE9170H   LINE      ECODE    ---       #83
      00FE9174H   LINE      ECODE    ---       #85
      00FE9174H   LINE      ECODE    ---       #86
      00FE9174H   LINE      ECODE    ---       #87
      00FE9174H   LINE      ECODE    ---       #88
      00FE9174H   LINE      ECODE    ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      00FE9175H   BLOCK     ECODE    ---       LVL=0
      00010F07H   SYMBOL    XDATA    ---       now_task_node
      00FE917FH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010991H   SYMBOL    XDATA    BYTE      bumpy_fun_node
      00010992H   SYMBOL    XDATA    DWORD     bumpy_time
      00010996H   SYMBOL    XDATA    BYTE      bumpy_time_lock
      ---         BLOCKEND  ---      ---       LVL=1
      00FE9175H   LINE      ECODE    ---       #91
      00FE917FH   LINE      ECODE    ---       #92
      00FE917FH   LINE      ECODE    ---       #96
      00FE9183H   LINE      ECODE    ---       #98
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 123


      00FE918CH   LINE      ECODE    ---       #100
      00FE91A0H   LINE      ECODE    ---       #101
      00FE91A6H   LINE      ECODE    ---       #102
      00FE91A6H   LINE      ECODE    ---       #103
      00FE91C2H   LINE      ECODE    ---       #105
      00FE91C8H   LINE      ECODE    ---       #106
      00FE91D6H   LINE      ECODE    ---       #107
      00FE91DCH   LINE      ECODE    ---       #108
      00FE91DCH   LINE      ECODE    ---       #109
      00FE91F9H   LINE      ECODE    ---       #111
      00FE91F9H   LINE      ECODE    ---       #114
      00FE91FEH   LINE      ECODE    ---       #128
      00FE921CH   LINE      ECODE    ---       #129
      00FE923EH   LINE      ECODE    ---       #130
      00FE9260H   LINE      ECODE    ---       #131
      00FE9286H   LINE      ECODE    ---       #133
      00FE929FH   LINE      ECODE    ---       #135
      00FE92A5H   LINE      ECODE    ---       #136
      00FE92A5H   LINE      ECODE    ---       #140
      00FE92A8H   LINE      ECODE    ---       #141
      00FE92A8H   LINE      ECODE    ---       #144
      00FE92AEH   LINE      ECODE    ---       #159
      00FE92BAH   LINE      ECODE    ---       #161
      00FE92C6H   LINE      ECODE    ---       #162
      00FE92ECH   LINE      ECODE    ---       #163
      00FE9305H   LINE      ECODE    ---       #165
      00FE930BH   LINE      ECODE    ---       #166
      00FE930BH   LINE      ECODE    ---       #169
      00FE930EH   LINE      ECODE    ---       #170
      00FE930EH   LINE      ECODE    ---       #173
      00FE9314H   LINE      ECODE    ---       #189
      00FE9332H   LINE      ECODE    ---       #190
      00FE9354H   LINE      ECODE    ---       #191
      00FE9376H   LINE      ECODE    ---       #192
      00FE939CH   LINE      ECODE    ---       #193
      00FE93B2H   LINE      ECODE    ---       #195
      00FE93B8H   LINE      ECODE    ---       #196
      00FE93B8H   LINE      ECODE    ---       #199
      00FE93BAH   LINE      ECODE    ---       #200
      00FE93BAH   LINE      ECODE    ---       #202
      00FE93C0H   LINE      ECODE    ---       #203
      00FE93CFH   LINE      ECODE    ---       #204
      00FE93D5H   LINE      ECODE    ---       #210
      00FE93D5H   LINE      ECODE    ---       #211
      00FE93D5H   LINE      ECODE    ---       #212
      00FE93D5H   LINE      ECODE    ---       #213
      00FE93D5H   LINE      ECODE    ---       #214
      ---         BLOCKEND  ---      ---       LVL=0

      00FE93D6H   BLOCK     ECODE    ---       LVL=0
      00010F0BH   SYMBOL    XDATA    ---       now_task_node
      00FE93E0H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010997H   SYMBOL    XDATA    BYTE      ramp_fun_node
      00010998H   SYMBOL    XDATA    DWORD     ramp_time
      0001099CH   SYMBOL    XDATA    BYTE      ramp_time_lock
      ---         BLOCKEND  ---      ---       LVL=1
      00FE93D6H   LINE      ECODE    ---       #216
      00FE93E0H   LINE      ECODE    ---       #217
      00FE93E0H   LINE      ECODE    ---       #223
      00FE93E9H   LINE      ECODE    ---       #225
      00FE93FDH   LINE      ECODE    ---       #226
      00FE9403H   LINE      ECODE    ---       #227
      00FE9403H   LINE      ECODE    ---       #228
      00FE941FH   LINE      ECODE    ---       #230
      00FE9425H   LINE      ECODE    ---       #231
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 124


      00FE9433H   LINE      ECODE    ---       #232
      00FE9439H   LINE      ECODE    ---       #233
      00FE943DH   LINE      ECODE    ---       #234
      00FE943DH   LINE      ECODE    ---       #237
      00FE9441H   LINE      ECODE    ---       #238
      00FE945FH   LINE      ECODE    ---       #240
      00FE945FH   LINE      ECODE    ---       #243
      00FE9464H   LINE      ECODE    ---       #255
      00FE9482H   LINE      ECODE    ---       #256
      00FE94A4H   LINE      ECODE    ---       #257
      00FE94C6H   LINE      ECODE    ---       #258
      00FE94ECH   LINE      ECODE    ---       #259
      00FE9505H   LINE      ECODE    ---       #261
      00FE950BH   LINE      ECODE    ---       #263
      00FE950BH   LINE      ECODE    ---       #266
      00FE950EH   LINE      ECODE    ---       #267
      00FE950EH   LINE      ECODE    ---       #270
      00FE9514H   LINE      ECODE    ---       #282
      00FE9536H   LINE      ECODE    ---       #283
      00FE9558H   LINE      ECODE    ---       #284
      00FE957AH   LINE      ECODE    ---       #285
      00FE95A0H   LINE      ECODE    ---       #286
      00FE95B9H   LINE      ECODE    ---       #288
      00FE95BFH   LINE      ECODE    ---       #289
      00FE95BFH   LINE      ECODE    ---       #292
      00FE95C2H   LINE      ECODE    ---       #293
      00FE95C2H   LINE      ECODE    ---       #296
      00FE95C8H   LINE      ECODE    ---       #306
      00FE95D8H   LINE      ECODE    ---       #307
      00FE95FAH   LINE      ECODE    ---       #308
      00FE961CH   LINE      ECODE    ---       #309
      00FE9642H   LINE      ECODE    ---       #310
      00FE9658H   LINE      ECODE    ---       #312
      00FE965EH   LINE      ECODE    ---       #313
      00FE965EH   LINE      ECODE    ---       #316
      00FE9660H   LINE      ECODE    ---       #317
      00FE9660H   LINE      ECODE    ---       #319
      00FE9666H   LINE      ECODE    ---       #320
      00FE9675H   LINE      ECODE    ---       #321
      00FE967BH   LINE      ECODE    ---       #322
      00FE967FH   LINE      ECODE    ---       #324
      00FE967FH   LINE      ECODE    ---       #325
      00FE967FH   LINE      ECODE    ---       #326
      00FE967FH   LINE      ECODE    ---       #327
      00FE967FH   LINE      ECODE    ---       #328
      ---         BLOCKEND  ---      ---       LVL=0

      00FE9680H   BLOCK     ECODE    ---       LVL=0
      00010F0FH   SYMBOL    XDATA    ---       now_task_node
      00FE968CH   BLOCK     ECODE    NEAR LAB  LVL=1
      0001099DH   SYMBOL    XDATA    BYTE      arch_fun_node
      0001099EH   SYMBOL    XDATA    DWORD     arch_time
      000109A2H   SYMBOL    XDATA    BYTE      arch_time_lock
      ---         BLOCKEND  ---      ---       LVL=1
      00FE9680H   LINE      ECODE    ---       #389
      00FE968CH   LINE      ECODE    ---       #390
      00FE968CH   LINE      ECODE    ---       #395
      00FE9695H   LINE      ECODE    ---       #397
      00FE96A9H   LINE      ECODE    ---       #398
      00FE96AFH   LINE      ECODE    ---       #399
      00FE96AFH   LINE      ECODE    ---       #400
      00FE96CBH   LINE      ECODE    ---       #402
      00FE96D0H   LINE      ECODE    ---       #403
      00FE96D6H   LINE      ECODE    ---       #404
      00FE96E4H   LINE      ECODE    ---       #405
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 125


      00FE96EAH   LINE      ECODE    ---       #406
      00FE96EEH   LINE      ECODE    ---       #407
      00FE96EEH   LINE      ECODE    ---       #411
      00FE970BH   LINE      ECODE    ---       #413
      00FE970BH   LINE      ECODE    ---       #415
      00FE9710H   LINE      ECODE    ---       #416
      00FE9716H   LINE      ECODE    ---       #417
      00FE9738H   LINE      ECODE    ---       #418
      00FE977AH   LINE      ECODE    ---       #419
      00FE979CH   LINE      ECODE    ---       #420
      00FE97A2H   LINE      ECODE    ---       #421
      00FE97C8H   LINE      ECODE    ---       #423
      00FE97CBH   LINE      ECODE    ---       #424
      00FE97CBH   LINE      ECODE    ---       #426
      00FE97D1H   LINE      ECODE    ---       #427
      00FE97E1H   LINE      ECODE    ---       #428
      00FE97EDH   LINE      ECODE    ---       #429
      00FE9813H   LINE      ECODE    ---       #430
      00FE982CH   LINE      ECODE    ---       #432
      00FE9832H   LINE      ECODE    ---       #433
      00FE9832H   LINE      ECODE    ---       #435
      00FE9835H   LINE      ECODE    ---       #436
      00FE9835H   LINE      ECODE    ---       #438
      00FE983BH   LINE      ECODE    ---       #439
      00FE984BH   LINE      ECODE    ---       #440
      00FE9857H   LINE      ECODE    ---       #441
      00FE987DH   LINE      ECODE    ---       #442
      00FE9893H   LINE      ECODE    ---       #444
      00FE9899H   LINE      ECODE    ---       #445
      00FE9899H   LINE      ECODE    ---       #447
      00FE989BH   LINE      ECODE    ---       #448
      00FE989BH   LINE      ECODE    ---       #450
      00FE98A0H   LINE      ECODE    ---       #451
      00FE98A6H   LINE      ECODE    ---       #452
      00FE98B5H   LINE      ECODE    ---       #453
      00FE98BBH   LINE      ECODE    ---       #454
      00FE98BFH   LINE      ECODE    ---       #456
      00FE98BFH   LINE      ECODE    ---       #457
      00FE98BFH   LINE      ECODE    ---       #458
      00FE98BFH   LINE      ECODE    ---       #459
      00FE98BFH   LINE      ECODE    ---       #460
      ---         BLOCKEND  ---      ---       LVL=0

      00FE98C2H   BLOCK     ECODE    ---       LVL=0
      00010EA8H   SYMBOL    XDATA    ---       now_task_node
      00FE98CEH   BLOCK     ECODE    NEAR LAB  LVL=1
      000109A3H   SYMBOL    XDATA    BYTE      barricade_fun_node
      000109A4H   SYMBOL    XDATA    DWORD     nowtime
      000109A8H   SYMBOL    XDATA    DWORD     lasttime
      00FE98EBH   BLOCK     ECODE    NEAR LAB  LVL=2
      00010EACH   SYMBOL    XDATA    BYTE      flag
      ---         BLOCKEND  ---      ---       LVL=2
      00FE9A15H   BLOCK     ECODE    NEAR LAB  LVL=2
      00010EACH   SYMBOL    XDATA    BYTE      flag
      ---         BLOCKEND  ---      ---       LVL=2
      ---         BLOCKEND  ---      ---       LVL=1
      00FE98C2H   LINE      ECODE    ---       #465
      00FE98CEH   LINE      ECODE    ---       #466
      00FE98CEH   LINE      ECODE    ---       #470
      00FE98EBH   LINE      ECODE    ---       #472
      00FE98EBH   LINE      ECODE    ---       #473
      00FE98EBH   LINE      ECODE    ---       #474
      00FE98F0H   LINE      ECODE    ---       #475
      00FE9904H   LINE      ECODE    ---       #476
      00FE990DH   LINE      ECODE    ---       #477
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 126


      00FE9927H   LINE      ECODE    ---       #478
      00FE9949H   LINE      ECODE    ---       #479
      00FE998BH   LINE      ECODE    ---       #480
      00FE99ADH   LINE      ECODE    ---       #481
      00FE99B3H   LINE      ECODE    ---       #482
      00FE99D9H   LINE      ECODE    ---       #483
      00FE99F8H   LINE      ECODE    ---       #485
      00FE9A0CH   LINE      ECODE    ---       #486
      00FE9A12H   LINE      ECODE    ---       #487
      00FE9A12H   LINE      ECODE    ---       #489
      00FE9A15H   LINE      ECODE    ---       #490
      00FE9A15H   LINE      ECODE    ---       #491
      00FE9A15H   LINE      ECODE    ---       #492
      00FE9A1AH   LINE      ECODE    ---       #493
      00FE9A2EH   LINE      ECODE    ---       #494
      00FE9A37H   LINE      ECODE    ---       #495
      00FE9A51H   LINE      ECODE    ---       #496
      00FE9A61H   LINE      ECODE    ---       #497
      00FE9A6DH   LINE      ECODE    ---       #498
      00FE9A93H   LINE      ECODE    ---       #499
      00FE9AAFH   LINE      ECODE    ---       #501
      00FE9AC3H   LINE      ECODE    ---       #502
      00FE9AC9H   LINE      ECODE    ---       #503
      00FE9AC9H   LINE      ECODE    ---       #505
      00FE9ACBH   LINE      ECODE    ---       #506
      00FE9ACBH   LINE      ECODE    ---       #508
      00FE9AD1H   LINE      ECODE    ---       #510
      00FE9AD3H   LINE      ECODE    ---       #511
      00FE9AD3H   LINE      ECODE    ---       #513
      00FE9AE2H   LINE      ECODE    ---       #514
      00FE9AE8H   LINE      ECODE    ---       #515
      00FE9AECH   LINE      ECODE    ---       #517
      00FE9AECH   LINE      ECODE    ---       #518
      00FE9AECH   LINE      ECODE    ---       #519
      00FE9AECH   LINE      ECODE    ---       #520
      00FE9AECH   LINE      ECODE    ---       #521
      ---         BLOCKEND  ---      ---       LVL=0

      00FE9AEFH   BLOCK     ECODE    ---       LVL=0
      00010F13H   SYMBOL    XDATA    ---       now_task_node
      00FE9AF9H   BLOCK     ECODE    NEAR LAB  LVL=1
      000109ACH   SYMBOL    XDATA    BYTE      turn_fun_node
      000109ADH   SYMBOL    XDATA    DWORD     turn_time
      000109B1H   SYMBOL    XDATA    BYTE      turn_time_lock
      000109B2H   SYMBOL    XDATA    FLOAT     turn_speed
      000109B6H   SYMBOL    XDATA    FLOAT     angle_error_temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FE9AEFH   LINE      ECODE    ---       #613
      00FE9AF9H   LINE      ECODE    ---       #614
      00FE9AF9H   LINE      ECODE    ---       #621
      00FE9B10H   LINE      ECODE    ---       #623
      00FE9B10H   LINE      ECODE    ---       #625
      00FE9B18H   LINE      ECODE    ---       #627
      00FE9B37H   LINE      ECODE    ---       #629
      00FE9B40H   LINE      ECODE    ---       #631
      00FE9B54H   LINE      ECODE    ---       #632
      00FE9B5AH   LINE      ECODE    ---       #633
      00FE9B6EH   LINE      ECODE    ---       #634
      00FE9B6EH   LINE      ECODE    ---       #636
      00FE9B78H   LINE      ECODE    ---       #637
      00FE9B82H   LINE      ECODE    ---       #640
      00FE9BA4H   LINE      ECODE    ---       #641
      00FE9BC6H   LINE      ECODE    ---       #642
      00FE9BE4H   LINE      ECODE    ---       #643
      00FE9BEAH   LINE      ECODE    ---       #644
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 127


      00FE9C0EH   LINE      ECODE    ---       #647
      00FE9C49H   LINE      ECODE    ---       #649
      00FE9C4FH   LINE      ECODE    ---       #650
      00FE9C52H   LINE      ECODE    ---       #655
      00FE9C58H   LINE      ECODE    ---       #656
      00FE9C58H   LINE      ECODE    ---       #658
      00FE9C5BH   LINE      ECODE    ---       #659
      00FE9C5BH   LINE      ECODE    ---       #661
      00FE9C63H   LINE      ECODE    ---       #663
      00FE9C73H   LINE      ECODE    ---       #664
      00FE9C95H   LINE      ECODE    ---       #665
      00FE9CABH   LINE      ECODE    ---       #666
      00FE9CC6H   LINE      ECODE    ---       #667
      00FE9CDCH   LINE      ECODE    ---       #668
      00FE9CF7H   LINE      ECODE    ---       #669
      00FE9D23H   LINE      ECODE    ---       #671
      00FE9D29H   LINE      ECODE    ---       #672
      00FE9D29H   LINE      ECODE    ---       #673
      00FE9D58H   LINE      ECODE    ---       #675
      00FE9D6EH   LINE      ECODE    ---       #677
      00FE9D78H   LINE      ECODE    ---       #678
      00FE9D82H   LINE      ECODE    ---       #679
      00FE9D8EH   LINE      ECODE    ---       #680
      00FE9D9AH   LINE      ECODE    ---       #681
      00FE9D9AH   LINE      ECODE    ---       #682
      00FE9DB3H   LINE      ECODE    ---       #684
      00FE9DBDH   LINE      ECODE    ---       #685
      00FE9DC7H   LINE      ECODE    ---       #686
      00FE9DD3H   LINE      ECODE    ---       #687
      00FE9DDFH   LINE      ECODE    ---       #688
      00FE9DE2H   LINE      ECODE    ---       #692
      00FE9E04H   LINE      ECODE    ---       #693
      00FE9E26H   LINE      ECODE    ---       #694
      00FE9E48H   LINE      ECODE    ---       #695
      00FE9E4EH   LINE      ECODE    ---       #696
      00FE9E74H   LINE      ECODE    ---       #697
      00FE9E74H   LINE      ECODE    ---       #699
      00FE9E76H   LINE      ECODE    ---       #700
      00FE9E76H   LINE      ECODE    ---       #703
      00FE9E7EH   LINE      ECODE    ---       #704
      00FE9E8DH   LINE      ECODE    ---       #705
      00FE9E93H   LINE      ECODE    ---       #706
      00FE9E97H   LINE      ECODE    ---       #708
      00FE9E97H   LINE      ECODE    ---       #709
      00FE9E97H   LINE      ECODE    ---       #710
      00FE9E97H   LINE      ECODE    ---       #711
      00FE9E97H   LINE      ECODE    ---       #712
      ---         BLOCKEND  ---      ---       LVL=0

      00FE9E98H   BLOCK     ECODE    ---       LVL=0
      00FE9E98H   LINE      ECODE    ---       #724
      00FE9E98H   LINE      ECODE    ---       #726
      00FE9E9DH   LINE      ECODE    ---       #727
      00FE9EB1H   LINE      ECODE    ---       #728
      00FE9EB5H   LINE      ECODE    ---       #729
      00FE9EB9H   LINE      ECODE    ---       #730
      ---         BLOCKEND  ---      ---       LVL=0

      00FE9EBAH   BLOCK     ECODE    ---       LVL=0
      00010F49H   SYMBOL    XDATA    BYTE      now_task_node
      00FE9EBAH   LINE      ECODE    ---       #737
      00FE9EBEH   LINE      ECODE    ---       #739
      00FE9EEBH   LINE      ECODE    ---       #741
      00FE9EEBH   LINE      ECODE    ---       #742
      00FE9EEFH   LINE      ECODE    ---       #743
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 128


      00FE9EF1H   LINE      ECODE    ---       #744
      00FE9EF1H   LINE      ECODE    ---       #745
      00FE9EFDH   LINE      ECODE    ---       #746
      00FE9EFFH   LINE      ECODE    ---       #747
      00FE9EFFH   LINE      ECODE    ---       #748
      00FE9F0BH   LINE      ECODE    ---       #749
      00FE9F0DH   LINE      ECODE    ---       #750
      00FE9F0DH   LINE      ECODE    ---       #751
      00FE9F19H   LINE      ECODE    ---       #752
      00FE9F1BH   LINE      ECODE    ---       #753
      00FE9F1BH   LINE      ECODE    ---       #754
      00FE9F27H   LINE      ECODE    ---       #755
      00FE9F29H   LINE      ECODE    ---       #756
      00FE9F29H   LINE      ECODE    ---       #757
      00FE9F35H   LINE      ECODE    ---       #758
      00FE9F37H   LINE      ECODE    ---       #759
      00FE9F37H   LINE      ECODE    ---       #760
      00FE9F43H   LINE      ECODE    ---       #761
      00FE9F45H   LINE      ECODE    ---       #762
      00FE9F45H   LINE      ECODE    ---       #763
      00FE9F49H   LINE      ECODE    ---       #764
      00FE9F49H   LINE      ECODE    ---       #765
      00FE9F49H   LINE      ECODE    ---       #766
      00FE9F49H   LINE      ECODE    ---       #767
      00FE9F49H   LINE      ECODE    ---       #769
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ccd_processing
      00FEEC08H   PUBLIC    ECODE    ---       ccd_edge_detect?
      00FEEF7BH   PUBLIC    ECODE    ---       ccd_calc_position_error?
      00FEEB07H   PUBLIC    ECODE    ---       ccd_data_filter?
      00FEE7BBH   PUBLIC    ECODE    ---       set_pit_timer_ms?
      00FEEE4EH   PUBLIC    ECODE    ---       ccd_is_line_valid?
      00FEF0AAH   PUBLIC    ECODE    ---       ccd_calc_position_error_simple?
      0001042AH   PUBLIC    XDATA    ---       edges
      00010434H   PUBLIC    XDATA    INT       line_position_error
      00010436H   PUBLIC    XDATA    BYTE      edge_count
      00010437H   PUBLIC    XDATA    BYTE      timer_count
      00010438H   PUBLIC    XDATA    ---       ccd_filtered_data
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000D3H   SFRSYM    DATA     BYTE      T4L
      000000D5H   SFRSYM    DATA     BYTE      T3L
      000000D7H   SFRSYM    DATA     BYTE      T2L
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000D2H   SFRSYM    DATA     BYTE      T4H
      000000D4H   SFRSYM    DATA     BYTE      T3H
      000000D6H   SFRSYM    DATA     BYTE      T2H
      0000008DH   SFRSYM    DATA     BYTE      TH1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 129


      0000008EH   SFRSYM    DATA     BYTE      AUXR
      000000AFH   SFRSYM    DATA     BYTE      IE2
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FEEB19H   SYMBOL    ECODE    ---       calculate_dynamic_threshold
      00FEE8A7H   SYMBOL    ECODE    ---       moving_average_filter
      00FEE9FAH   SYMBOL    ECODE    ---       median_filter

      00FEE7BBH   BLOCK     ECODE    ---       LVL=0
      00010E8DH   SYMBOL    XDATA    INT       tim_n
      00010E8FH   SYMBOL    XDATA    WORD      time_ms
      00FEE7C7H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E91H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE7BBH   LINE      ECODE    ---       #18
      00FEE7C7H   LINE      ECODE    ---       #19
      00FEE7C7H   LINE      ECODE    ---       #21
      00FEE7F5H   LINE      ECODE    ---       #23
      00FEE801H   LINE      ECODE    ---       #25
      00FEE809H   LINE      ECODE    ---       #26
      00FEE815H   LINE      ECODE    ---       #27
      00FEE818H   LINE      ECODE    ---       #28
      00FEE826H   LINE      ECODE    ---       #30
      00FEE82EH   LINE      ECODE    ---       #31
      00FEE83AH   LINE      ECODE    ---       #32
      00FEE83CH   LINE      ECODE    ---       #33
      00FEE84AH   LINE      ECODE    ---       #35
      00FEE852H   LINE      ECODE    ---       #36
      00FEE85EH   LINE      ECODE    ---       #37
      00FEE860H   LINE      ECODE    ---       #38
      00FEE86EH   LINE      ECODE    ---       #40
      00FEE876H   LINE      ECODE    ---       #41
      00FEE882H   LINE      ECODE    ---       #42
      00FEE884H   LINE      ECODE    ---       #43
      00FEE892H   LINE      ECODE    ---       #45
      00FEE89AH   LINE      ECODE    ---       #46
      00FEE8A6H   LINE      ECODE    ---       #47
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE8A7H   BLOCK     ECODE    ---       LVL=0
      00010CFEH   SYMBOL    XDATA    ---       input
      00010D02H   SYMBOL    XDATA    ---       output
      00010D06H   SYMBOL    XDATA    BYTE      data_size
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 130


      00FEE8BFH   BLOCK     ECODE    NEAR LAB  LVL=1
      00010D07H   SYMBOL    XDATA    BYTE      i
      00010D08H   SYMBOL    XDATA    BYTE      j
      00010D09H   SYMBOL    XDATA    WORD      sum
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE8A7H   LINE      ECODE    ---       #56
      00FEE8BFH   LINE      ECODE    ---       #60
      00FEE8C8H   LINE      ECODE    ---       #61
      00FEE8D0H   LINE      ECODE    ---       #62
      00FEE8D7H   LINE      ECODE    ---       #63
      00FEE903H   LINE      ECODE    ---       #64
      00FEE91AH   LINE      ECODE    ---       #65
      00FEE93FH   LINE      ECODE    ---       #66
      00FEE963H   LINE      ECODE    ---       #69
      00FEE96BH   LINE      ECODE    ---       #70
      00FEE99DH   LINE      ECODE    ---       #71
      00FEE9DFH   LINE      ECODE    ---       #72
      00FEE9F9H   LINE      ECODE    ---       #73
      ---         BLOCKEND  ---      ---       LVL=0

      00FEE9FAH   BLOCK     ECODE    ---       LVL=0
      00010E42H   SYMBOL    XDATA    ---       window
      00010E46H   SYMBOL    XDATA    BYTE      data_size
      00FEEA08H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010E47H   SYMBOL    XDATA    BYTE      i
      00010E48H   SYMBOL    XDATA    BYTE      j
      00010E49H   SYMBOL    XDATA    BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      00FEE9FAH   LINE      ECODE    ---       #76
      00FEEA08H   LINE      ECODE    ---       #80
      00FEEA10H   LINE      ECODE    ---       #81
      00FEEA23H   LINE      ECODE    ---       #82
      00FEEA51H   LINE      ECODE    ---       #83
      00FEEA6AH   LINE      ECODE    ---       #84
      00FEEA94H   LINE      ECODE    ---       #85
      00FEEAAFH   LINE      ECODE    ---       #86
      00FEEACBH   LINE      ECODE    ---       #88
      00FEEAEFH   LINE      ECODE    ---       #90
      00FEEB06H   LINE      ECODE    ---       #91
      ---         BLOCKEND  ---      ---       LVL=0

      00FEEB07H   BLOCK     ECODE    ---       LVL=0
      00FEEB07H   LINE      ECODE    ---       #103
      00FEEB07H   LINE      ECODE    ---       #108
      00FEEB18H   LINE      ECODE    ---       #117
      ---         BLOCKEND  ---      ---       LVL=0

      00FEEB19H   BLOCK     ECODE    ---       LVL=0
      00010EADH   SYMBOL    XDATA    WORD      max_val
      00010EAFH   SYMBOL    XDATA    WORD      min_val
      00010EB1H   SYMBOL    XDATA    BYTE      i
      00FEEB19H   LINE      ECODE    ---       #121
      00FEEB19H   LINE      ECODE    ---       #122
      00FEEB21H   LINE      ECODE    ---       #123
      00FEEB2BH   LINE      ECODE    ---       #126
      00FEEB34H   LINE      ECODE    ---       #127
      00FEEB76H   LINE      ECODE    ---       #128
      00FEEBB8H   LINE      ECODE    ---       #129
      00FEEBD2H   LINE      ECODE    ---       #130
      00FEEC07H   LINE      ECODE    ---       #131
      ---         BLOCKEND  ---      ---       LVL=0

      00FEEC08H   BLOCK     ECODE    ---       LVL=0
      00010EB2H   SYMBOL    XDATA    BYTE      i
      00010EB3H   SYMBOL    XDATA    BYTE      in_edge
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 131


      00010EB4H   SYMBOL    XDATA    BYTE      current_edge
      00010EB5H   SYMBOL    XDATA    WORD      dynamic_threshold
      00FEEC08H   LINE      ECODE    ---       #134
      00FEEC08H   LINE      ECODE    ---       #136
      00FEEC0DH   LINE      ECODE    ---       #137
      00FEEC12H   LINE      ECODE    ---       #138
      00FEEC1BH   LINE      ECODE    ---       #140
      00FEEC20H   LINE      ECODE    ---       #142
      00FEEC29H   LINE      ECODE    ---       #144
      00FEEC7BH   LINE      ECODE    ---       #147
      00FEEC9AH   LINE      ECODE    ---       #148
      00FEECCCH   LINE      ECODE    ---       #149
      00FEECD2H   LINE      ECODE    ---       #150
      00FEECD2H   LINE      ECODE    ---       #153
      00FEED2AH   LINE      ECODE    ---       #156
      00FEED4FH   LINE      ECODE    ---       #157
      00FEED9CH   LINE      ECODE    ---       #158
      00FEEDA1H   LINE      ECODE    ---       #159
      00FEEDACH   LINE      ECODE    ---       #160
      00FEEDB7H   LINE      ECODE    ---       #161
      00FEEDB7H   LINE      ECODE    ---       #164
      00FEEDF6H   LINE      ECODE    ---       #165
      00FEEE28H   LINE      ECODE    ---       #166
      00FEEE4DH   LINE      ECODE    ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

      00FEEE4EH   BLOCK     ECODE    ---       LVL=0
      00010DE9H   SYMBOL    XDATA    BYTE      continuous_count
      00010DEAH   SYMBOL    XDATA    BYTE      max_continuous
      00010DEBH   SYMBOL    XDATA    WORD      left_contrast
      00010DEDH   SYMBOL    XDATA    WORD      right_contrast
      00010DEFH   SYMBOL    XDATA    BYTE      i
      00010DF0H   SYMBOL    XDATA    WORD      dynamic_threshold
      00FEEE4EH   LINE      ECODE    ---       #171
      00FEEE4EH   LINE      ECODE    ---       #172
      00FEEE53H   LINE      ECODE    ---       #173
      00FEEE58H   LINE      ECODE    ---       #177
      00FEEE61H   LINE      ECODE    ---       #178
      00FEEE6EH   LINE      ECODE    ---       #180
      00FEEE86H   LINE      ECODE    ---       #181
      00FEEE8AH   LINE      ECODE    ---       #182
      00FEEE8AH   LINE      ECODE    ---       #187
      00FEEEB1H   LINE      ECODE    ---       #188
      00FEEED8H   LINE      ECODE    ---       #189
      00FEEEF4H   LINE      ECODE    ---       #190
      00FEEEF8H   LINE      ECODE    ---       #191
      00FEEEF8H   LINE      ECODE    ---       #197
      00FEEF02H   LINE      ECODE    ---       #198
      00FEEF25H   LINE      ECODE    ---       #199
      00FEEF30H   LINE      ECODE    ---       #200
      00FEEF3EH   LINE      ECODE    ---       #201
      00FEEF46H   LINE      ECODE    ---       #202
      00FEEF48H   LINE      ECODE    ---       #204
      00FEEF4DH   LINE      ECODE    ---       #205
      00FEEF4DH   LINE      ECODE    ---       #206
      00FEEF66H   LINE      ECODE    ---       #209
      00FEEF72H   LINE      ECODE    ---       #210
      00FEEF75H   LINE      ECODE    ---       #211
      00FEEF75H   LINE      ECODE    ---       #213
      00FEEF79H   LINE      ECODE    ---       #214
      00FEEF79H   LINE      ECODE    ---       #215
      00FEEF7AH   LINE      ECODE    ---       #216
      ---         BLOCKEND  ---      ---       LVL=0

      00FEEF7BH   BLOCK     ECODE    ---       LVL=0
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 132


      00010D0BH   SYMBOL    XDATA    LONG      sum
      00010D0FH   SYMBOL    XDATA    LONG      weight_sum
      00010D13H   SYMBOL    XDATA    BYTE      i
      00010D14H   SYMBOL    XDATA    INT       weight
      00010D16H   SYMBOL    XDATA    INT       center
      00FEEF7BH   LINE      ECODE    ---       #219
      00FEEF7BH   LINE      ECODE    ---       #220
      00FEEF87H   LINE      ECODE    ---       #221
      00FEEF93H   LINE      ECODE    ---       #227
      00FEEF9CH   LINE      ECODE    ---       #228
      00FEEFA1H   LINE      ECODE    ---       #229
      00FEEFA1H   LINE      ECODE    ---       #232
      00FEEFACH   LINE      ECODE    ---       #234
      00FEEFD1H   LINE      ECODE    ---       #235
      00FEEFDDH   LINE      ECODE    ---       #236
      00FEF00EH   LINE      ECODE    ---       #237
      00FEF02BH   LINE      ECODE    ---       #238
      00FEF047H   LINE      ECODE    ---       #241
      00FEF055H   LINE      ECODE    ---       #242
      00FEF073H   LINE      ECODE    ---       #243
      00FEF075H   LINE      ECODE    ---       #244
      00FEF097H   LINE      ECODE    ---       #245
      00FEF097H   LINE      ECODE    ---       #247
      00FEF0A3H   LINE      ECODE    ---       #248
      00FEF0A9H   LINE      ECODE    ---       #249
      ---         BLOCKEND  ---      ---       LVL=0

      00FEF0AAH   BLOCK     ECODE    ---       LVL=0
      00010F37H   SYMBOL    XDATA    INT       center
      00FEF0AAH   LINE      ECODE    ---       #253
      00FEF0AAH   LINE      ECODE    ---       #257
      00FEF0CCH   LINE      ECODE    ---       #259
      00FEF0D8H   LINE      ECODE    ---       #260
      00FEF0DEH   LINE      ECODE    ---       #261
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       menu
      00FE0C00H   PUBLIC    ECODE    ---       menu_display?
      00FE144AH   PUBLIC    ECODE    ---       function_page_fun?
      00FE17DBH   PUBLIC    ECODE    ---       parameter_adjustment_page_fun?
      00FE161EH   PUBLIC    ECODE    ---       clear_eeprom_page_fun?
      00FE2253H   PUBLIC    ECODE    ---       path_display_page_fun?
      00FE1213H   PUBLIC    ECODE    ---       state_slect_page_fun?
      00FE1EF6H   PUBLIC    ECODE    ---       common_para_adj_page_fun?
      00FE164BH   PUBLIC    ECODE    ---       ccd_page_fun?
      00FE15A7H   PUBLIC    ECODE    ---       start_page_fun?
      00FE1192H   PUBLIC    ECODE    ---       take_point_page_fun?
      00010C21H   PUBLIC    XDATA    BYTE      start_step
      000000D0H   SFRSYM    DATA     BYTE      PSW
      0000009DH   SFRSYM    DATA     BYTE      IRCBAND
      00000097H   SFRSYM    DATA     BYTE      AUXR2
      000000EFH   SFRSYM    DATA     BYTE      AUXINTIF
      000000FDH   SFRSYM    DATA     BYTE      S4CON
      000000ACH   SFRSYM    DATA     BYTE      S3CON
      0000009AH   SFRSYM    DATA     BYTE      S2CON
      000000B7H   SFRSYM    DATA     BYTE      IPH
      000000CDH   SFRSYM    DATA     BYTE      SPSTAT
      000000DDH   SFRSYM    DATA     BYTE      T4T3M
      000000C1H   SFRSYM    DATA     BYTE      WDT_CONTR
      000000DFH   SFRSYM    DATA     BYTE      IP3
      000000F4H   SFRSYM    DATA     BYTE      USBCON
      000000B5H   SFRSYM    DATA     BYTE      IP2
      000000FFH   SFRSYM    DATA     BYTE      RSTCFG
      000000F9H   SFRSYM    DATA     BYTE      LINICR
      0000008EH   SFRSYM    DATA     BYTE      AUXR
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 133


      000000AFH   SFRSYM    DATA     BYTE      IE2
      000000F8H.6 SFRSYM    DATA     BIT       P76
      000000F8H.5 SFRSYM    DATA     BIT       P75
      00000088H   SFRSYM    DATA     BYTE      TCON
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000F1H   SFRSYM    DATA     BYTE      CANICR
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000BCH   SFRSYM    DATA     BYTE      ADC_CONTR
      000000E7H   SFRSYM    DATA     BYTE      CMPCR2
      000000E6H   SFRSYM    DATA     BYTE      CMPCR1
      000000B8H   SFRSYM    DATA     BYTE      IP
      0000008FH   SFRSYM    DATA     BYTE      INTCLKO
      000000EEH   SFRSYM    DATA     BYTE      IP3H
      000000B6H   SFRSYM    DATA     BYTE      IP2H
      000000A8H   SFRSYM    DATA     BYTE      IE
      000000BBH   SFRSYM    DATA     BYTE      P_SW3
      000000BAH   SFRSYM    DATA     BYTE      P_SW2
      000000A2H   SFRSYM    DATA     BYTE      P_SW1
      000000CEH   SFRSYM    DATA     BYTE      SPCTL
      000000F8H   SFRSYM    DATA     BYTE      P7
      000000E8H   SFRSYM    DATA     BYTE      P6
      000000C8H   SFRSYM    DATA     BYTE      P5
      000000C0H   SFRSYM    DATA     BYTE      P4
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000A0H   SFRSYM    DATA     BYTE      P2
      00000090H   SFRSYM    DATA     BYTE      P1
      00000080H   SFRSYM    DATA     BYTE      P0
      00FE2038H   SYMBOL    ECODE    ---       draw_marker
      00010C1CH   SYMBOL    XDATA    BYTE      current_point_index
      00010C1DH   SYMBOL    XDATA    FLOAT     step
      00010C22H   SYMBOL    XDATA    BYTE      now_page_node

      00FE0C00H   BLOCK     ECODE    ---       LVL=0
      00FE0C00H   LINE      ECODE    ---       #27
      00FE0C00H   LINE      ECODE    ---       #30
      00FE0C1AH   LINE      ECODE    ---       #32
      00FE0C1AH   LINE      ECODE    ---       #33
      00FE0C1EH   LINE      ECODE    ---       #34
      00FE0C20H   LINE      ECODE    ---       #35
      00FE0C20H   LINE      ECODE    ---       #36
      00FE0C24H   LINE      ECODE    ---       #37
      00FE0C26H   LINE      ECODE    ---       #38
      00FE0C26H   LINE      ECODE    ---       #39
      00FE0C2AH   LINE      ECODE    ---       #40
      00FE0C2CH   LINE      ECODE    ---       #41
      00FE0C2CH   LINE      ECODE    ---       #42
      00FE0C30H   LINE      ECODE    ---       #43
      00FE0C32H   LINE      ECODE    ---       #44
      00FE0C32H   LINE      ECODE    ---       #45
      00FE0C36H   LINE      ECODE    ---       #46
      00FE0C38H   LINE      ECODE    ---       #47
      00FE0C38H   LINE      ECODE    ---       #48
      00FE0C3CH   LINE      ECODE    ---       #49
      00FE0C3EH   LINE      ECODE    ---       #50
      00FE0C3EH   LINE      ECODE    ---       #51
      00FE0C42H   LINE      ECODE    ---       #52
      00FE0C42H   LINE      ECODE    ---       #53
      00FE0C42H   LINE      ECODE    ---       #55
      00FE0C5AH   LINE      ECODE    ---       #57
      00FE0C8DH   LINE      ECODE    ---       #58
      00FE0CA1H   LINE      ECODE    ---       #59
      00FE0CB3H   LINE      ECODE    ---       #60
      00FE0CE8H   LINE      ECODE    ---       #61
      00FE0CFAH   LINE      ECODE    ---       #62
      00FE0D2FH   LINE      ECODE    ---       #63
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 134


      00FE0D41H   LINE      ECODE    ---       #64
      00FE0D72H   LINE      ECODE    ---       #65
      00FE0D84H   LINE      ECODE    ---       #66
      00FE0DB5H   LINE      ECODE    ---       #67
      00FE0DC7H   LINE      ECODE    ---       #68
      00FE0DF8H   LINE      ECODE    ---       #69
      00FE0E0AH   LINE      ECODE    ---       #70
      00FE0E3BH   LINE      ECODE    ---       #71
      00FE0E4DH   LINE      ECODE    ---       #72
      00FE0E7EH   LINE      ECODE    ---       #73
      00FE0E90H   LINE      ECODE    ---       #74
      00FE0EA8H   LINE      ECODE    ---       #75
      00FE0EBAH   LINE      ECODE    ---       #76
      00FE0EEBH   LINE      ECODE    ---       #77
      00FE0EFDH   LINE      ECODE    ---       #78
      00FE0F2EH   LINE      ECODE    ---       #79
      00FE0F40H   LINE      ECODE    ---       #80
      00FE0F56H   LINE      ECODE    ---       #81
      00FE0F6AH   LINE      ECODE    ---       #82
      00FE0F9FH   LINE      ECODE    ---       #83
      00FE0FB7H   LINE      ECODE    ---       #84
      00FE0FCFH   LINE      ECODE    ---       #85
      00FE0FE7H   LINE      ECODE    ---       #86
      00FE0FFFH   LINE      ECODE    ---       #87
      00FE1017H   LINE      ECODE    ---       #88
      00FE102FH   LINE      ECODE    ---       #89
      00FE1047H   LINE      ECODE    ---       #90
      00FE105DH   LINE      ECODE    ---       #91
      00FE1090H   LINE      ECODE    ---       #92
      00FE10C5H   LINE      ECODE    ---       #93
      00FE10F8H   LINE      ECODE    ---       #94
      00FE112DH   LINE      ECODE    ---       #95
      00FE1160H   LINE      ECODE    ---       #96
      00FE1191H   LINE      ECODE    ---       #97
      ---         BLOCKEND  ---      ---       LVL=0

      00FE1192H   BLOCK     ECODE    ---       LVL=0
      00FE1192H   LINE      ECODE    ---       #101
      00FE1192H   LINE      ECODE    ---       #103
      00FE11A2H   LINE      ECODE    ---       #105
      00FE11DCH   LINE      ECODE    ---       #106
      00FE11E0H   LINE      ECODE    ---       #107
      00FE11E6H   LINE      ECODE    ---       #108
      00FE11E6H   LINE      ECODE    ---       #109
      00FE11F6H   LINE      ECODE    ---       #111
      00FE11FCH   LINE      ECODE    ---       #112
      00FE1200H   LINE      ECODE    ---       #113
      00FE1200H   LINE      ECODE    ---       #115
      00FE1212H   LINE      ECODE    ---       #117
      ---         BLOCKEND  ---      ---       LVL=0

      00FE1213H   BLOCK     ECODE    ---       LVL=0
      00010C23H   SYMBOL    XDATA    BYTE      point_state_slect
      00FE1213H   LINE      ECODE    ---       #119
      00FE1213H   LINE      ECODE    ---       #120
      00FE1213H   LINE      ECODE    ---       #123
      00FE1223H   LINE      ECODE    ---       #125
      00FE122EH   LINE      ECODE    ---       #126
      00FE123AH   LINE      ECODE    ---       #128
      00FE1240H   LINE      ECODE    ---       #129
      00FE1240H   LINE      ECODE    ---       #130
      00FE1244H   LINE      ECODE    ---       #131
      00FE1244H   LINE      ECODE    ---       #132
      00FE1254H   LINE      ECODE    ---       #134
      00FE1262H   LINE      ECODE    ---       #135
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 135


      00FE126EH   LINE      ECODE    ---       #137
      00FE1274H   LINE      ECODE    ---       #138
      00FE1274H   LINE      ECODE    ---       #139
      00FE1278H   LINE      ECODE    ---       #140
      00FE1278H   LINE      ECODE    ---       #142
      00FE128BH   LINE      ECODE    ---       #144
      00FE128FH   LINE      ECODE    ---       #145
      00FE129AH   LINE      ECODE    ---       #146
      00FE12C7H   LINE      ECODE    ---       #148
      00FE12C7H   LINE      ECODE    ---       #150
      00FE12CFH   LINE      ECODE    ---       #151
      00FE12D4H   LINE      ECODE    ---       #153
      00FE12D6H   LINE      ECODE    ---       #154
      00FE12D6H   LINE      ECODE    ---       #155
      00FE12DEH   LINE      ECODE    ---       #156
      00FE12E3H   LINE      ECODE    ---       #157
      00FE12E5H   LINE      ECODE    ---       #158
      00FE12E5H   LINE      ECODE    ---       #159
      00FE12EDH   LINE      ECODE    ---       #160
      00FE12F2H   LINE      ECODE    ---       #161
      00FE12F4H   LINE      ECODE    ---       #162
      00FE12F4H   LINE      ECODE    ---       #163
      00FE12FCH   LINE      ECODE    ---       #164
      00FE1301H   LINE      ECODE    ---       #165
      00FE1303H   LINE      ECODE    ---       #166
      00FE1303H   LINE      ECODE    ---       #167
      00FE130BH   LINE      ECODE    ---       #168
      00FE1310H   LINE      ECODE    ---       #169
      00FE1312H   LINE      ECODE    ---       #170
      00FE1312H   LINE      ECODE    ---       #171
      00FE131AH   LINE      ECODE    ---       #172
      00FE131FH   LINE      ECODE    ---       #173
      00FE1321H   LINE      ECODE    ---       #174
      00FE1321H   LINE      ECODE    ---       #176
      00FE1329H   LINE      ECODE    ---       #177
      00FE132EH   LINE      ECODE    ---       #179
      00FE1330H   LINE      ECODE    ---       #180
      00FE1330H   LINE      ECODE    ---       #182
      00FE1338H   LINE      ECODE    ---       #183
      00FE133EH   LINE      ECODE    ---       #185
      00FE133EH   LINE      ECODE    ---       #186
      00FE133EH   LINE      ECODE    ---       #187
      00FE133EH   LINE      ECODE    ---       #195
      00FE1350H   LINE      ECODE    ---       #196
      00FE137DH   LINE      ECODE    ---       #198
      00FE137DH   LINE      ECODE    ---       #199
      00FE138FH   LINE      ECODE    ---       #200
      00FE1392H   LINE      ECODE    ---       #201
      00FE1392H   LINE      ECODE    ---       #202
      00FE13A4H   LINE      ECODE    ---       #203
      00FE13A6H   LINE      ECODE    ---       #204
      00FE13A6H   LINE      ECODE    ---       #206
      00FE13B8H   LINE      ECODE    ---       #207
      00FE13BAH   LINE      ECODE    ---       #208
      00FE13BAH   LINE      ECODE    ---       #210
      00FE13CCH   LINE      ECODE    ---       #211
      00FE13CEH   LINE      ECODE    ---       #212
      00FE13CEH   LINE      ECODE    ---       #214
      00FE13E0H   LINE      ECODE    ---       #215
      00FE13E2H   LINE      ECODE    ---       #216
      00FE13E2H   LINE      ECODE    ---       #218
      00FE13F4H   LINE      ECODE    ---       #219
      00FE13F6H   LINE      ECODE    ---       #220
      00FE13F6H   LINE      ECODE    ---       #221
      00FE1408H   LINE      ECODE    ---       #222
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 136


      00FE140AH   LINE      ECODE    ---       #223
      00FE140AH   LINE      ECODE    ---       #224
      00FE141CH   LINE      ECODE    ---       #225
      00FE141CH   LINE      ECODE    ---       #226
      00FE141CH   LINE      ECODE    ---       #228
      00FE1437H   LINE      ECODE    ---       #230
      00FE1449H   LINE      ECODE    ---       #231
      ---         BLOCKEND  ---      ---       LVL=0

      00FE144AH   BLOCK     ECODE    ---       LVL=0
      00010C24H   SYMBOL    XDATA    BYTE      second_page
      00FE144AH   LINE      ECODE    ---       #234
      00FE144AH   LINE      ECODE    ---       #235
      00FE144AH   LINE      ECODE    ---       #237
      00FE1453H   LINE      ECODE    ---       #239
      00FE1459H   LINE      ECODE    ---       #240
      00FE1459H   LINE      ECODE    ---       #242
      00FE1469H   LINE      ECODE    ---       #244
      00FE1474H   LINE      ECODE    ---       #245
      00FE1480H   LINE      ECODE    ---       #247
      00FE1486H   LINE      ECODE    ---       #248
      00FE1486H   LINE      ECODE    ---       #249
      00FE148AH   LINE      ECODE    ---       #250
      00FE148AH   LINE      ECODE    ---       #251
      00FE149AH   LINE      ECODE    ---       #253
      00FE14A8H   LINE      ECODE    ---       #254
      00FE14B4H   LINE      ECODE    ---       #256
      00FE14BAH   LINE      ECODE    ---       #257
      00FE14BAH   LINE      ECODE    ---       #258
      00FE14BEH   LINE      ECODE    ---       #259
      00FE14BEH   LINE      ECODE    ---       #262
      00FE14DFH   LINE      ECODE    ---       #264
      00FE14DFH   LINE      ECODE    ---       #266
      00FE14E3H   LINE      ECODE    ---       #268
      00FE14E6H   LINE      ECODE    ---       #270
      00FE14E6H   LINE      ECODE    ---       #272
      00FE14F8H   LINE      ECODE    ---       #273
      00FE150BH   LINE      ECODE    ---       #275
      00FE150FH   LINE      ECODE    ---       #276
      00FE1515H   LINE      ECODE    ---       #277
      00FE1515H   LINE      ECODE    ---       #279
      00FE1518H   LINE      ECODE    ---       #280
      00FE1518H   LINE      ECODE    ---       #282
      00FE152AH   LINE      ECODE    ---       #283
      00FE153AH   LINE      ECODE    ---       #285
      00FE153EH   LINE      ECODE    ---       #286
      00FE1544H   LINE      ECODE    ---       #287
      00FE1544H   LINE      ECODE    ---       #289
      00FE1546H   LINE      ECODE    ---       #290
      00FE1546H   LINE      ECODE    ---       #292
      00FE1558H   LINE      ECODE    ---       #293
      00FE1568H   LINE      ECODE    ---       #295
      00FE156CH   LINE      ECODE    ---       #296
      00FE1572H   LINE      ECODE    ---       #297
      00FE1572H   LINE      ECODE    ---       #299
      00FE1574H   LINE      ECODE    ---       #300
      00FE1574H   LINE      ECODE    ---       #302
      00FE1586H   LINE      ECODE    ---       #303
      00FE1596H   LINE      ECODE    ---       #305
      00FE159AH   LINE      ECODE    ---       #306
      00FE15A0H   LINE      ECODE    ---       #307
      00FE15A0H   LINE      ECODE    ---       #309
      00FE15A2H   LINE      ECODE    ---       #310
      00FE15A2H   LINE      ECODE    ---       #312
      00FE15A6H   LINE      ECODE    ---       #314
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 137


      00FE15A6H   LINE      ECODE    ---       #315
      00FE15A6H   LINE      ECODE    ---       #316
      ---         BLOCKEND  ---      ---       LVL=0

      00FE15A7H   BLOCK     ECODE    ---       LVL=0
      00FE15A7H   LINE      ECODE    ---       #317
      00FE15A7H   LINE      ECODE    ---       #319
      00FE15B7H   LINE      ECODE    ---       #321
      00FE15C2H   LINE      ECODE    ---       #322
      00FE15C6H   LINE      ECODE    ---       #323
      00FE15C6H   LINE      ECODE    ---       #324
      00FE15CFH   LINE      ECODE    ---       #326
      00FE15D3H   LINE      ECODE    ---       #327
      00FE15D3H   LINE      ECODE    ---       #328
      00FE15DCH   LINE      ECODE    ---       #330
      00FE15E2H   LINE      ECODE    ---       #331
      00FE15E6H   LINE      ECODE    ---       #332
      00FE15EBH   LINE      ECODE    ---       #333
      00FE15F1H   LINE      ECODE    ---       #334
      00FE15F5H   LINE      ECODE    ---       #335
      00FE15F5H   LINE      ECODE    ---       #337
      00FE1607H   LINE      ECODE    ---       #338
      00FE161DH   LINE      ECODE    ---       #341
      ---         BLOCKEND  ---      ---       LVL=0

      00FE161EH   BLOCK     ECODE    ---       LVL=0
      00FE161EH   LINE      ECODE    ---       #345
      00FE161EH   LINE      ECODE    ---       #347
      00FE162EH   LINE      ECODE    ---       #349
      00FE1634H   LINE      ECODE    ---       #350
      00FE1638H   LINE      ECODE    ---       #351
      00FE1638H   LINE      ECODE    ---       #352
      00FE164AH   LINE      ECODE    ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      00FE164BH   BLOCK     ECODE    ---       LVL=0
      00010C25H   SYMBOL    XDATA    BYTE      clear_screen_flag
      00FE164BH   LINE      ECODE    ---       #356
      00FE164BH   LINE      ECODE    ---       #357
      00FE164BH   LINE      ECODE    ---       #359
      00FE1651H   LINE      ECODE    ---       #361
      00FE165AH   LINE      ECODE    ---       #363
      00FE165EH   LINE      ECODE    ---       #364
      00FE1664H   LINE      ECODE    ---       #365
      00FE1664H   LINE      ECODE    ---       #367
      00FE1674H   LINE      ECODE    ---       #369
      00FE167FH   LINE      ECODE    ---       #370
      00FE168DH   LINE      ECODE    ---       #371
      00FE1699H   LINE      ECODE    ---       #372
      00FE169FH   LINE      ECODE    ---       #373
      00FE16A3H   LINE      ECODE    ---       #374
      00FE16A3H   LINE      ECODE    ---       #376
      00FE16B3H   LINE      ECODE    ---       #378
      00FE16C1H   LINE      ECODE    ---       #379
      00FE16CDH   LINE      ECODE    ---       #380
      00FE16D3H   LINE      ECODE    ---       #381
      00FE16E1H   LINE      ECODE    ---       #382
      00FE16E5H   LINE      ECODE    ---       #383
      00FE16E5H   LINE      ECODE    ---       #384
      00FE16F5H   LINE      ECODE    ---       #386
      00FE16FBH   LINE      ECODE    ---       #387
      00FE1700H   LINE      ECODE    ---       #388
      00FE1704H   LINE      ECODE    ---       #389
      00FE1708H   LINE      ECODE    ---       #390
      00FE1708H   LINE      ECODE    ---       #395
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 138


      00FE173CH   LINE      ECODE    ---       #397
      00FE1776H   LINE      ECODE    ---       #398
      00FE1788H   LINE      ECODE    ---       #399
      00FE179EH   LINE      ECODE    ---       #400
      00FE17B0H   LINE      ECODE    ---       #401
      00FE17C8H   LINE      ECODE    ---       #404
      00FE17D1H   LINE      ECODE    ---       #405
      ---         BLOCKEND  ---      ---       LVL=0

      00FE17DBH   BLOCK     ECODE    ---       LVL=0
      00010C26H   SYMBOL    XDATA    BYTE      pid_para_adj_node
      00FE17DBH   LINE      ECODE    ---       #409
      00FE17DBH   LINE      ECODE    ---       #410
      00FE17DBH   LINE      ECODE    ---       #412
      00FE17EDH   LINE      ECODE    ---       #413
      00FE17FDH   LINE      ECODE    ---       #415
      00FE1808H   LINE      ECODE    ---       #416
      00FE1814H   LINE      ECODE    ---       #418
      00FE181AH   LINE      ECODE    ---       #419
      00FE181AH   LINE      ECODE    ---       #420
      00FE1826H   LINE      ECODE    ---       #422
      00FE182CH   LINE      ECODE    ---       #423
      00FE182CH   LINE      ECODE    ---       #424
      00FE1830H   LINE      ECODE    ---       #425
      00FE1830H   LINE      ECODE    ---       #426
      00FE1840H   LINE      ECODE    ---       #428
      00FE1846H   LINE      ECODE    ---       #429
      00FE184AH   LINE      ECODE    ---       #430
      00FE184AH   LINE      ECODE    ---       #431
      00FE187AH   LINE      ECODE    ---       #434
      00FE187AH   LINE      ECODE    ---       #435
      00FE188AH   LINE      ECODE    ---       #437
      00FE18A9H   LINE      ECODE    ---       #438
      00FE18ADH   LINE      ECODE    ---       #439
      00FE18ADH   LINE      ECODE    ---       #440
      00FE18C0H   LINE      ECODE    ---       #442
      00FE18DFH   LINE      ECODE    ---       #443
      00FE18E3H   LINE      ECODE    ---       #444
      00FE18E3H   LINE      ECODE    ---       #445
      00FE18E6H   LINE      ECODE    ---       #446
      00FE18E6H   LINE      ECODE    ---       #447
      00FE18F6H   LINE      ECODE    ---       #449
      00FE1915H   LINE      ECODE    ---       #450
      00FE1919H   LINE      ECODE    ---       #451
      00FE1919H   LINE      ECODE    ---       #452
      00FE192CH   LINE      ECODE    ---       #454
      00FE194BH   LINE      ECODE    ---       #455
      00FE194FH   LINE      ECODE    ---       #456
      00FE194FH   LINE      ECODE    ---       #457
      00FE1952H   LINE      ECODE    ---       #458
      00FE1952H   LINE      ECODE    ---       #459
      00FE1962H   LINE      ECODE    ---       #461
      00FE1981H   LINE      ECODE    ---       #462
      00FE1985H   LINE      ECODE    ---       #463
      00FE1985H   LINE      ECODE    ---       #464
      00FE1998H   LINE      ECODE    ---       #466
      00FE19B7H   LINE      ECODE    ---       #467
      00FE19BBH   LINE      ECODE    ---       #468
      00FE19BBH   LINE      ECODE    ---       #469
      00FE19BEH   LINE      ECODE    ---       #470
      00FE19BEH   LINE      ECODE    ---       #471
      00FE19CEH   LINE      ECODE    ---       #473
      00FE19EDH   LINE      ECODE    ---       #474
      00FE19F1H   LINE      ECODE    ---       #475
      00FE19F1H   LINE      ECODE    ---       #476
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 139


      00FE1A04H   LINE      ECODE    ---       #478
      00FE1A23H   LINE      ECODE    ---       #479
      00FE1A27H   LINE      ECODE    ---       #480
      00FE1A27H   LINE      ECODE    ---       #481
      00FE1A2AH   LINE      ECODE    ---       #482
      00FE1A2AH   LINE      ECODE    ---       #483
      00FE1A3AH   LINE      ECODE    ---       #486
      00FE1A57H   LINE      ECODE    ---       #487
      00FE1A5BH   LINE      ECODE    ---       #488
      00FE1A5BH   LINE      ECODE    ---       #489
      00FE1A6EH   LINE      ECODE    ---       #492
      00FE1A8BH   LINE      ECODE    ---       #493
      00FE1A8FH   LINE      ECODE    ---       #494
      00FE1A8FH   LINE      ECODE    ---       #495
      00FE1A92H   LINE      ECODE    ---       #496
      00FE1A92H   LINE      ECODE    ---       #497
      00FE1AA2H   LINE      ECODE    ---       #499
      00FE1AC1H   LINE      ECODE    ---       #500
      00FE1AC5H   LINE      ECODE    ---       #501
      00FE1AC5H   LINE      ECODE    ---       #502
      00FE1AD8H   LINE      ECODE    ---       #504
      00FE1AF7H   LINE      ECODE    ---       #505
      00FE1AFBH   LINE      ECODE    ---       #506
      00FE1AFBH   LINE      ECODE    ---       #507
      00FE1AFEH   LINE      ECODE    ---       #508
      00FE1AFEH   LINE      ECODE    ---       #509
      00FE1B0EH   LINE      ECODE    ---       #511
      00FE1B39H   LINE      ECODE    ---       #512
      00FE1B3DH   LINE      ECODE    ---       #513
      00FE1B3DH   LINE      ECODE    ---       #514
      00FE1B50H   LINE      ECODE    ---       #516
      00FE1B7BH   LINE      ECODE    ---       #517
      00FE1B7FH   LINE      ECODE    ---       #518
      00FE1B7FH   LINE      ECODE    ---       #519
      00FE1B82H   LINE      ECODE    ---       #520
      00FE1B82H   LINE      ECODE    ---       #521
      00FE1B92H   LINE      ECODE    ---       #523
      00FE1BB1H   LINE      ECODE    ---       #524
      00FE1BB5H   LINE      ECODE    ---       #525
      00FE1BB5H   LINE      ECODE    ---       #526
      00FE1BC8H   LINE      ECODE    ---       #528
      00FE1BE7H   LINE      ECODE    ---       #529
      00FE1BEBH   LINE      ECODE    ---       #530
      00FE1BEBH   LINE      ECODE    ---       #531
      00FE1BEDH   LINE      ECODE    ---       #532
      00FE1BEDH   LINE      ECODE    ---       #533
      00FE1BFDH   LINE      ECODE    ---       #535
      00FE1C1CH   LINE      ECODE    ---       #536
      00FE1C20H   LINE      ECODE    ---       #537
      00FE1C20H   LINE      ECODE    ---       #538
      00FE1C30H   LINE      ECODE    ---       #540
      00FE1C4FH   LINE      ECODE    ---       #541
      00FE1C53H   LINE      ECODE    ---       #542
      00FE1C53H   LINE      ECODE    ---       #543
      00FE1C53H   LINE      ECODE    ---       #544
      00FE1C53H   LINE      ECODE    ---       #545
      00FE1C53H   LINE      ECODE    ---       #546
      00FE1C53H   LINE      ECODE    ---       #547
      00FE1C83H   LINE      ECODE    ---       #549
      00FE1C83H   LINE      ECODE    ---       #550
      00FE1C95H   LINE      ECODE    ---       #551
      00FE1CC6H   LINE      ECODE    ---       #552
      00FE1CC9H   LINE      ECODE    ---       #553
      00FE1CC9H   LINE      ECODE    ---       #554
      00FE1CDBH   LINE      ECODE    ---       #555
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 140


      00FE1D0CH   LINE      ECODE    ---       #556
      00FE1D0FH   LINE      ECODE    ---       #557
      00FE1D0FH   LINE      ECODE    ---       #558
      00FE1D21H   LINE      ECODE    ---       #559
      00FE1D52H   LINE      ECODE    ---       #560
      00FE1D55H   LINE      ECODE    ---       #561
      00FE1D55H   LINE      ECODE    ---       #562
      00FE1D67H   LINE      ECODE    ---       #563
      00FE1D98H   LINE      ECODE    ---       #564
      00FE1D9BH   LINE      ECODE    ---       #565
      00FE1D9BH   LINE      ECODE    ---       #566
      00FE1DADH   LINE      ECODE    ---       #567
      00FE1DDEH   LINE      ECODE    ---       #568
      00FE1DE1H   LINE      ECODE    ---       #569
      00FE1DE1H   LINE      ECODE    ---       #570
      00FE1DF3H   LINE      ECODE    ---       #571
      00FE1E24H   LINE      ECODE    ---       #572
      00FE1E27H   LINE      ECODE    ---       #573
      00FE1E27H   LINE      ECODE    ---       #574
      00FE1E39H   LINE      ECODE    ---       #575
      00FE1E6AH   LINE      ECODE    ---       #576
      00FE1E6DH   LINE      ECODE    ---       #577
      00FE1E6DH   LINE      ECODE    ---       #578
      00FE1E7FH   LINE      ECODE    ---       #579
      00FE1EB0H   LINE      ECODE    ---       #580
      00FE1EB2H   LINE      ECODE    ---       #581
      00FE1EB2H   LINE      ECODE    ---       #582
      00FE1EC4H   LINE      ECODE    ---       #583
      00FE1EF5H   LINE      ECODE    ---       #584
      00FE1EF5H   LINE      ECODE    ---       #585
      00FE1EF5H   LINE      ECODE    ---       #586
      00FE1EF5H   LINE      ECODE    ---       #587
      00FE1EF5H   LINE      ECODE    ---       #590
      ---         BLOCKEND  ---      ---       LVL=0

      00FE1EF6H   BLOCK     ECODE    ---       LVL=0
      00010C27H   SYMBOL    XDATA    BYTE      common_para_adj_node
      00FE1EF6H   LINE      ECODE    ---       #592
      00FE1EF6H   LINE      ECODE    ---       #593
      00FE1EF6H   LINE      ECODE    ---       #595
      00FE1F08H   LINE      ECODE    ---       #596
      00FE1F18H   LINE      ECODE    ---       #598
      00FE1F23H   LINE      ECODE    ---       #599
      00FE1F2FH   LINE      ECODE    ---       #601
      00FE1F35H   LINE      ECODE    ---       #602
      00FE1F35H   LINE      ECODE    ---       #603
      00FE1F41H   LINE      ECODE    ---       #605
      00FE1F47H   LINE      ECODE    ---       #606
      00FE1F47H   LINE      ECODE    ---       #607
      00FE1F4BH   LINE      ECODE    ---       #608
      00FE1F4BH   LINE      ECODE    ---       #609
      00FE1F5BH   LINE      ECODE    ---       #611
      00FE1F61H   LINE      ECODE    ---       #612
      00FE1F65H   LINE      ECODE    ---       #613
      00FE1F65H   LINE      ECODE    ---       #614
      00FE1F6FH   LINE      ECODE    ---       #616
      00FE1F6FH   LINE      ECODE    ---       #618
      00FE1F7FH   LINE      ECODE    ---       #620
      00FE1FAAH   LINE      ECODE    ---       #621
      00FE1FAEH   LINE      ECODE    ---       #622
      00FE1FAEH   LINE      ECODE    ---       #623
      00FE1FBEH   LINE      ECODE    ---       #625
      00FE1FE9H   LINE      ECODE    ---       #626
      00FE1FEDH   LINE      ECODE    ---       #627
      00FE1FEDH   LINE      ECODE    ---       #630
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 141


      00FE1FF4H   LINE      ECODE    ---       #632
      00FE1FF4H   LINE      ECODE    ---       #633
      00FE2006H   LINE      ECODE    ---       #634
      00FE2037H   LINE      ECODE    ---       #635
      00FE2037H   LINE      ECODE    ---       #636
      00FE2037H   LINE      ECODE    ---       #637
      00FE2037H   LINE      ECODE    ---       #638
      00FE2037H   LINE      ECODE    ---       #641
      00FE2037H   LINE      ECODE    ---       #643
      ---         BLOCKEND  ---      ---       LVL=0

      00FE2038H   BLOCK     ECODE    ---       LVL=0
      00010E93H   SYMBOL    XDATA    INT       x
      00010E95H   SYMBOL    XDATA    INT       y
      00010E97H   SYMBOL    XDATA    WORD      color
      00FE2038H   LINE      ECODE    ---       #649
      00FE204AH   LINE      ECODE    ---       #652
      00FE206CH   LINE      ECODE    ---       #653
      00FE208EH   LINE      ECODE    ---       #654
      00FE20AAH   LINE      ECODE    ---       #655
      00FE20CCH   LINE      ECODE    ---       #656
      00FE20EEH   LINE      ECODE    ---       #657
      00FE2110H   LINE      ECODE    ---       #658
      00FE2132H   LINE      ECODE    ---       #659
      00FE214EH   LINE      ECODE    ---       #660
      00FE216AH   LINE      ECODE    ---       #661
      00FE218CH   LINE      ECODE    ---       #662
      00FE21AEH   LINE      ECODE    ---       #663
      00FE21D0H   LINE      ECODE    ---       #664
      00FE21F2H   LINE      ECODE    ---       #665
      00FE220EH   LINE      ECODE    ---       #666
      00FE2230H   LINE      ECODE    ---       #667
      00FE2252H   LINE      ECODE    ---       #668
      ---         BLOCKEND  ---      ---       LVL=0

      00FE2253H   BLOCK     ECODE    ---       LVL=0
      00FE2255H   BLOCK     ECODE    NEAR LAB  LVL=1
      00010C28H   SYMBOL    XDATA    BYTE      first_enter
      0001088CH   SYMBOL    XDATA    DOUBLE    min_lon
      00010894H   SYMBOL    XDATA    DOUBLE    max_lon
      0001089CH   SYMBOL    XDATA    DOUBLE    min_lat
      000108A4H   SYMBOL    XDATA    DOUBLE    max_lat
      00010C29H   SYMBOL    XDATA    BYTE      point_count
      00010C2AH   SYMBOL    XDATA    FLOAT     point_adjust_step
      000108ACH   SYMBOL    XDATA    BYTE      valid_points
      000108ADH   SYMBOL    XDATA    BYTE      i
      000108AEH   SYMBOL    XDATA    FLOAT     lon_range
      000108B2H   SYMBOL    XDATA    FLOAT     lat_range
      000108B6H   SYMBOL    XDATA    FLOAT     scale_x
      000108BAH   SYMBOL    XDATA    FLOAT     scale_y
      000108BEH   SYMBOL    XDATA    FLOAT     scale
      000108C2H   SYMBOL    XDATA    FLOAT     center_x
      000108C6H   SYMBOL    XDATA    FLOAT     center_y
      000108CAH   SYMBOL    XDATA    INT       prev_x
      000108CCH   SYMBOL    XDATA    INT       prev_y
      000108CEH   SYMBOL    XDATA    BYTE      first_point
      000108CFH   SYMBOL    XDATA    INT       x
      000108D1H   SYMBOL    XDATA    INT       y
      ---         BLOCKEND  ---      ---       LVL=1
      00FE2253H   LINE      ECODE    ---       #674
      00FE2255H   LINE      ECODE    ---       #675
      00FE2255H   LINE      ECODE    ---       #680
      00FE225AH   LINE      ECODE    ---       #687
      00FE226CH   LINE      ECODE    ---       #689
      00FE2272H   LINE      ECODE    ---       #692
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 142


      00FE227EH   LINE      ECODE    ---       #693
      00FE2282H   LINE      ECODE    ---       #694
      00FE2287H   LINE      ECODE    ---       #695
      00FE228EH   LINE      ECODE    ---       #696
      00FE22AFH   LINE      ECODE    ---       #697
      00FE22BFH   LINE      ECODE    ---       #698
      00FE22C1H   LINE      ECODE    ---       #699
      00FE22C1H   LINE      ECODE    ---       #700
      00FE22E0H   LINE      ECODE    ---       #701
      00FE22E8H   LINE      ECODE    ---       #702
      00FE22EAH   LINE      ECODE    ---       #703
      00FE2301H   LINE      ECODE    ---       #705
      00FE2301H   LINE      ECODE    ---       #717
      00FE2310H   LINE      ECODE    ---       #720
      00FE2320H   LINE      ECODE    ---       #722
      00FE233CH   LINE      ECODE    ---       #723
      00FE2340H   LINE      ECODE    ---       #724
      00FE2340H   LINE      ECODE    ---       #725
      00FE2358H   LINE      ECODE    ---       #727
      00FE2376H   LINE      ECODE    ---       #728
      00FE23C6H   LINE      ECODE    ---       #729
      00FE23CAH   LINE      ECODE    ---       #730
      00FE23CAH   LINE      ECODE    ---       #732
      00FE23E8H   LINE      ECODE    ---       #733
      00FE2438H   LINE      ECODE    ---       #734
      00FE243CH   LINE      ECODE    ---       #735
      00FE243CH   LINE      ECODE    ---       #737
      00FE2454H   LINE      ECODE    ---       #739
      00FE2472H   LINE      ECODE    ---       #740
      00FE24C2H   LINE      ECODE    ---       #741
      00FE24C6H   LINE      ECODE    ---       #742
      00FE24C6H   LINE      ECODE    ---       #744
      00FE24E4H   LINE      ECODE    ---       #745
      00FE2534H   LINE      ECODE    ---       #746
      00FE2538H   LINE      ECODE    ---       #747
      00FE2538H   LINE      ECODE    ---       #750
      00FE2548H   LINE      ECODE    ---       #751
      00FE254EH   LINE      ECODE    ---       #752
      00FE2554H   LINE      ECODE    ---       #753
      00FE2558H   LINE      ECODE    ---       #754
      00FE255BH   LINE      ECODE    ---       #755
      00FE255BH   LINE      ECODE    ---       #758
      00FE2577H   LINE      ECODE    ---       #759
      00FE2593H   LINE      ECODE    ---       #760
      00FE25AFH   LINE      ECODE    ---       #761
      00FE25CBH   LINE      ECODE    ---       #763
      00FE25D0H   LINE      ECODE    ---       #765
      00FE25D8H   LINE      ECODE    ---       #766
      00FE25FAH   LINE      ECODE    ---       #767
      00FE266EH   LINE      ECODE    ---       #768
      00FE26E2H   LINE      ECODE    ---       #769
      00FE2756H   LINE      ECODE    ---       #770
      00FE27CAH   LINE      ECODE    ---       #771
      00FE27D5H   LINE      ECODE    ---       #772
      00FE27F1H   LINE      ECODE    ---       #776
      00FE27FAH   LINE      ECODE    ---       #777
      00FE280AH   LINE      ECODE    ---       #778
      00FE280DH   LINE      ECODE    ---       #779
      00FE280DH   LINE      ECODE    ---       #782
      00FE2843H   LINE      ECODE    ---       #783
      00FE2879H   LINE      ECODE    ---       #785
      00FE28A3H   LINE      ECODE    ---       #786
      00FE28CDH   LINE      ECODE    ---       #788
      00FE28EBH   LINE      ECODE    ---       #789
      00FE2909H   LINE      ECODE    ---       #790
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 143


      00FE2943H   LINE      ECODE    ---       #792
      00FE2985H   LINE      ECODE    ---       #793
      00FE29C7H   LINE      ECODE    ---       #796
      00FE29D7H   LINE      ECODE    ---       #797
      00FE29E9H   LINE      ECODE    ---       #798
      00FE29FFH   LINE      ECODE    ---       #799
      00FE2A15H   LINE      ECODE    ---       #800
      00FE2A4AH   LINE      ECODE    ---       #801
      00FE2A5CH   LINE      ECODE    ---       #802
      00FE2AA4H   LINE      ECODE    ---       #803
      00FE2AB6H   LINE      ECODE    ---       #804
      00FE2AFEH   LINE      ECODE    ---       #807
      00FE2B06H   LINE      ECODE    ---       #808
      00FE2B0EH   LINE      ECODE    ---       #809
      00FE2B14H   LINE      ECODE    ---       #811
      00FE2B1CH   LINE      ECODE    ---       #812
      00FE2B3EH   LINE      ECODE    ---       #814
      00FE2BABH   LINE      ECODE    ---       #815
      00FE2C18H   LINE      ECODE    ---       #819
      00FE2C48H   LINE      ECODE    ---       #820
      00FE2C78H   LINE      ECODE    ---       #822
      00FE2C8CH   LINE      ECODE    ---       #824
      00FE2C9AH   LINE      ECODE    ---       #825
      00FE2CADH   LINE      ECODE    ---       #826
      00FE2CADH   LINE      ECODE    ---       #828
      00FE2CB6H   LINE      ECODE    ---       #829
      00FE2CDCH   LINE      ECODE    ---       #830
      00FE2CDCH   LINE      ECODE    ---       #832
      00FE2CE8H   LINE      ECODE    ---       #833
      00FE2CF4H   LINE      ECODE    ---       #834
      00FE2CF9H   LINE      ECODE    ---       #836
      00FE2D15H   LINE      ECODE    ---       #838
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SEEKFREE_CONFIG
      00FF00A7H   PUBLIC    CODE     ---       dl1b_default_configuration
      00FF012EH   PUBLIC    CODE     ---       imu660ra_config_file

      ---         MODULE    ---      ---       ?C?DPADD?
      00FE6296H   PUBLIC    ECODE    ---       ?C?DPADD?
      00FE6292H   PUBLIC    ECODE    ---       ?C?DPSUB?

      ---         MODULE    ---      ---       ?C?DPMUL?
      00FE63A5H   PUBLIC    ECODE    ---       ?C?DPMUL?

      ---         MODULE    ---      ---       ?C?DPDIV?
      00FE64B5H   PUBLIC    ECODE    ---       ?C?DPDIV?

      ---         MODULE    ---      ---       ?C?DPCMP?
      00FE6554H   PUBLIC    ECODE    ---       ?C?DPCMP3?
      00FE6556H   PUBLIC    ECODE    ---       ?C?DPCMP?

      ---         MODULE    ---      ---       ?C?DPNEG?
      00FE65B5H   PUBLIC    ECODE    ---       ?C?DPNEG?

      ---         MODULE    ---      ---       ?C?DCAST?
      00FE65CDH   PUBLIC    ECODE    FAR LAB   ?C?DCASTC?
      00FE65C8H   PUBLIC    ECODE    FAR LAB   ?C?DCASTI?
      00FE65C3H   PUBLIC    ECODE    FAR LAB   ?C?DCASTL?

      ---         MODULE    ---      ---       ?C?CASTD?
      00FE6610H   PUBLIC    ECODE    FAR LAB   ?C?CASTD?

      ---         MODULE    ---      ---       ?C?DCASTF?
      00FE6670H   PUBLIC    ECODE    FAR LAB   ?C?DCASTF?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 144



      ---         MODULE    ---      ---       ?C?FCASTD?
      00FE66B2H   PUBLIC    ECODE    FAR LAB   ?C?FCASTD?

      ---         MODULE    ---      ---       PRINTF?
      00010A44H   PUBLIC    XDATA    ---       ?PRINTF??BYTE
      00FE66E9H   PUBLIC    ECODE    FAR LAB   PRINTF?

      ---         MODULE    ---      ---       SPRINTF?
      00010A44H   PUBLIC    XDATA    ---       ?SPRINTF??BYTE
      00FE6716H   PUBLIC    ECODE    FAR LAB   SPRINTF?

      ---         MODULE    ---      ---       FABS?
      00FE6736H   PUBLIC    ECODE    FAR LAB   FABS??

      ---         MODULE    ---      ---       SQRT??
      00FE674BH   PUBLIC    ECODE    ---       SQRT??

      ---         MODULE    ---      ---       POW??
      00FF3E10H   PUBLIC    ECODE    ---       pow??

      ---         MODULE    ---      ---       SIN??
      00FE689BH   PUBLIC    ECODE    ---       COS??
      00FE68BCH   PUBLIC    ECODE    ---       SIN??
      00FE68F2H   PUBLIC    ECODE    ---       TAN??

      ---         MODULE    ---      ---       ATAN??
      00FE6A3EH   PUBLIC    ECODE    ---       ATAN??

      ---         MODULE    ---      ---       ASIN??
      00FE6B62H   PUBLIC    ECODE    ---       ASIN??

      ---         MODULE    ---      ---       FLOOR??
      00FF44CEH   PUBLIC    ECODE    ---       floor??

      ---         MODULE    ---      ---       ?C?DPGETOPN?
      00FE6B91H   PUBLIC    ECODE    ---       ?C?DPGETOPN2?
      00FE6BDCH   PUBLIC    ECODE    ---       ?C?DPNANRESULT?
      00FE6BE8H   PUBLIC    ECODE    ---       ?C?DPOVERFLOW?
      00FE6BCDH   PUBLIC    ECODE    ---       ?C?DPRESULT2?
      00FE6BB4H   PUBLIC    ECODE    ---       ?C?DPRESULT?
      00FE6BE3H   PUBLIC    ECODE    ---       ?C?DPUNDERFLOW?

      ---         MODULE    ---      ---       _CHKDOUBLE_??
      00FE6BF6H   PUBLIC    ECODE    ---       _CHKDOUBLE_??

      ---         MODULE    ---      ---       ?C?PRNFMT?
      00FE6C41H   PUBLIC    ECODE    ---       ?C?PRNFMT?

      ---         MODULE    ---      ---       ?C?DPSERIES?
      00FE706FH   PUBLIC    ECODE    ---       ?C?DP2SERIES?
      00FE707BH   PUBLIC    ECODE    ---       ?C?DPSERIES?

      ---         MODULE    ---      ---       EXP??
      00FE7159H   PUBLIC    ECODE    ---       EXP??

      ---         MODULE    ---      ---       LOG??
      00FE7246H   PUBLIC    ECODE    FAR LAB   LOG??

      ---         MODULE    ---      ---       MODF??
      00FF4597H   PUBLIC    ECODE    ---       modf??

      ---         MODULE    ---      ---       ?C?DPCONVERT?
      00FE7369H   PUBLIC    ECODE    ---       ?C?DPCONVERT?
      00FE7492H   PUBLIC    ECODE    ---       ?C?DPROUND?
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 145



      ---         MODULE    ---      ---       ?C?DTNPWR?
      00FE756DH   PUBLIC    ECODE    ---       ?C?DTNPWR?

      ---         MODULE    ---      ---       ?C?FPGETOPN?
      00FE75F6H   PUBLIC    ECODE    ---       ?C?FPGETOPN2?
      00FE7633H   PUBLIC    ECODE    ---       ?C?FPNANRESULT?
      00FE763BH   PUBLIC    ECODE    ---       ?C?FPOVERFLOW?
      00FE7625H   PUBLIC    ECODE    ---       ?C?FPRESULT2?
      00FE760FH   PUBLIC    ECODE    ---       ?C?FPRESULT?
      00FE7638H   PUBLIC    ECODE    ---       ?C?FPUNDERFLOW?

      ---         MODULE    ---      ---       ?C?FPADD?
      00FE764DH   PUBLIC    ECODE    ---       ?C?FPADD?
      00FE764AH   PUBLIC    ECODE    ---       ?C?FPSUB?

      ---         MODULE    ---      ---       ?C?FPMUL?
      00FE7705H   PUBLIC    ECODE    ---       ?C?FPMUL?

      ---         MODULE    ---      ---       ?C?FPDIV?
      00FE77A5H   PUBLIC    ECODE    ---       ?C?FPDIV?

      ---         MODULE    ---      ---       ?C?FPCMP?
      00FE7826H   PUBLIC    ECODE    ---       ?C?FPCMP3?
      00FE7828H   PUBLIC    ECODE    ---       ?C?FPCMP?

      ---         MODULE    ---      ---       ?C?FPNEG?
      00FE786CH   PUBLIC    ECODE    ---       ?C?FPNEG?

      ---         MODULE    ---      ---       ?C?FCAST?
      00FE7884H   PUBLIC    ECODE    FAR LAB   ?C?FCASTC?
      00FE787FH   PUBLIC    ECODE    FAR LAB   ?C?FCASTI?
      00FE787AH   PUBLIC    ECODE    FAR LAB   ?C?FCASTL?

      ---         MODULE    ---      ---       ?C?CASTF?
      00FE78B7H   PUBLIC    ECODE    FAR LAB   ?C?CASTF?

      ---         MODULE    ---      ---       ?C?INITHDATA
      00FF273FH   PUBLIC    CODE     ---       ?C?INITHDATA

      ---         MODULE    ---      ---       ?C?SIDIV?
      00FE78F4H   PUBLIC    ECODE    ---       ?C?SIDIV?

      ---         MODULE    ---      ---       ?C?LMUL?
      00FE7926H   PUBLIC    ECODE    ---       ?C?LMUL?

      ---         MODULE    ---      ---       ?C?LIMUL?
      00FE7937H   PUBLIC    ECODE    ---       ?C?LIMUL?

      ---         MODULE    ---      ---       ?C?ULDIV?
      00FE7942H   PUBLIC    ECODE    FAR LAB   ?C?ULDIV?
      00FE7940H   PUBLIC    ECODE    FAR LAB   ?C?ULIDIV?

      ---         MODULE    ---      ---       ?C?SLDIV?
      00FE7993H   PUBLIC    ECODE    FAR LAB   ?C?SLDIV?

      ---         MODULE    ---      ---       ?C?BMOVEPP?
      00FE79C9H   PUBLIC    ECODE    ---       ?C?BMOVEPP8?
      00FE79CBH   PUBLIC    ECODE    ---       ?C?BMOVEPP?

      ---         MODULE    ---      ---       STRNCMP?
      00FF4619H   PUBLIC    ECODE    ---       strncmp??

      ---         MODULE    ---      ---       STRNCPY?
      00FF4650H   PUBLIC    ECODE    ---       strncpy??
L251 LINKER/LOCATER V4.66.93.0                                                        07/23/2025  07:24:14  PAGE 146



      ---         MODULE    ---      ---       STRLEN?
      00FF0067H   PUBLIC    ECODE    ---       strlen??

      ---         MODULE    ---      ---       STRCHR?
      00FF46A5H   PUBLIC    ECODE    ---       strchr??

      ---         MODULE    ---      ---       MEMCPY?
      00FF4680H   PUBLIC    ECODE    ---       memcpy??

      ---         MODULE    ---      ---       MEMSET?
      00FF0027H   PUBLIC    ECODE    ---       memset??

      ---         MODULE    ---      ---       ?C?INITHDATA_END
      00FF4BD3H   PUBLIC    HCONST   WORD      ?C?INITHDATA_END

Program Size: data=8.0 edata+hdata=2582 xdata=3914 const=2254 code=80512
L251 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
