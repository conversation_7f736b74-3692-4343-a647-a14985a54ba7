<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>STC32G12K128</TargetName>
      <ToolsetNumber>0x1</ToolsetNumber>
      <ToolsetName>MCS-251</ToolsetName>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STC32G12K128 Series</Device>
          <Vendor>STC</Vendor>
          <Cpu>IRAM(0-0xFFF) XRAM(0x10000-0x11FFF) IROM(0xFE0000-0xFFFFFF) CLOCK(35000000)</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"LIB\STARTUP251.ASM" ("80251 Startup Code")</StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>63457</DeviceId>
          <RegisterFile>STC32G.H</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>STC\</RegisterFilePath>
          <DBRegisterFilePath>STC\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Out_File\</OutputDirectory>
          <OutputName>SEEKFREE</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Out_File\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
          <BankNo>65535</BankNo>
        </CommonProperty>
        <DllOption>
          <SimDllName>S251.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DCORE51.DLL</SimDlgDll>
          <SimDlgDllArguments>-p251</SimDlgDllArguments>
          <TargetDllName>S251.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCORE51.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-p251</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>0</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>0</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\MON251.DLL</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>0</bUseTDR>
          <Flash2>BIN\STCMON251.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <Target251>
          <Target251Misc>
            <MemoryModel>4</MemoryModel>
            <RTOS>0</RTOS>
            <RomSize>4</RomSize>
            <NearDataHold>0</NearDataHold>
            <XDataHold>0</XDataHold>
            <FarDataHold>0</FarDataHold>
            <uocRom>0</uocRom>
            <uocXRAM>0</uocXRAM>
            <uSrcBin>1</uSrcBin>
            <uFrame4>0</uFrame4>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <hadIROM>1</hadIROM>
            <Use_Code_Banking>0</Use_Code_Banking>
            <uCC7>0</uCC7>
            <fp_hp>0</fp_hp>
            <CBANKS2>0</CBANKS2>
            <OnChipMemories>
              <RCB>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x10000</Size>
              </RCB>
              <IROM>
                <Type>1</Type>
                <StartAddress>0xfe0000</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x1000</Size>
              </IRAM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x10000</StartAddress>
                <Size>0x2000</Size>
              </XRAM>
              <Ocm1>
                <Type>1</Type>
                <StartAddress>0xfe0c00</StartAddress>
                <Size>0x1f400</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
            </OnChipMemories>
          </Target251Misc>
          <C251>
            <RegColor>0</RegColor>
            <uOrder>0</uOrder>
            <uAlias>1</uAlias>
            <uRentF>0</uRentF>
            <uUch>0</uUch>
            <uFlt64>1</uFlt64>
            <Fuzzy>3</Fuzzy>
            <Optim>0</Optim>
            <wLevel>3</wLevel>
            <SizSpd>1</SizSpd>
            <AcaOpt>0</AcaOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Libraries\libraries;..\..\Libraries\seekfree_libraries;..\..\Libraries\seekfree_peripheral;..\CODE;..\USER\inc;..\USER\src;..\..\Libraries\seekfree_components</IncludePath>
            </VariousControls>
          </C251>
          <Ax51>
            <UseMpl>0</UseMpl>
            <UseStandard>1</UseStandard>
            <UseCase>0</UseCase>
            <UseMod51>0</UseMod51>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Ax51>
          <Lx51>
            <useFile>0</useFile>
            <linkonly>0</linkonly>
            <UseMemoryFromTarget>1</UseMemoryFromTarget>
            <CaseSensitiveSymbols>1</CaseSensitiveSymbols>
            <WarningLevel>2</WarningLevel>
            <DataOverlaying>1</DataOverlaying>
            <OverlayString></OverlayString>
            <MiscControls>REMOVEUNUSED</MiscControls>
            <DisableWarningNumbers>15,16,57</DisableWarningNumbers>
            <LinkerCmdFile></LinkerCmdFile>
            <Assign></Assign>
            <ReserveString></ReserveString>
            <CClasses></CClasses>
            <UserClasses></UserClasses>
            <CSection></CSection>
            <UserSection></UserSection>
            <CodeBaseAddress></CodeBaseAddress>
            <XDataBaseAddress></XDataBaseAddress>
            <PDataBaseAddress></PDataBaseAddress>
            <BitBaseAddress></BitBaseAddress>
            <DataBaseAddress></DataBaseAddress>
            <IDataBaseAddress></IDataBaseAddress>
            <Precede></Precede>
            <Stack></Stack>
            <CodeSegmentName></CodeSegmentName>
            <XDataSegmentName></XDataSegmentName>
            <BitSegmentName></BitSegmentName>
            <DataSegmentName></DataSegmentName>
            <IDataSegmentName></IDataSegmentName>
          </Lx51>
        </Target251>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>startup</GroupName>
          <Files>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\libraries\board.c</FilePath>
            </File>
            <File>
              <FileName>board.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\libraries\board.h</FilePath>
            </File>
            <File>
              <FileName>common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\libraries\common.c</FilePath>
            </File>
            <File>
              <FileName>common.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\libraries\common.h</FilePath>
            </File>
            <File>
              <FileName>STC32Gxx.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\libraries\STC32Gxx.h</FilePath>
            </File>
            <File>
              <FileName>START251.A51</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\Libraries\libraries\START251.A51</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>seekfree_libraries</GroupName>
          <Files>
            <File>
              <FileName>headfile.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\headfile.h</FilePath>
            </File>
            <File>
              <FileName>zf_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_adc.c</FilePath>
            </File>
            <File>
              <FileName>zf_adc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_adc.h</FilePath>
            </File>
            <File>
              <FileName>zf_delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_delay.c</FilePath>
            </File>
            <File>
              <FileName>zf_delay.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_delay.h</FilePath>
            </File>
            <File>
              <FileName>zf_eeprom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_eeprom.c</FilePath>
            </File>
            <File>
              <FileName>zf_eeprom.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_eeprom.h</FilePath>
            </File>
            <File>
              <FileName>zf_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_gpio.c</FilePath>
            </File>
            <File>
              <FileName>zf_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_gpio.h</FilePath>
            </File>
            <File>
              <FileName>zf_nvic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_nvic.c</FilePath>
            </File>
            <File>
              <FileName>zf_nvic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_nvic.h</FilePath>
            </File>
            <File>
              <FileName>zf_pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_pwm.c</FilePath>
            </File>
            <File>
              <FileName>zf_pwm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_pwm.h</FilePath>
            </File>
            <File>
              <FileName>zf_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_spi.c</FilePath>
            </File>
            <File>
              <FileName>zf_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_spi.h</FilePath>
            </File>
            <File>
              <FileName>zf_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_tim.c</FilePath>
            </File>
            <File>
              <FileName>zf_tim.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_tim.h</FilePath>
            </File>
            <File>
              <FileName>zf_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_uart.c</FilePath>
            </File>
            <File>
              <FileName>zf_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_uart.h</FilePath>
            </File>
            <File>
              <FileName>zf_fifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_fifo.c</FilePath>
            </File>
            <File>
              <FileName>zf_fifo.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_fifo.h</FilePath>
            </File>
            <File>
              <FileName>zf_function.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_function.c</FilePath>
            </File>
            <File>
              <FileName>zf_function.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_libraries\zf_function.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>seekfree_peripheral</GroupName>
          <Files>
            <File>
              <FileName>headfile.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\headfile.h</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_CONFIG.LIB</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_CONFIG.LIB</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_CONFIG.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_CONFIG.h</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_FONT.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_FONT.c</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_FONT.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_FONT.h</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_IMU660RA.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_IMU660RA.c</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_IMU660RA.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_IMU660RA.h</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_TSL1401.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_TSL1401.c</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_TSL1401.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_TSL1401.h</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_GPS_TAU1201.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_GPS_TAU1201.c</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_GPS_TAU1201.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_GPS_TAU1201.h</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_IPS200_SPI.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_IPS200_SPI.c</FilePath>
            </File>
            <File>
              <FileName>SEEKFREE_IPS200_SPI.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\seekfree_peripheral\SEEKFREE_IPS200_SPI.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>doc</GroupName>
          <Files>
            <File>
              <FileName>version.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Libraries\doc\version.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_c</GroupName>
          <Files>
            <File>
              <FileName>isr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USER\src\isr.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USER\src\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_h</GroupName>
          <Files>
            <File>
              <FileName>isr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\USER\inc\isr.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>code</GroupName>
          <Files>
            <File>
              <FileName>beep.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\beep.c</FilePath>
            </File>
            <File>
              <FileName>beep.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\beep.h</FilePath>
            </File>
            <File>
              <FileName>config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\config.c</FilePath>
            </File>
            <File>
              <FileName>config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\config.h</FilePath>
            </File>
            <File>
              <FileName>key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\key.c</FilePath>
            </File>
            <File>
              <FileName>key.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\key.h</FilePath>
            </File>
            <File>
              <FileName>lora.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\lora.c</FilePath>
            </File>
            <File>
              <FileName>lora.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\lora.h</FilePath>
            </File>
            <File>
              <FileName>motor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\motor.c</FilePath>
            </File>
            <File>
              <FileName>motor.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\motor.h</FilePath>
            </File>
            <File>
              <FileName>my_atan2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\my_atan2.c</FilePath>
            </File>
            <File>
              <FileName>my_atan2.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\my_atan2.h</FilePath>
            </File>
            <File>
              <FileName>pid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\pid.c</FilePath>
            </File>
            <File>
              <FileName>pid.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\pid.h</FilePath>
            </File>
            <File>
              <FileName>point_processing.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\point_processing.c</FilePath>
            </File>
            <File>
              <FileName>point_processing.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\point_processing.h</FilePath>
            </File>
            <File>
              <FileName>quaternion_pose_calculating.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\quaternion_pose_calculating.c</FilePath>
            </File>
            <File>
              <FileName>quaternion_pose_calculating.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\quaternion_pose_calculating.h</FilePath>
            </File>
            <File>
              <FileName>exception_handling.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\exception_handling.c</FilePath>
            </File>
            <File>
              <FileName>exception_handling.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\exception_handling.h</FilePath>
            </File>
            <File>
              <FileName>task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\task.c</FilePath>
            </File>
            <File>
              <FileName>task.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\task.h</FilePath>
            </File>
            <File>
              <FileName>ccd_processing.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\ccd_processing.c</FilePath>
            </File>
            <File>
              <FileName>ccd_processing.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\ccd_processing.h</FilePath>
            </File>
            <File>
              <FileName>menu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CODE\menu.c</FilePath>
            </File>
            <File>
              <FileName>menu.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CODE\menu.h</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
