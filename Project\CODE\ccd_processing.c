#include "ccd_processing.h"
#include "math.h"
#define MOVING_AVG_WINDOW  5       // 移动平均窗口大小
#define MEDIAN_FILTER_SIZE 3       // 中值滤波窗口大小
#define WHITE_THRESHOLD    100     // 白线阈值
// 全局变量定义
//uint8 ccd_raw_data[CCD_PIXEL_NUM] = {0};

//曝光时间
uint8 timer_count = 10;

uint16 ccd_filtered_data[CCD_PIXEL_NUM] = {0};
EdgeInfo edges[MAX_EDGE_NUM] = {0};
uint8 edge_count = 0;
int16 line_position_error = 0;


void set_pit_timer_ms(TIMN_enum tim_n,uint16 time_ms)
{
	uint16 temp;
	temp = (uint16)65536 - (uint16)(sys_clk / (12 * (1000 / time_ms)));
	
	if(TIM_0 == tim_n)
	{
		TL0 = temp; 	
		TH0 = temp >> 8;
	}
	else if(TIM_1 == tim_n)
	{
		TL1 = temp; 	
		TH1 = temp >> 8;
	}
	else if(TIM_2 == tim_n)
	{
		T2L = temp; 	
		T2H = temp >> 8;
	}
	else if(TIM_3 == tim_n)
	{
		T3L = temp; 	
		T3H = temp >> 8;
	}
	else if(TIM_4 == tim_n)
	{
		T4L = temp; 	
		T4H = temp >> 8;
	}
}






// 移动平均滤波
static void moving_average_filter(uint16 *input, uint16 *output, uint8 data_size) {
    uint8 i, j;
    uint16 sum;
    
    for (i = MOVING_AVG_WINDOW/2; i < data_size - MOVING_AVG_WINDOW/2; i++) {
        sum = 0;
        for (j = 0; j < MOVING_AVG_WINDOW; j++) {
            sum += input[i - MOVING_AVG_WINDOW/2 + j];
        }
        output[i] = sum / MOVING_AVG_WINDOW;
    }
    
    // 处理边界
    for (i = 0; i < MOVING_AVG_WINDOW/2; i++) {
        output[i] = input[i];
        output[data_size - 1 - i] = input[data_size - 1 - i];
    }
}

// 中值滤波
static uint8 median_filter(uint8 *window, uint8 data_size) {
    uint8 i, j, temp;
    
    // 简单冒泡排序
    for (i = 0; i < data_size - 1; i++) {
        for (j = i + 1; j < data_size; j++) {
            if (window[j] < window[i]) {
                temp = window[i];
                window[i] = window[j];
                window[j] = temp;
            }
        }
    }
    
    return window[data_size/2];
}

// CCD数据初始化
//void ccd_data_init(void) {
//    memset(ccd_raw_data, 0, CCD_PIXEL_NUM);
//    memset(ccd_filtered_data, 0, CCD_PIXEL_NUM);
//    memset(edges, 0, sizeof(EdgeInfo) * MAX_EDGE_NUM);
//    edge_count = 0;
//    line_position_error = 0;
//}

// CCD数据滤波
void ccd_data_filter(void) {
    //uint8 i;
    //uint8 window[MEDIAN_FILTER_SIZE];
    
    // 第一步：移动平均滤波
    moving_average_filter(ccd_data_ch1, ccd_filtered_data, CCD_PIXEL_NUM);
    
    // 第二步：中值滤波
//    for (i = 1; i < CCD_PIXEL_NUM - 1; i++) {
//        window[0] = ccd_filtered_data[i - 1];
//        window[1] = ccd_filtered_data[i];
//        window[2] = ccd_filtered_data[i + 1];
//        ccd_filtered_data[i] = median_filter(window, MEDIAN_FILTER_SIZE);
//    }
}

// 边缘检测
// 新增函数：计算动态阈值
static uint16 calculate_dynamic_threshold(void) {
    uint16 max_val = 0;
    uint16 min_val = 4095; // 12位ADC最大值
    uint8 i;
    
    for (i = VALID_PIXEL_START; i < VALID_PIXEL_END; i++) {
        if (ccd_filtered_data[i] > max_val) max_val = ccd_filtered_data[i];
        if (ccd_filtered_data[i] < min_val) min_val = ccd_filtered_data[i];
    }
    return min_val + (uint16)((max_val - min_val) * DYNAMIC_THRESHOLD_RATIO);
}

// 修改边缘检测函数
void ccd_edge_detect(void) {
    uint8 i;
    uint8 in_edge = 0;
    uint8 current_edge = 0;
    uint16 dynamic_threshold = calculate_dynamic_threshold();
    
    edge_count = 0;
    
    for (i = VALID_PIXEL_START; i < VALID_PIXEL_END - SAMPLE_INTERVAL && edge_count < MAX_EDGE_NUM; i += SAMPLE_INTERVAL) {
        // 检测上升沿（黑到白）
        if (!in_edge && 
            ccd_filtered_data[i] > dynamic_threshold && 
            ccd_filtered_data[i-SAMPLE_INTERVAL] <= dynamic_threshold) {
            edges[current_edge].left_edge = i;
            edges[current_edge].max_value = ccd_filtered_data[i];
            in_edge = 1;
        }
        
        // 检测下降沿（白到黑）
        if (in_edge && 
            ccd_filtered_data[i] <= dynamic_threshold && 
            ccd_filtered_data[i-SAMPLE_INTERVAL] > dynamic_threshold) {
            edges[current_edge].right_edge = i - 1;
            edges[current_edge].width = edges[current_edge].right_edge - edges[current_edge].left_edge;
            in_edge = 0;
            current_edge++;
            edge_count++;
        }
        
        // 更新边缘区域最大值
        if (in_edge && ccd_filtered_data[i] > edges[current_edge].max_value) {
            edges[current_edge].max_value = ccd_filtered_data[i];
        }
    }
}

// 修改有效线判断函数
uint8 ccd_is_line_valid(void) {
		uint8 continuous_count = 0;
		uint8 max_continuous = 0;
		uint16 left_contrast;
		uint16 right_contrast;
		uint8 i;
		uint16 dynamic_threshold = calculate_dynamic_threshold();
    if (edge_count > 0) {
        // 1. 基本宽度检查
        if (edges[0].width < MIN_EXPECTED_WIDTH || edges[0].width > MAX_EXPECTED_WIDTH) {
            return 0;
        }

        // 2. 边缘对比度检查
        //uint16 left_contrast = edges[0].max_value - ccd_filtered_data[edges[0].left_edge-1];
        //uint16 right_contrast = edges[0].max_value - ccd_filtered_data[edges[0].right_edge+1];
		left_contrast = edges[0].max_value - ccd_filtered_data[edges[0].left_edge-1];
        right_contrast = edges[0].max_value - ccd_filtered_data[edges[0].right_edge+1];
        if (left_contrast < CONTRAST_THRESHOLD || right_contrast < CONTRAST_THRESHOLD) {
            return 0;
        }

        // 3. 连续性检查（新增）
        //uint8 continuous_count = 0;
        //uint8 max_continuous = 0;
        //for (uint8 i = edges[0].left_edge; i <= edges[0].right_edge; i++) {
				for (i = edges[0].left_edge; i <= edges[0].right_edge; i++) {
            if (ccd_filtered_data[i] > dynamic_threshold) {
                continuous_count++;
                if (continuous_count > max_continuous) {
                    max_continuous = continuous_count;
                }
            } else {
                continuous_count = 0;
            }
        }
        
        // 连续性阈值判断
        if (max_continuous < CONTINUITY_THRESHOLD) {
            return 0;
        }

        return 1;
    }
    return 0;
}

// 计算位置误差，负号左转，正号右转
int16 ccd_calc_position_error(void) {
    int32 sum = 0;
    int32 weight_sum = 0;
    uint8 i;
	int16 weight;

    int16 center;
    
    if (!ccd_is_line_valid()) {
        return 0;
    }
    
    // 使用加权平均计算中心位置
    for (i = edges[0].left_edge; i <= edges[0].right_edge; i++) {
        //int16 weight = ccd_filtered_data[i] - WHITE_THRESHOLD;
				weight = ccd_filtered_data[i] - WHITE_THRESHOLD;
        if (weight > 0) {
            sum += (i - CCD_MIDDLE_POS) * weight;
            weight_sum += weight;
        }
    }
    
    if (weight_sum != 0) {
        center = sum / weight_sum;
    } else {
        center = (edges[0].left_edge + edges[0].right_edge) / 2 - CCD_MIDDLE_POS;
    }
    
    line_position_error = center;
    return line_position_error;
}


// 计算位置误差，负号左转，正号右转
int16 ccd_calc_position_error_simple(void) {

    int16 center;

        center = (edges[0].left_edge + edges[0].right_edge) / 2 - CCD_MIDDLE_POS;
    
    line_position_error = center;
    return line_position_error;
}