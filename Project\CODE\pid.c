#include "pid.h"
#include "config.h"
#include "math.h"
//标志位定义
static uint8 speed_side_out_lock=0;

//传感器相关变量
float ctimer_speed = 0.0; //编码器速度



//角度环PID参数
float kp_angle = 1.0;//
float ki_angle = 0.0;
float kd_angle = 1.0f;//1.5;//0.5;//0.0;
//角速度环PID参数
float kp_rate = 2.50f;//3.5;//10;//目前参数2.5
float ki_rate = 0.01;//0.015;//0.004;//0.015;
float kd_rate = 1.0;//2.0;//2.5;//1.5;//0.2;//目前参数1.5

//速度环pid参数
float kp_speed = 270.0;
float ki_speed = 5.0f;//6.10f;//7.0f;//6.10f;//5.0f;//目前的参数
float kd_speed = 16.0f;//15.0f;//20.0f;//15.0f;//10.0f;//5.0f;//0.0;//目前的参数15.0f;

//ccd相关pid参数
float kp_ccd = 3;//1.5//0.5; // CCD角度环比例系数
float ki_ccd = 0.0; // CCD角度环积分系数
float kd_ccd = 0.0; // CCD角度环微分系数

//目标值
float target_rate = 0.0;
float target_angle=0;
float target_speed =2.0f;//1.0f;//3.0f;//2.0f;//1.0f;//0.75f;//1.0f;//1.50f;//2.0f;//3.0f;//1.0f;//0.75;//2.0f;//1.0f;//0.75f;//1.0f;//2.0f;//1;//0.75;

float rate_error_sum;
float angle_error_sum;
float speed_error_sum;

//输出值
float speed_out=0;
float speed_side_out=0;
float speed_ccd_out=0;

//限幅值
static float rate_integral_limit =500.0f;

//pid中间量

//static float rate_integral_sum = 0.0;

float rate_integral_sum = 0.0;

//pid速度环计数
uint8 time1=0;

//角度环pid函数
float pid_angle(float target_angle, float current_angle)
{
    static float angle_error = 0.0;
    static float angle_last = 0.0;
    static float angle_integral = 0.0;
    static float output = 0.0;
    
    angle_error = target_angle - current_angle;
    if(angle_error>200)
    angle_error-=360;
    if(angle_error<-200)
    angle_error+=360;
		
//		if(angle_error>180)
//    angle_error-=360;
//    if(angle_error<-180)
//    angle_error+=360;
    if(angle_error>5||angle_error<-5)
    {
//			if(angle_error>30||angle_error<-30)
//			{
//				//kp_rate=5;
//				//kp_rate=3.5;
//				kp_rate=2.7;
//				ki_rate=0.015;//0.004;
//				rate_integral_limit=500.0f;
//				
//			}
			if(angle_error>50||angle_error<-50)
			{
				//kp_rate=5;
				//kp_rate=3.5;
				kp_rate=2.7;
				//ki_rate=0.015;//0.004;
				rate_integral_limit=500.0f;
				
			}
			else
			{
				kp_rate=2.0;//2.5;
				//ki_rate=0.001;
				rate_integral_limit=200.0f;
			}
//			if((angle_error<10||angle_error>-10)
//				&&(rate_integral_sum>=200||rate_integral_sum<-200))
			if((angle_error<10||angle_error>-10)
				&&(rate_integral_sum>=100||rate_integral_sum<-100))
			{
				rate_integral_sum = 0;
			}
			angle_integral += angle_error;
      angle_error_sum=angle_integral;
      output = kp_angle * angle_error + ki_angle * angle_integral - kd_angle * (current_angle - angle_last);
      angle_last = current_angle;
      speed_side_out_lock=0;
    }
    else
    {
      speed_side_out_lock=1;
      output = 0.0;
    }
     return output;
}
////角速度环pid函数
//float pid_rate(float target_rate, float current_rate)
//{
//    static float rate_error = 0.0;
//    static float rate_error_last = 0.0;
//    static float rate_integral = 0.0;
//    static float output = 0.0;

//    rate_error = target_rate - current_rate;
//    //rate_integral += rate_error;
//		//error_sum=rate_integral;
//    rate_error_sum+=rate_error;
//		//积分项限幅

////    if(rate_integral > RATE_INTEGRAL_LIMIT) rate_integral = RATE_INTEGRAL_LIMIT;
////    else if(rate_integral < -RATE_INTEGRAL_LIMIT) rate_integral = -RATE_INTEGRAL_LIMIT;
//		if(rate_error_sum > RATE_INTEGRAL_LIMIT) rate_error_sum = RATE_INTEGRAL_LIMIT;
//    else if(rate_error_sum < -RATE_INTEGRAL_LIMIT) rate_error_sum = -RATE_INTEGRAL_LIMIT;
//    
//		//output = kp_rate * rate_error + ki_rate *(-200/(20*rate_error)+10.02)* rate_integral + kd_rate * (rate_error - rate_error_last);
//    //output = kp_rate * rate_error + ki_rate * rate_integral + kd_rate * (rate_error - rate_error_last);
//    
//		output = kp_rate * rate_error + ki_rate * rate_error_sum + kd_rate * (rate_error - rate_error_last);
//    
//		rate_error_last = rate_error;
//    if(output > RATE_OUTPUT_LIMIT_MAX)
//    {
//        output = RATE_OUTPUT_LIMIT_MAX;
//    }
//    else if(output < RATE_OUTPUT_LIMIT_MIN)
//    {
//        output = RATE_OUTPUT_LIMIT_MIN;
//    }
//    if(speed_side_out_lock==1)
//      output=0;
//    return output;
//}


//角速度环pid函数
float pid_rate(float target_rate, float current_rate)
{
    static float rate_error = 0.0;
    static float rate_last = 0.0;
    static float rate_integral = 0.0;
		//static float rate_integral_sum = 0.0;
    static float output = 0.0;

		
    rate_error = target_rate - current_rate;
		rate_error = rate_error/10*10;
    //rate_integral += rate_error;
		//error_sum=rate_integral;
    rate_error_sum+=rate_error;
		if(rate_error_sum>50000)rate_error_sum=50000;
		if(rate_error_sum<-50000)rate_error_sum=50000;
		rate_integral_sum = ki_rate*rate_error_sum;
		//积分项限幅

//    if(rate_integral > RATE_INTEGRAL_LIMIT) rate_integral = RATE_INTEGRAL_LIMIT;
//    else if(rate_integral < -RATE_INTEGRAL_LIMIT) rate_integral = -RATE_INTEGRAL_LIMIT;
		
//		
//		if(rate_error_sum > RATE_INTEGRAL_LIMIT) rate_error_sum = RATE_INTEGRAL_LIMIT;
//    else if(rate_error_sum < -RATE_INTEGRAL_LIMIT) rate_error_sum = -RATE_INTEGRAL_LIMIT;
//		
		
//		if(rate_integral_sum > RATE_INTEGRAL_LIMIT) rate_integral_sum = RATE_INTEGRAL_LIMIT;
//    else if(rate_integral_sum < -RATE_INTEGRAL_LIMIT) rate_integral_sum = -RATE_INTEGRAL_LIMIT;
    
		
		
//		if(rate_integral_sum > RATE_INTEGRAL_LIMIT) rate_integral_sum = rate_integral_limit;
//    else if(rate_integral_sum < -RATE_INTEGRAL_LIMIT) rate_integral_sum = -rate_integral_limit;

		if(rate_integral_sum > rate_integral_limit) rate_integral_sum = rate_integral_limit;
    else if(rate_integral_sum < -rate_integral_limit) rate_integral_sum = -rate_integral_limit;
		
    
		//output = kp_rate * rate_error + ki_rate *(-200/(20*rate_error)+10.02)* rate_integral + kd_rate * (rate_error - rate_error_last);
    //output = kp_rate * rate_error + ki_rate * rate_integral + kd_rate * (rate_error - rate_error_last);
    
		//output = kp_rate * rate_error + ki_rate * rate_error_sum - kd_rate * (current_rate - rate_last);
    
		output = kp_rate * rate_error + rate_integral_sum  - kd_rate * (current_rate - rate_last);
    
		rate_last = current_rate;
//    if(output > RATE_OUTPUT_LIMIT_MAX)
//    {
//        output = RATE_OUTPUT_LIMIT_MAX;
//    }
//    else if(output < RATE_OUTPUT_LIMIT_MIN)
//    {
//        output = RATE_OUTPUT_LIMIT_MIN;
//    }
		if(output > rate_integral_limit)
    {
        output = rate_integral_limit;
    }
    else if(output < -rate_integral_limit)
    {
        output = -rate_integral_limit;
    }
		
		
		
    if(speed_side_out_lock==1)
      output=0;
    return output;
}



//速度环pid函数
//问题记录：是使用仿照学长的积分清零
float pid_speed(float target_speed, float current_speed)
{
    static float speed_error = 0.0;
    static float speed_error_last = 0.0;
    static float speed_integral = 0.0;
		static float speed_integral_sum=0.0;
    static float output = 0.0;
	
//		if(current_speed<0.8*target_speed)
//		{
//			speed_error = target_speed - current_speed;
//			speed_integral += speed_error;
//			//积分项限幅
//			if(speed_integral > SPEED_INTEGRAL_LIMIT) speed_integral = SPEED_INTEGRAL_LIMIT;
//			else if(speed_integral < -SPEED_INTEGRAL_LIMIT) speed_integral = -SPEED_INTEGRAL_LIMIT;
//		
//			output = kp_speed * speed_error 
//								+ ki_speed * speed_integral 
//								+ kd_speed * (speed_error - speed_error_last)
//								+620;
//			speed_error_last = speed_error;
//			//输出限幅
//			if (output > SPEED_OUTPUT_LIMIT_MAX)
//			{
//					output = SPEED_OUTPUT_LIMIT_MAX;
//			}
//			else if (output < SPEED_OUTPUT_LIMIT_MIN)
//			{
//					output = SPEED_OUTPUT_LIMIT_MIN;
//			}
//			return output;
//			
//		}	
//		
//		else
//		{
			speed_error = target_speed - current_speed;
			speed_integral += speed_error;
			if(speed_integral>80)
			speed_integral=80;
			if(speed_integral<-80)
			speed_integral=-80;
			speed_integral_sum=ki_speed * speed_integral ;
			//积分项限幅
//			if(speed_integral > SPEED_INTEGRAL_LIMIT) speed_integral = SPEED_INTEGRAL_LIMIT;
//			else if(speed_integral < -SPEED_INTEGRAL_LIMIT) speed_integral = -SPEED_INTEGRAL_LIMIT;
//		
			if(speed_integral_sum > SPEED_INTEGRAL_LIMIT) speed_integral_sum = SPEED_INTEGRAL_LIMIT;
			else if(speed_integral_sum < -SPEED_INTEGRAL_LIMIT) speed_integral_sum = -SPEED_INTEGRAL_LIMIT;
			speed_error_sum=speed_integral_sum;
			//if(current_speed<target_speed&&speed_integral_sum<-100)
				if(current_speed<target_speed&&speed_integral_sum<-20)
				speed_integral_sum=0;
			
			
//			output = kp_speed * speed_error 
//								+ ki_speed * speed_integral 
//								+ kd_speed * (speed_error - speed_error_last)
//								+620;
			output = kp_speed * speed_error 
								+ speed_integral_sum 
								+ kd_speed * (speed_error - speed_error_last)
								+600;//640;
			speed_error_last = speed_error;
			//输出限幅
			if (output > SPEED_OUTPUT_LIMIT_MAX)
			{
					output = SPEED_OUTPUT_LIMIT_MAX;
			}
			else if (output < SPEED_OUTPUT_LIMIT_MIN)
			{
					output = SPEED_OUTPUT_LIMIT_MIN;
			}
			return output;
		//}
}

float pid_ccd(float target_ccd, float current_ccd)
{
    static float ccd_error = 0.0;
    static float ccd_error_last = 0.0;
    static float ccd_integral = 0.0;
    static float output = 0.0;

    ccd_error = target_ccd - current_ccd;
    ccd_integral += ccd_error;
    //积分项限幅
    //if(ccd_integral > CCD_INTEGRAL_LIMIT) ccd_integral = CCD_INTEGRAL_LIMIT;
    //else if(ccd_integral < -CCD_INTEGRAL_LIMIT) ccd_integral = -CCD_INTEGRAL_LIMIT;
    output = kp_ccd * ccd_error + ki_ccd * ccd_integral + kd_ccd * (ccd_error - ccd_error_last);
    ccd_error_last = ccd_error;
    //输出限幅
    if (output > CCD_OUTPUT_LIMIT_MAX)
    {
        output = CCD_OUTPUT_LIMIT_MAX;
    }
    else if (output < CCD_OUTPUT_LIMIT_MIN)
    {
        output = CCD_OUTPUT_LIMIT_MIN;
    }
    return output;
}
