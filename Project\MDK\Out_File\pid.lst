C251 COMPILER V5.60.0,  pid                                                                22/07/25  15:14:31  PAGE 1   


C251 COMPILER V5.60.0, COMPILATION OF MODULE pid
OBJECT MODULE PLACED IN .\Out_File\pid.obj
COMPILER INVOKED BY: E:\Keil5C251\C251\BIN\C251.EXE ..\CODE\pid.c LARGE INTR2 FLOAT64 ROM(HUGE) WARNINGLEVEL(3) OPTIMIZE
                    -(0,SPEED) BROWSE INCDIR(..\..\Libraries\libraries;..\..\Libraries\seekfree_libraries;..\..\Libraries\seekfree_peripheral
                    -;..\CODE;..\USER\inc;..\USER\src;..\..\Libraries\seekfree_components) DEBUG PRINT(.\Out_File\pid.lst) TABS(2) OBJECT(.\O
                    -ut_File\pid.obj) 

stmt  level    source

    1          #include "pid.h"
    2          #include "config.h"
    3          #include "math.h"
    4          //标志位定义
    5          static uint8 speed_side_out_lock=0;
    6          
    7          //传感器相关变量
    8          float ctimer_speed = 0.0; //编码器速度
    9          
   10          
   11          
   12          //角度环PID参数
   13          float kp_angle = 1.0;//
   14          float ki_angle = 0.0;
   15          float kd_angle = 1.0f;//1.5;//0.5;//0.0;
   16          //角速度环PID参数
   17          float kp_rate = 2.50f;//3.5;//10;//目前参数2.5
   18          float ki_rate = 0.01;//0.015;//0.004;//0.015;
   19          float kd_rate = 1.0;//2.0;//2.5;//1.5;//0.2;//目前参数1.5
   20          
   21          //速度环pid参数
   22          float kp_speed = 270.0;
   23          float ki_speed = 5.0f;//6.10f;//7.0f;//6.10f;//5.0f;//目前的参数
   24          float kd_speed = 16.0f;//15.0f;//20.0f;//15.0f;//10.0f;//5.0f;//0.0;//目前的参数15.0f;
   25          
   26          //ccd相关pid参数
   27          float kp_ccd = 3;//1.5//0.5; // CCD角度环比例系数
   28          float ki_ccd = 0.0; // CCD角度环积分系数
   29          float kd_ccd = 0.0; // CCD角度环微分系数
   30          
   31          //目标值
   32          float target_rate = 0.0;
   33          float target_angle=0;
   34          float target_speed =2.0f;//1.0f;//3.0f;//2.0f;//1.0f;//0.75f;//1.0f;//1.50f;//2.0f;//3.0f;//1.0f;//0.75;/
             -/2.0f;//1.0f;//0.75f;//1.0f;//2.0f;//1;//0.75;
   35          
   36          float rate_error_sum;
   37          float angle_error_sum;
   38          float speed_error_sum;
   39          
   40          //输出值
   41          float speed_out=0;
   42          float speed_side_out=0;
   43          float speed_ccd_out=0;
   44          
   45          //限幅值
   46          static float rate_integral_limit =500.0f;
   47          
   48          //pid中间量
   49          
   50          //static float rate_integral_sum = 0.0;
   51          
   52          float rate_integral_sum = 0.0;
   53          
   54          //pid速度环计数
   55          uint8 time1=0;
C251 COMPILER V5.60.0,  pid                                                                22/07/25  15:14:31  PAGE 2   

   56          
   57          //角度环pid函数
   58          float pid_angle(float target_angle, float current_angle)
   59          {
   60   1          static float angle_error = 0.0;
   61   1          static float angle_last = 0.0;
   62   1          static float angle_integral = 0.0;
   63   1          static float output = 0.0;
   64   1          
   65   1          angle_error = target_angle - current_angle;
   66   1          if(angle_error>200)
   67   1          angle_error-=360;
   68   1          if(angle_error<-200)
   69   1          angle_error+=360;
   70   1          
   71   1      //    if(angle_error>180)
   72   1      //    angle_error-=360;
   73   1      //    if(angle_error<-180)
   74   1      //    angle_error+=360;
   75   1          if(angle_error>5||angle_error<-5)
   76   1          {
   77   2      //      if(angle_error>30||angle_error<-30)
   78   2      //      {
   79   2      //        //kp_rate=5;
   80   2      //        //kp_rate=3.5;
   81   2      //        kp_rate=2.7;
   82   2      //        ki_rate=0.015;//0.004;
   83   2      //        rate_integral_limit=500.0f;
   84   2      //        
   85   2      //      }
   86   2            if(angle_error>50||angle_error<-50)
   87   2            {
   88   3              //kp_rate=5;
   89   3              //kp_rate=3.5;
   90   3              kp_rate=2.7;
   91   3              //ki_rate=0.015;//0.004;
   92   3              rate_integral_limit=500.0f;
   93   3              
   94   3            }
   95   2            else
   96   2            {
   97   3              kp_rate=2.0;//2.5;
   98   3              //ki_rate=0.001;
   99   3              rate_integral_limit=200.0f;
  100   3            }
  101   2      //      if((angle_error<10||angle_error>-10)
  102   2      //        &&(rate_integral_sum>=200||rate_integral_sum<-200))
  103   2            if((angle_error<10||angle_error>-10)
  104   2              &&(rate_integral_sum>=100||rate_integral_sum<-100))
  105   2            {
  106   3              rate_integral_sum = 0;
  107   3            }
  108   2            angle_integral += angle_error;
  109   2            angle_error_sum=angle_integral;
  110   2            output = kp_angle * angle_error + ki_angle * angle_integral - kd_angle * (current_angle - angle_las
             -t);
  111   2            angle_last = current_angle;
  112   2            speed_side_out_lock=0;
  113   2          }
  114   1          else
  115   1          {
  116   2            speed_side_out_lock=1;
  117   2            output = 0.0;
  118   2          }
  119   1           return output;
  120   1      }
C251 COMPILER V5.60.0,  pid                                                                22/07/25  15:14:31  PAGE 3   

  121          ////角速度环pid函数
  122          //float pid_rate(float target_rate, float current_rate)
  123          //{
  124          //    static float rate_error = 0.0;
  125          //    static float rate_error_last = 0.0;
  126          //    static float rate_integral = 0.0;
  127          //    static float output = 0.0;
  128          
  129          //    rate_error = target_rate - current_rate;
  130          //    //rate_integral += rate_error;
  131          //    //error_sum=rate_integral;
  132          //    rate_error_sum+=rate_error;
  133          //    //积分项限幅
  134          
  135          ////    if(rate_integral > RATE_INTEGRAL_LIMIT) rate_integral = RATE_INTEGRAL_LIMIT;
  136          ////    else if(rate_integral < -RATE_INTEGRAL_LIMIT) rate_integral = -RATE_INTEGRAL_LIMIT;
  137          //    if(rate_error_sum > RATE_INTEGRAL_LIMIT) rate_error_sum = RATE_INTEGRAL_LIMIT;
  138          //    else if(rate_error_sum < -RATE_INTEGRAL_LIMIT) rate_error_sum = -RATE_INTEGRAL_LIMIT;
  139          //    
  140          //    //output = kp_rate * rate_error + ki_rate *(-200/(20*rate_error)+10.02)* rate_integral + kd_rate * (r
             -ate_error - rate_error_last);
  141          //    //output = kp_rate * rate_error + ki_rate * rate_integral + kd_rate * (rate_error - rate_error_last
             -);
  142          //    
  143          //    output = kp_rate * rate_error + ki_rate * rate_error_sum + kd_rate * (rate_error - rate_error_last);
  144          //    
  145          //    rate_error_last = rate_error;
  146          //    if(output > RATE_OUTPUT_LIMIT_MAX)
  147          //    {
  148          //        output = RATE_OUTPUT_LIMIT_MAX;
  149          //    }
  150          //    else if(output < RATE_OUTPUT_LIMIT_MIN)
  151          //    {
  152          //        output = RATE_OUTPUT_LIMIT_MIN;
  153          //    }
  154          //    if(speed_side_out_lock==1)
  155          //      output=0;
  156          //    return output;
  157          //}
  158          
  159          
  160          //角速度环pid函数
  161          float pid_rate(float target_rate, float current_rate)
  162          {
  163   1          static float rate_error = 0.0;
  164   1          static float rate_last = 0.0;
  165   1          static float rate_integral = 0.0;
  166   1          //static float rate_integral_sum = 0.0;
  167   1          static float output = 0.0;
  168   1      
  169   1          
  170   1          rate_error = target_rate - current_rate;
  171   1          rate_error = rate_error/10*10;
  172   1          //rate_integral += rate_error;
  173   1          //error_sum=rate_integral;
  174   1          rate_error_sum+=rate_error;
  175   1          if(rate_error_sum>50000)rate_error_sum=50000;
  176   1          if(rate_error_sum<-50000)rate_error_sum=50000;
  177   1          rate_integral_sum = ki_rate*rate_error_sum;
  178   1          //积分项限幅
  179   1      
  180   1      //    if(rate_integral > RATE_INTEGRAL_LIMIT) rate_integral = RATE_INTEGRAL_LIMIT;
  181   1      //    else if(rate_integral < -RATE_INTEGRAL_LIMIT) rate_integral = -RATE_INTEGRAL_LIMIT;
  182   1          
  183   1      //    
  184   1      //    if(rate_error_sum > RATE_INTEGRAL_LIMIT) rate_error_sum = RATE_INTEGRAL_LIMIT;
C251 COMPILER V5.60.0,  pid                                                                22/07/25  15:14:31  PAGE 4   

  185   1      //    else if(rate_error_sum < -RATE_INTEGRAL_LIMIT) rate_error_sum = -RATE_INTEGRAL_LIMIT;
  186   1      //    
  187   1          
  188   1      //    if(rate_integral_sum > RATE_INTEGRAL_LIMIT) rate_integral_sum = RATE_INTEGRAL_LIMIT;
  189   1      //    else if(rate_integral_sum < -RATE_INTEGRAL_LIMIT) rate_integral_sum = -RATE_INTEGRAL_LIMIT;
  190   1          
  191   1          
  192   1          
  193   1      //    if(rate_integral_sum > RATE_INTEGRAL_LIMIT) rate_integral_sum = rate_integral_limit;
  194   1      //    else if(rate_integral_sum < -RATE_INTEGRAL_LIMIT) rate_integral_sum = -rate_integral_limit;
  195   1      
  196   1          if(rate_integral_sum > rate_integral_limit) rate_integral_sum = rate_integral_limit;
  197   1          else if(rate_integral_sum < -rate_integral_limit) rate_integral_sum = -rate_integral_limit;
  198   1          
  199   1          
  200   1          //output = kp_rate * rate_error + ki_rate *(-200/(20*rate_error)+10.02)* rate_integral + kd_rate * (rat
             -e_error - rate_error_last);
  201   1          //output = kp_rate * rate_error + ki_rate * rate_integral + kd_rate * (rate_error - rate_error_last);
  202   1          
  203   1          //output = kp_rate * rate_error + ki_rate * rate_error_sum - kd_rate * (current_rate - rate_last);
  204   1          
  205   1          output = kp_rate * rate_error + rate_integral_sum  - kd_rate * (current_rate - rate_last);
  206   1          
  207   1          rate_last = current_rate;
  208   1      //    if(output > RATE_OUTPUT_LIMIT_MAX)
  209   1      //    {
  210   1      //        output = RATE_OUTPUT_LIMIT_MAX;
  211   1      //    }
  212   1      //    else if(output < RATE_OUTPUT_LIMIT_MIN)
  213   1      //    {
  214   1      //        output = RATE_OUTPUT_LIMIT_MIN;
  215   1      //    }
  216   1          if(output > rate_integral_limit)
  217   1          {
  218   2              output = rate_integral_limit;
  219   2          }
  220   1          else if(output < -rate_integral_limit)
  221   1          {
  222   2              output = -rate_integral_limit;
  223   2          }
  224   1          
  225   1          
  226   1          
  227   1          if(speed_side_out_lock==1)
  228   1            output=0;
  229   1          return output;
  230   1      }
  231          
  232          
  233          
  234          //速度环pid函数
  235          //问题记录：是使用仿照学长的积分清零
  236          float pid_speed(float target_speed, float current_speed)
  237          {
  238   1          static float speed_error = 0.0;
  239   1          static float speed_error_last = 0.0;
  240   1          static float speed_integral = 0.0;
  241   1          static float speed_integral_sum=0.0;
  242   1          static float output = 0.0;
  243   1        
  244   1      //    if(current_speed<0.8*target_speed)
  245   1      //    {
  246   1      //      speed_error = target_speed - current_speed;
  247   1      //      speed_integral += speed_error;
  248   1      //      //积分项限幅
  249   1      //      if(speed_integral > SPEED_INTEGRAL_LIMIT) speed_integral = SPEED_INTEGRAL_LIMIT;
C251 COMPILER V5.60.0,  pid                                                                22/07/25  15:14:31  PAGE 5   

  250   1      //      else if(speed_integral < -SPEED_INTEGRAL_LIMIT) speed_integral = -SPEED_INTEGRAL_LIMIT;
  251   1      //    
  252   1      //      output = kp_speed * speed_error 
  253   1      //                + ki_speed * speed_integral 
  254   1      //                + kd_speed * (speed_error - speed_error_last)
  255   1      //                +620;
  256   1      //      speed_error_last = speed_error;
  257   1      //      //输出限幅
  258   1      //      if (output > SPEED_OUTPUT_LIMIT_MAX)
  259   1      //      {
  260   1      //          output = SPEED_OUTPUT_LIMIT_MAX;
  261   1      //      }
  262   1      //      else if (output < SPEED_OUTPUT_LIMIT_MIN)
  263   1      //      {
  264   1      //          output = SPEED_OUTPUT_LIMIT_MIN;
  265   1      //      }
  266   1      //      return output;
  267   1      //      
  268   1      //    } 
  269   1      //    
  270   1      //    else
  271   1      //    {
  272   1            speed_error = target_speed - current_speed;
  273   1            speed_integral += speed_error;
  274   1            if(speed_integral>80)
  275   1            speed_integral=80;
  276   1            if(speed_integral<-80)
  277   1            speed_integral=-80;
  278   1            speed_integral_sum=ki_speed * speed_integral ;
  279   1            //积分项限幅
  280   1      //      if(speed_integral > SPEED_INTEGRAL_LIMIT) speed_integral = SPEED_INTEGRAL_LIMIT;
  281   1      //      else if(speed_integral < -SPEED_INTEGRAL_LIMIT) speed_integral = -SPEED_INTEGRAL_LIMIT;
  282   1      //    
  283   1            if(speed_integral_sum > SPEED_INTEGRAL_LIMIT) speed_integral_sum = SPEED_INTEGRAL_LIMIT;
  284   1            else if(speed_integral_sum < -SPEED_INTEGRAL_LIMIT) speed_integral_sum = -SPEED_INTEGRAL_LIMIT;
  285   1            speed_error_sum=speed_integral_sum;
  286   1            //if(current_speed<target_speed&&speed_integral_sum<-100)
  287   1              if(current_speed<target_speed&&speed_integral_sum<-20)
  288   1              speed_integral_sum=0;
  289   1            
  290   1            
  291   1      //      output = kp_speed * speed_error 
  292   1      //                + ki_speed * speed_integral 
  293   1      //                + kd_speed * (speed_error - speed_error_last)
  294   1      //                +620;
  295   1            output = kp_speed * speed_error 
  296   1                      + speed_integral_sum 
  297   1                      + kd_speed * (speed_error - speed_error_last)
  298   1                      +600;//640;
  299   1            speed_error_last = speed_error;
  300   1            //输出限幅
  301   1            if (output > SPEED_OUTPUT_LIMIT_MAX)
  302   1            {
  303   2                output = SPEED_OUTPUT_LIMIT_MAX;
  304   2            }
  305   1            else if (output < SPEED_OUTPUT_LIMIT_MIN)
  306   1            {
  307   2                output = SPEED_OUTPUT_LIMIT_MIN;
  308   2            }
  309   1            return output;
  310   1          //}
  311   1      }
  312          
  313          float pid_ccd(float target_ccd, float current_ccd)
  314          {
  315   1          static float ccd_error = 0.0;
C251 COMPILER V5.60.0,  pid                                                                22/07/25  15:14:31  PAGE 6   

  316   1          static float ccd_error_last = 0.0;
  317   1          static float ccd_integral = 0.0;
  318   1          static float output = 0.0;
  319   1      
  320   1          ccd_error = target_ccd - current_ccd;
  321   1          ccd_integral += ccd_error;
  322   1          //积分项限幅
  323   1          //if(ccd_integral > CCD_INTEGRAL_LIMIT) ccd_integral = CCD_INTEGRAL_LIMIT;
  324   1          //else if(ccd_integral < -CCD_INTEGRAL_LIMIT) ccd_integral = -CCD_INTEGRAL_LIMIT;
  325   1          output = kp_ccd * ccd_error + ki_ccd * ccd_integral + kd_ccd * (ccd_error - ccd_error_last);
  326   1          ccd_error_last = ccd_error;
  327   1          //输出限幅
  328   1          if (output > CCD_OUTPUT_LIMIT_MAX)
  329   1          {
  330   2              output = CCD_OUTPUT_LIMIT_MAX;
  331   2          }
  332   1          else if (output < CCD_OUTPUT_LIMIT_MIN)
  333   1          {
  334   2              output = CCD_OUTPUT_LIMIT_MIN;
  335   2          }
  336   1          return output;
  337   1      }


Module Information          Static   Overlayable
------------------------------------------------
  code size            =    ------     ------
  ecode size           =      2111     ------
  data size            =    ------     ------
  idata size           =    ------     ------
  pdata size           =    ------     ------
  xdata size           =       198     ------
  xdata-const size     =    ------     ------
  edata size           =    ------     ------
  bit size             =    ------     ------
  ebit size            =    ------     ------
  bitaddressable size  =    ------     ------
  ebitaddressable size =    ------     ------
  far data size        =    ------     ------
  huge data size       =    ------     ------
  const size           =    ------     ------
  hconst size          =       354     ------
End of Module Information.


C251 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
