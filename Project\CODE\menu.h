#ifndef __MENU_H_
#define __MENU_H_

//第一级菜单枚举类型
typedef enum first_menu
{
    take_point_page,
    state_slect_page,
    common_function_page,
    pid_parameter_adjustment_page,
    common_para_adj_page,
    path_display_page,
		ccd_page,
} first_menu_enum;


//第二级菜单枚举类型
//typedef enum second_menu
//{
//    start_page=1,
////    stop_page,
////    debug_page,
//    link_ccd_page,
//    link_pid_parameter_page,
//    link_path_display_page,
//    clear_eeprom_page,
//}
//second_menu_enum;

typedef enum second_menu
{
    start_page=1,
    link_ccd_page,
    link_pid_parameter_page,
    link_common_para_adj_page,
    link_path_display_page,
    clear_eeprom_page,
}
second_menu_enum;



//点位状态选择枚举类型在point_processing.h中定义

//pid叶子枚举类型
typedef enum pid_slection
{
    kp_angle_1=1,
    ki_angle_2,
    kd_angle_3,
    kp_rate_4,
    ki_rate_5,
    kd_rate_6,
    kp_speed_7,
    ki_speed_8,
    kd_speed_9,
}pid_slection;

typedef enum start_step_enum
{
	start_idle=0,
	pre_start=1,
	formal_start=2,
} start_step_enum;


typedef enum common_para_adj_enum
{
  target_speed_1=1,


} common_para_adj_enum;




void menu_display(void);
void take_point_page_fun(void);
void state_slect_page_fun(void);
void function_page_fun(void);
void start_page_fun(void);
void ccd_page_fun(void);
void parameter_adjustment_page_fun(void);
void common_para_adj_page_fun(void);
void path_display_page_fun(void);
void clear_eeprom_page_fun(void);

#endif
