#ifndef __CCD_PROCESSING_H
#define __CCD_PROCESSING_H


#include "headfile.h"
#include "SEEKFREE_TSL1401.h"




// CCD参数配置
// 修改CCD参数配置部分
#define CCD_PIXEL_NUM      128
#define CCD_MIDDLE_POS     64
#define VALID_PIXEL_START  10
#define VALID_PIXEL_END    118
#define MAX_EDGE_NUM       2
#define SAMPLE_INTERVAL    5//3      // 采样间隔——重点调节
#define DYNAMIC_THRESHOLD_RATIO 0.7  // 动态阈值比例系数——重点调节

// 修改边缘数据结构
typedef struct {
    uint8 left_edge;
    uint8 right_edge;
    uint8 width;
    uint16 max_value;    // 边缘区域最大值
} EdgeInfo;

// 全局变量声明
//extern uint8 ccd_raw_data[CCD_PIXEL_NUM];       // CCD原始数据

extern uint8 timer_count;

extern uint16 ccd_filtered_data[CCD_PIXEL_NUM];  // 滤波后数据
extern EdgeInfo edges[MAX_EDGE_NUM];            // 边缘信息
extern uint8 edge_count;                        // 检测到的边缘数量
extern int16 line_position_error;               // 位置误差

extern uint16 ccd_filtered_data[CCD_PIXEL_NUM];

// 函数声明
void set_pit_timer_ms(TIMN_enum tim_n,uint16 time_ms);
//void ccd_data_init(void);                       // CCD数据初始化
void ccd_data_filter(void);                     // CCD数据滤波
void ccd_edge_detect(void);                     // 边缘检测
int16 ccd_calc_position_error(void);            // 计算位置误差
uint8 ccd_is_line_valid(void);                  // 判断是否检测到有效线

int16 ccd_calc_position_error_simple(void);
// 有效线判断参数
#define MIN_EXPECTED_WIDTH   5     // 最小预期宽度（像素）——重点调节
#define MAX_EXPECTED_WIDTH   30    // 最大预期宽度（像素）——重点调节
#define CONTRAST_THRESHOLD   50    // 边缘最小对比度——重点调节
#define CONTINUITY_THRESHOLD 10    // 最小连续像素数——重点调节

#endif