#ifndef _CONFIG_H_
#define _CONFIG_H_

#include "lora.h"
#include "key.h"
#include "motor.h"
#include "beep.h"
#include "task.h"
#include "quaternion_pose_calculating.h"
#include "pid.h"
#include "point_processing.h"
#include "ccd_processing.h"
#include "exception_handling.h"
#include "menu.h"
#include "stdio.h"
#include "my_atan2.h"
#include "stdio.h"
#include <stdlib.h>
#include <math.h>

#include "key.h"
// 如果需要重定义，先取消之前的定义
#ifdef key_point
#undef key_point
#endif

// 定义新的值
#define key_point key1_pres

//模式选择宏定义
#define NORMAL_MODE 0 //正常模式
#define TEST_MODE 1  //调试模式
//#define NOW_MODE TEST_MODE //当前模式选择
#define NOW_MODE NORMAL_MODE //当前模式选择

//CCD循迹模式选择
#define CCD_TRACKING_MODE_SIMPLE 0
#define CCD_TRACKING_MODE_ADVANCED 1

#define CCD_TRACKING_MODE CCD_TRACKING_MODE_SIMPLE
//#define CCD_TRACKING_MODE CCD_TRACKING_MODE_ADVANCED

//元素处理任务模式选择
#define ELEMENT_PROCESSING_TASK_MODE_0 0 //元素处理任务未控制角度
#define ELEMENT_PROCESSING_TASK_MODE_TEST_1 1 //元素处理任务控制角度

//#define ELEMENT_PROCESSING_TASK_MODE ELEMENT_PROCESSING_TASK_MODE_0
#define ELEMENT_PROCESSING_TASK_MODE ELEMENT_PROCESSING_TASK_MODE_TEST_1

//元素处理发送数据模式选择
#define ELEMENT_PROCESSING_SEND_DATA 0 //元素处理发送数据
#define ELEMENT_PROCESSING_SEND_DATA_NO 1 //元素处理不发送数据

//选择元素处理发送数据模式
//#define ELEMENT_PROCESSING_SEND_DATA_MODE ELEMENT_PROCESSING_SEND_DATA_NO
#define ELEMENT_PROCESSING_SEND_DATA_MODE ELEMENT_PROCESSING_SEND_DATA



//冒泡排序宏定义,1为开启，0为关闭
#define bubble_sort_flag 0

/***关于外设的宏定义***/
//定义脉冲引脚
#define SPEEDL_PLUSE   CTIM0_P34
//定义方向引脚
#define SPEEDL_DIR     P35
//选择重定向到哪一个串口
#define uart_slect 0
//选择1为重定向到板载串口，选择0为lora或者esp8266串口

//选择使用哪个串口进行解析数据
#define UART_send_data UART_3
#define UART_Parse_instructions UART_4
/*****关于外设的宏定义*****/

/*****状态机相关宏定义*****/
//颠簸路段相关宏定义
#define BUMPY_UP_THRESHOLD 15//20//25
#define BUMPY_STABLE_THRESHOLD 10//15
#define BUMPY_DOWN_THRESHOLD -10


#define RAMP_UP_THRESHOLD 20//坡道上坡阈值
#define RAMP_STABLE_THRESHOLD 10//坡道稳定阈值
#define RAMP_DOWN_THRESHOLD -10//坡道下坡阈值
#define BUMPY_STOP_THRESHOLD -20//颠簸路段停止阈值
/*****状态机相关宏定义*****/


/*****异常处理相关宏定义*****/
//姿态异常处理宏定义
#define PITCH_POSTURE_EXCEPTION_MAXTHRESHOLD 55//后仰
#define PITCH_POSTURE_EXCEPTION_MINTHRESHOLD -55//前倾
#define ROLL_POSTURE_EXCEPTION_MAXTHRESHOLD 55
#define ROLL_POSTURE_EXCEPTION_MINTHRESHOLD -55

#define DETECTION_CAHNGE_ANGLE 1
#define DETECTION_CAHNGE_ANGLE_NO 0


/****异常处理相关宏定义*****/

/*****PID参数宏定义*****/
//角速度环积分限幅
#define RATE_INTEGRAL_LIMIT   500.0f//12000.0f//6500.0f  // 积分项最大累积量

// 速度环控制参数
#define SPEED_INTEGRAL_LIMIT   400.0f//200.0f//3000.0f  // 积分项最大累积量
#define SPEED_OUTPUT_LIMIT_MAX 1000.0f   // 输出上限
#define SPEED_OUTPUT_LIMIT_MIN  600.0f//640.0f//600.0f//640.0f//620.0f//600.0f//620.0f//600.0f//620.0f//600.0f     // 输出下限
#define RATE_OUTPUT_LIMIT_MAX  500.0f//200.0f//500.0f//200.0f//500.0f//200.0f//400.0f//200.0f//250.0f// 角速度环输出上限
#define RATE_OUTPUT_LIMIT_MIN -500.0f//-200.0f//-500.0f//-200.0f//-500.0f//-200.0f//-400.0f//-200.0f//-250.0f// 角速度环输出下限

#define CCD_OUTPUT_LIMIT_MAX 200.0f//200.0f   // CCD角度环输出上限
#define CCD_OUTPUT_LIMIT_MIN -200.0f//-200.0f     // CCD角度环输出下限

/*****PID参数宏定义*****/

// 中断时间宏定义
#define TIME3_MS  10
#define TIME1_MS  10

//换点距离阈值
#define distance_threshold 0.8//0.5//2//0.5 //距离阈值，单位m

/*****全局变量声明*****/
//控制全局变量声明处
extern uint8 start_flag;
//传感器相关全局变量
extern uint8 g_ctimer_read_finish_flag;
extern uint8 g_ccd_process_flag;//CCD数据处理标志位
//丢失点位标志位以及变量
extern double anomaly_detection_angle;
extern uint8 anomaly_detection_flag;
extern uint8 anomaly_change_point_num;//丢点次数

//数据全局变量声明处
extern char take_point_num;
extern double start_angle;
extern int32 encoder;
extern uint32 heartbeat_time;
extern uint8 element_processing_state; //元素处理状态

extern void (*tim0_irq_handler)(void);
extern void (*tim1_irq_handler)(void);
extern void (*tim2_irq_handler)(void);
extern void (*tim3_irq_handler)(void);
extern void (*tim4_irq_handler)(void);

#endif 