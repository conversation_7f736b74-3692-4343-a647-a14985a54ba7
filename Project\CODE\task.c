#include "task.h"

extern uint32 debug_time;

void normal_function(void);
void grass_function(uint8 *now_task_node);
void bumpy_function(uint8 *now_task_node);
void ramp_function(uint8 *now_task_node);
void arch_function(uint8 *now_task_node);
void barricade_function(uint8 *now_task_node);
void Complete_function(void);
//状态机节点
uint8 now_task_node = normal;
//进程锁
uint8 normal_task_flag = 0;
uint8 grass_task_flag = 0;
uint8 ramp_task_flag = 0;
uint8 bumpy_task_flag = 0;
uint8 arch_task_flag = 0;
uint8 barricade_task_flag = 0;
uint8 turn_task_flag = 0;
uint8 Complete_task_flag = 0;


//point_task point_task_array[7] = {
//    {normal_running, normal_function},
//    {grass_prepare, grass_function},
//    {ramp_prepare, ramp_function},
//    {bumpy_prepare, bumpy_function},
//    {arch_prepare, arch_function},
//    {barricade_prepare, barricade_function},
//    {Complete_running, Complete_function}
//};

// 任务函数



//正常路段

void normal_function(void)
{

    g_ccd_process_flag = 0;//清空ccd处理标志位,以防万一

    target_rate=pid_angle(target_angle,angles.yaw);
    speed_side_out=pid_rate(target_rate,actual_angular_speed);
    //speed_out=pid_speed(target_speed,((encoder)/1024.0)*1.0676);//这里要不要考虑一些精度转换的问题？
    speed_out=pid_speed(target_speed,ctimer_speed);//这里要不要考虑一些精度转换的问题？
		g_ctimer_read_finish_flag = 1; //设置标志位，表示计时器读取完成
    speed_set(1000,speed_out,speed_side_out);//要不要塞到上面的if中？
}
//草地
void grass_function(uint8 *now_task_node)
{
    static uint8 grass_fun_node = grass_prepare;
    static uint8 grass_running_time = 0;
    switch(grass_fun_node)
    {
        case grass_prepare:
            {
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                grass_fun_node = grass_running;
                remind_on();
            }
            break;
        case grass_running:
            {
                grass_running_time++;
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(grass_running_time > 50)
                grass_fun_node = grass_stop;
            }
            break;
        case grass_stop:
            {
                *now_task_node = normal;
                grass_task_flag = 1;
                remind_off();
            }
            break;
        default:
            break;
    }
}
//颠簸路段
void bumpy_function(uint8 *now_task_node)
{
    static uint8 bumpy_fun_node = bumpy_prepare;
		static uint32 bumpy_time;
		static uint8 bumpy_time_lock=0;
    posture_exception_handling();
		
		if(bumpy_time_lock==0)
		{
			bumpy_time=heartbeat_time;
			bumpy_time_lock=1;
		}
		if(heartbeat_time-bumpy_time>1500)
		{
			element_processing_state = bumpy_stop;
			now_task_node = normal;
			bumpy_task_flag = 1;
		}
    switch(bumpy_fun_node)
    {
        case bumpy_prepare:
            {   

               element_processing_state = bumpy_prepare;

               #if (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_0 )
               speed_out = 1000;
               speed_side_out = 0; 
               speed_set(1000, speed_out, speed_side_out);
               if(angles.pitch > BUMPY_UP_THRESHOLD)
               {
                   bumpy_fun_node = bumpy_up;
               }
               //remind_once();
                #elif (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_TEST_1)
                //speed_out = 1000;
                //speed_out=pid_speed(0.75,ctimer_speed);
								speed_out=pid_speed(2.5,ctimer_speed);
								target_rate=pid_angle(target_angle,angles.yaw);
								speed_side_out=pid_rate(target_rate,actual_angular_speed);
                speed_set(1000, speed_out, speed_side_out);
							 
                if(angles.pitch > BUMPY_UP_THRESHOLD)
                {
                    bumpy_fun_node = bumpy_up;
                }
                //remind_once();
                #endif
            }
            break;
        case bumpy_up:
            {

                element_processing_state = bumpy_up; 

                #if (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_0 )
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(500, speed_out, speed_side_out);
                if(angles.pitch < BUMPY_STABLE_THRESHOLD)
                {
                    bumpy_fun_node = bumpy_stable;
                }
                //remind_once();
                //remind_once();
                #elif (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_TEST_1)
                //speed_out = 1000;
								//speed_out=pid_speed(target_speed,ctimer_speed);
								speed_out=0;
                //speed_side_out = pid_rate(target_rate,actual_angular_speed);
								speed_side_out=0;
                speed_set(500, speed_out, speed_side_out); 
                if(angles.pitch < BUMPY_STABLE_THRESHOLD)
                {
                    bumpy_fun_node = bumpy_stable;
                }
                #endif
            }
            break;
        case bumpy_stable:
            {

                element_processing_state = bumpy_stable;

                #if (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_0 )
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < BUMPY_DOWN_THRESHOLD)
                {
                    bumpy_fun_node = bumpy_stop;
                }
                //remind_once();
                //remind_once();
                //remind_once();
                #elif (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_TEST_1)
                //speed_out = 1000;
								//speed_out=pid_speed(target_speed,ctimer_speed);
								speed_out=pid_speed(0.5,ctimer_speed);
								target_rate=pid_angle(target_angle,angles.yaw);
                speed_side_out = pid_rate(target_rate,actual_angular_speed);
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < BUMPY_DOWN_THRESHOLD)
                {
                    bumpy_fun_node = bumpy_stop;
                }
                #endif
            }
            break;
        case bumpy_stop:
        {
            element_processing_state = bumpy_stop;
            *now_task_node = normal;
            bumpy_task_flag = 1;
            //remind_once();
            //remind_once();
            //remind_once();
            //remind_once();
        }
            break;
        default:
            break;
    }
}
//坡道
void ramp_function(uint8 *now_task_node)
{
    static uint8 ramp_fun_node = ramp_prepare;
		static uint32 ramp_time;
		static uint8 ramp_time_lock=0;
		
	
		if(ramp_time_lock==0)
		{
			ramp_time=heartbeat_time;
			ramp_time_lock=1;
		}
		if(heartbeat_time-ramp_time>2000)
		{
			element_processing_state = ramp_stop;
			now_task_node = normal;
			ramp_task_flag = 1;
			remind_off();
		}
	
		
    posture_exception_handling();
    switch(ramp_fun_node)
    {
        case ramp_prepare:
            {

                element_processing_state = ramp_prepare;
                #if (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_0 )
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch > RAMP_UP_THRESHOLD)
                {
                    ramp_fun_node = ramp_up;
                    //remind_on();
                }
                #elif (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_TEST_1)
                //speed_out = 1000;
								speed_out=pid_speed(0.75,ctimer_speed);
								target_rate=pid_angle(target_angle,angles.yaw);
                speed_side_out = pid_rate(target_rate,actual_angular_speed);
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch > RAMP_UP_THRESHOLD)
                {
                    ramp_fun_node = ramp_up;
                    //remind_on();
                }
                #endif
            }
            break;
        case ramp_up:
            {

                element_processing_state = ramp_up;

                #if (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_0 )
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < RAMP_STABLE_THRESHOLD)
                {
                    ramp_fun_node = ramp_stable;
                }
                #elif (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_TEST_1)
                //speed_out = 1000;
								speed_out=pid_speed(target_speed,ctimer_speed);
								target_rate=pid_angle(target_angle,angles.yaw);
                speed_side_out = pid_rate(target_rate,actual_angular_speed);
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < RAMP_STABLE_THRESHOLD)
                {
                    ramp_fun_node = ramp_stable;
                }
                #endif
            }
            break;
        case ramp_stable:
            {

                element_processing_state = ramp_stable;
                #if (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_0 )
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < RAMP_DOWN_THRESHOLD)
                {
                    ramp_fun_node = ramp_stop;
                }
                #elif (ELEMENT_PROCESSING_TASK_MODE == ELEMENT_PROCESSING_TASK_MODE_TEST_1)
                speed_out = 1000;
								target_rate=pid_angle(target_angle,angles.yaw);
                speed_side_out = pid_rate(target_rate,actual_angular_speed);
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < RAMP_DOWN_THRESHOLD)
                {
                    ramp_fun_node = ramp_stop;
                }
                #endif
            }
            break;
        case ramp_stop:
            {
                element_processing_state = ramp_stop;
                *now_task_node = normal;
                ramp_task_flag = 1;
                remind_off();
            }
            break;
        default:
            break;
    }
}
//桥洞
//void arch_function(uint8 *now_task_node)
//{
//    static uint8 arch_fun_node = arch_prepare;
//    switch(arch_fun_node)
//    {
//        case arch_prepare:
//            {
//                element_processing_state = arch_prepare;
//                g_ccd_process_flag = 1; //开启CCD数据处理
//            }
//            break;
//        case arch_enter:
//            {
//                element_processing_state = arch_enter;
//                speed_out = 1000;
//                speed_side_out = 0;
//                speed_set(1000, speed_out, speed_side_out);
//                if(angles.pitch < RAMP_STABLE_THRESHOLD)
//                {
//                    arch_fun_node = arch_stable;
//                }
//            }
//            break;
//        case arch_stable:
//            {
//                element_processing_state = arch_stable;
//                speed_out = 1000;
//                speed_side_out = 0;
//                speed_set(1000, speed_out, speed_side_out);
//                if(angles.pitch < RAMP_DOWN_THRESHOLD)
//                {
//                    arch_fun_node = arch_stop;
//                }
//            }
//            break;
//        case arch_stop:
//            {
//                element_processing_state = arch_stop;
//                *now_task_node = normal;
//                arch_task_flag = 1;
//                remind_off();
//            }
//            break;
//        default:
//            break;
//    }
//}   

///////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////
//注意注意，哪个状态不用ccd的时候要关掉标志位，也就是g_ccd_process_flag=0//
///////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////




void arch_function(uint8 *now_task_node)
{
    static uint8 arch_fun_node = arch_prepare;
		static uint32 arch_time;
		static uint8 arch_time_lock=0;
		
		if(arch_time_lock==0)
		{
			arch_time=heartbeat_time;
			arch_time_lock=1;
		}
		if(heartbeat_time-arch_time>4000)
		{
			g_ccd_process_flag = 0; //关闭CCD数据处理
			element_processing_state = arch_stop;
			now_task_node = normal;
			arch_task_flag = 1;
			remind_off();
		}
	
	
	
    switch(arch_fun_node)
    {
        case arch_prepare:
            {
                element_processing_state = arch_prepare;
                g_ccd_process_flag = 1; //开启CCD数据处理
                target_rate=pid_angle(target_angle,angles.yaw);
                speed_side_out=pid_rate(target_rate,actual_angular_speed)+pid_ccd(0,line_position_error);
                speed_out=pid_speed(target_speed,ctimer_speed);//这里要不要考虑一些精度转换的问题？
                g_ctimer_read_finish_flag = 1; //设置标志位，表示计时器读取完成
                speed_set(1000,speed_out,speed_side_out);
            }
            break;
        case arch_enter:
            {
                element_processing_state = arch_enter;
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < RAMP_STABLE_THRESHOLD)
                {
                    arch_fun_node = arch_stable;
                }
            }
            break;
        case arch_stable:
            {
                element_processing_state = arch_stable;
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(angles.pitch < RAMP_DOWN_THRESHOLD)
                {
                    arch_fun_node = arch_stop;
                }
            }
            break;
        case arch_stop:
            {
                g_ccd_process_flag = 0; //关闭CCD数据处理
                element_processing_state = arch_stop;
                *now_task_node = normal;
                arch_task_flag = 1;
                remind_off();
            }
            break;
        default:
            break;
    }
}   



//路障
void barricade_function(uint8 *now_task_node)
{
    static uint8 barricade_fun_node = barricade_prepare;
    static uint32 nowtime=0;
    static uint32 lasttime=0;
    switch(barricade_fun_node)
    {
        case barricade_prepare:
            {
                uint8 flag=0;
                nowtime=heartbeat_time;
                if(flag ==0)
                {lasttime=nowtime;flag=1;}
                target_rate=pid_angle(target_angle,angles.yaw);
                speed_side_out=pid_rate(target_rate,actual_angular_speed)+pid_ccd(0,line_position_error);
                speed_out=pid_speed(target_speed,ctimer_speed);//这里要不要考虑一些精度转换的问题？
                g_ctimer_read_finish_flag = 1; //设置标志位，表示计时器读取完成
                speed_set(1000,speed_out,speed_side_out);
                if(heartbeat_time-lasttime>1000)
                {
                    lasttime=heartbeat_time;
                    barricade_fun_node = barricade_enter;
                }
            }
             break;
        case barricade_enter:
            {   
                uint8 flag=0;
                nowtime=heartbeat_time;
                if(flag ==0)
                {lasttime=nowtime;flag=1;}
                speed_out = 1000;
                speed_side_out = 0;
                speed_set(1000, speed_out, speed_side_out);
                if(heartbeat_time-lasttime>500)
                {
                    lasttime=heartbeat_time;
                    barricade_fun_node = barricade_stable;
                }
            }	
            break;
        case barricade_stable:
            {
                barricade_fun_node = barricade_stop;		
            }
            break;
        case barricade_stop:
            {
                *now_task_node = normal;
                barricade_task_flag = 1;
                remind_off();
            }
            break;
        default:
            break;
    }
}
//void turn_function(uint8 *now_task_node)
//{
//		
//	static uint8 turn_fun_node = turn_prepare;
//    static uint32 turn_time=0;
//	static uint8 turn_time_lock=0;
//    static float turn_speed=0;
//    static float angle_error_temp = 0.0;
//    switch(turn_fun_node)
//    {
//        case turn_prepare:
//            {
//                element_processing_state = turn_fun_node;
//                
//                if(ctimer_speed>1.5)//要不要判断一下目标角度
//                {
//                    if(turn_time_lock==0)
//                    {
//                        turn_time=heartbeat_time;
//                        turn_time_lock=1;
//                        debug_time=turn_time;
//                    }
//                   //target_speed=0.75;
//                    pwm_duty(bottom_left_motor, 0);
//                    pwm_duty(bottom_right_motor, 0);
//                    if(ctimer_speed<1.0||((heartbeat_time-turn_time)>50))
//                   {						
//                    turn_fun_node = turn_running;
//                    }
//                }
//                else 
//                {
//										turn_fun_node=turn_stop;
//                }
//            }
//            break;
//        case turn_running:
//            {
//								element_processing_state = turn_fun_node;
//							  
//                turn_speed=0.75f;
//                angle_error_temp = target_angle - angles.yaw;
//                if(angle_error_temp>200)
//                angle_error_temp-=360;
//                if(angle_error_temp<-200)
//                angle_error_temp+=360;
//                if(angle_error_temp<10||angle_error_temp>-10)
//                {
//                    turn_fun_node = turn_stop;
//                }
//								if(angle_error_temp>150||angle_error_temp<-150)
//								{
//									if(angle_error_temp>150)
//									{
//										pwm_duty(bottom_left_motor, 0);
//										pwm_duty(bottom_right_motor, 0);
//										pwm_duty(tail_left_motor, 1000);
//										pwm_duty(tail_right_motor, 600);
//									}
//									if(angle_error_temp<-150)
//									{
//										pwm_duty(bottom_left_motor, 0);
//										pwm_duty(bottom_right_motor, 0);
//										pwm_duty(tail_left_motor, 600);
//										pwm_duty(tail_right_motor, 1000);
//									}
//								}
//								else
//								{
//									target_rate=pid_angle(target_angle,angles.yaw);
//									speed_side_out=pid_rate(target_rate,actual_angular_speed);
//									speed_out=pid_speed(turn_speed,ctimer_speed);//这里要不要考虑一些精度转换的问题？
//									g_ctimer_read_finish_flag = 1; //设置标志位，表示计时器读取完成
//									speed_set(1000,speed_out,speed_side_out);
//								}
//            }
//            break;
//        case turn_stop:
//            {
//								
//								element_processing_state = turn_fun_node;
//                *now_task_node = normal;
//                turn_task_flag = 1;
//                remind_off();
//            }
//            break;
//        default:
//            break;
//    }
//}

void turn_function(uint8 *now_task_node)
{
		
	static uint8 turn_fun_node = turn_prepare;
    static uint32 turn_time=0;
	static uint8 turn_time_lock=0;
    static float turn_speed=0;
    static float angle_error_temp = 0.0;
    switch(turn_fun_node)
    {
        case turn_prepare:
            {
                element_processing_state = turn_fun_node;
                
                if(ctimer_speed>1.5)//要不要判断一下目标角度
                {
                    if(turn_time_lock==0)
                    {
                        turn_time=heartbeat_time;
                        turn_time_lock=1;
                        debug_time=turn_time;
                    }
                   //target_speed=0.75;
                    pwm_duty(bottom_left_motor, 0);
                    pwm_duty(bottom_right_motor, 0);
										//pwm_duty(tail_left_motor, 0);
										//pwm_duty(tail_right_motor, 0);
										target_rate=pid_angle(target_angle,angles.yaw);
										speed_side_out=pid_rate(target_rate,actual_angular_speed);
										speed_out=pid_speed(0.5,ctimer_speed);//这里要不要考虑一些精度转换的问题？
										g_ctimer_read_finish_flag = 1; //设置标志位，表示计时器读取完成
										speed_set(0,speed_out,speed_side_out);
										
                    //if(ctimer_speed<1.0||((heartbeat_time-turn_time)>50))
										if(ctimer_speed<1.0||((heartbeat_time-turn_time)>150))
                   {						
                    turn_fun_node = turn_running;
                    }
                }
                else 
                {
										//turn_fun_node=turn_stop;
										turn_fun_node=turn_running;
                }
            }
            break;
        case turn_running:
            {
								element_processing_state = turn_fun_node;
							  
                turn_speed=0.75f;
                angle_error_temp = target_angle - angles.yaw;
                if(angle_error_temp>200)
                angle_error_temp-=360;
                if(angle_error_temp<-200)
                angle_error_temp+=360;
                if(angle_error_temp<10||angle_error_temp>-10)
                {
                    turn_fun_node = turn_stop;
                }
								if(angle_error_temp>150||angle_error_temp<-150)
								{
									if(angle_error_temp>150)
									{
										pwm_duty(bottom_left_motor, 0);
										pwm_duty(bottom_right_motor, 0);
										pwm_duty(tail_left_motor, 1000);
										pwm_duty(tail_right_motor, 600);
									}
									if(angle_error_temp<-150)
									{
										pwm_duty(bottom_left_motor, 0);
										pwm_duty(bottom_right_motor, 0);
										pwm_duty(tail_left_motor, 600);
										pwm_duty(tail_right_motor, 1000);
									}
								}
								else
								{
									target_rate=pid_angle(target_angle,angles.yaw);
									speed_side_out=pid_rate(target_rate,actual_angular_speed);
									speed_out=pid_speed(turn_speed,ctimer_speed);//这里要不要考虑一些精度转换的问题？
									g_ctimer_read_finish_flag = 1; //设置标志位，表示计时器读取完成
									speed_set(1000,speed_out,speed_side_out);
								}
            }
            break;
        case turn_stop:
            {
								
								element_processing_state = turn_fun_node;
                *now_task_node = normal;
                turn_task_flag = 1;
                remind_off();
            }
            break;
        default:
            break;
    }
}










//完成
void Complete_function(void)
{
    start_flag=0;
    speed_set(0,0,0);
		speed_set_zero();
    remind_on();
}





//状态机函数
void point_task_function(uint8 now_task_node)
{
    switch (now_task_node)
    {
    case normal:
        normal_function();
        break;
    case grass:
        grass_function(&now_task_node);
        break;
    case ramp://坡道
        ramp_function(&now_task_node);
        break;
    case bumpy://颠簸
		bumpy_function(&now_task_node);
        break;
    case arch://桥洞
        arch_function(&now_task_node);
        break;
    case barricade://路障
        barricade_function(&now_task_node);
        break;
    case turn://减速转弯
        turn_function(&now_task_node);
        break;
    case Complete:
        Complete_function();
        break;
    default:
        break;
    }
    
}


