#ifndef TASK_H
#define TASK_H
#include "headfile.h"
#include "common.h"

#include "config.h"
#include "motor.h"
#include "beep.h"
#include "quaternion_pose_calculating.h"
#include "pid.h"
#include "point_processing.h"
#include "exception_handling.h"
#include "stdio.h"
#include "my_atan2.h"
#include "stdio.h"
#include <stdlib.h>
#include <math.h>


typedef struct 
{
    uint8 state_machine_node;
    void (*function)(void);
}point_task;

//正常路段
typedef enum
{
    normal_running,
}normal_task_enum;

//草地
typedef enum
{
    grass_prepare,
    grass_running,
    grass_stop,
}grass_task_enum;

//坡道
typedef enum
{
    ramp_prepare,
    ramp_up,
    ramp_stable,
    ramp_down,
    ramp_stop,
}ramp_task_enum;

//颠簸路段
typedef enum
{
    bumpy_prepare,
    bumpy_up,
    bumpy_stable,
    bumpy_stop,
}bumpy_task_enum;

//桥洞
typedef enum
{
    arch_prepare,
    arch_enter,
    arch_stable,
    arch_stop,
}arch_task_enum;

//路障
typedef enum
{
    barricade_prepare,
    barricade_enter,
    barricade_stable,
    barricade_stop,
}barricade_task_enum;

//减速转弯
typedef enum
{
	turn_prepare,
    turn_running,
    turn_stop,
}
turn_task_enum;

//完成
typedef enum
{
    Complete_running,
}Complete_task_enum;

extern uint8 normal_task_flag;
extern uint8 grass_task_flag;
extern uint8 ramp_task_flag;
extern uint8 bumpy_task_flag;
extern uint8 arch_task_flag;
extern uint8 barricade_task_flag;
extern uint8 turn_task_flag;
extern uint8 Complete_task_flag;
extern uint8 now_task_node;

//extern point_task point_task_array[7];

void normal_function(void);
void grass_function(uint8 *now_task_node);
void ramp_function(uint8 *now_task_node);
void bumpy_function(uint8* now_task_node);
void arch_function(uint8 *now_task_node);
void barricade_function(uint8 *now_task_node);
void turn_function(uint8 *now_task_node);
void Complete_function(void);
void point_task_function(uint8 now_task_node);

#endif